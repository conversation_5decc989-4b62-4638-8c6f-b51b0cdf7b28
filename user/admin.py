from django.contrib import admin
from .models import User, MetaData, AdminActionLog, UserActionLog

# Register your models here.
@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('username', 'name', 'email', 'role', 'is_active', 'is_staff', 'last_login')
    list_filter = ('role', 'is_active', 'is_staff', 'is_superuser')
    search_fields = ('username', 'name', 'email', 'phone')
    readonly_fields = ('last_login', 'date_joined', 'last_activity')
    fieldsets = (
        ('Basic Information', {
            'fields': ('username', 'password', 'name', 'email', 'phone', 'photo')
        }),
        ('Personal Information', {
            'fields': ('dob', 'gender', 'address')
        }),
        ('Employee Information', {
            'fields': ('emp_id', 'nid', 'role', 'salary', 'join_date')
        }),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'is_manager', 'is_employee', 'groups', 'user_permissions')
        }),
        ('Important Dates', {
            'fields': ('last_login', 'date_joined', 'last_activity', 'create_day')
        }),
        ('Additional Settings', {
            'fields': ('email_verified', 'receive_email_notifications', 'receive_system_notifications')
        }),
    )

    # Protected usernames that cannot be deleted
    PROTECTED_USERNAMES = ['developer', 'owner']

    def has_delete_permission(self, request, obj=None):
        # If no object is specified, allow access to the delete page
        if obj is None:
            return True

        # Check if the username is in the protected list
        if obj.username in self.PROTECTED_USERNAMES:
            return False

        return True

# Register the AdminActionLog model with custom admin
@admin.register(AdminActionLog)
class AdminActionLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action_type', 'target_user', 'action_time', 'ip_address')
    list_filter = ('action_type', 'action_time', 'user')
    search_fields = ('user__username', 'target_user__username', 'description', 'ip_address')
    readonly_fields = ('user', 'action_type', 'action_time', 'ip_address', 'target_user', 'description')

    def has_add_permission(self, request):
        # Prevent manual creation of log entries
        return False

    def has_change_permission(self, request, obj=None):
        # Prevent editing log entries
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete log entries
        return request.user.is_superuser


# Register the UserActionLog model with custom admin
@admin.register(UserActionLog)
class UserActionLogAdmin(admin.ModelAdmin):
    list_display = ('user', 'action_type', 'module', 'status', 'action_time', 'target_description', 'ip_address')
    list_filter = ('action_type', 'module', 'status', 'action_time', 'user')
    search_fields = ('user__username', 'user__name', 'description', 'target_description', 'ip_address')
    readonly_fields = ('user', 'action_type', 'module', 'action_time', 'status', 'target_model',
                      'target_id', 'target_description', 'before_values', 'after_values',
                      'ip_address', 'user_agent', 'description', 'additional_data', 'financial_impact')

    date_hierarchy = 'action_time'
    ordering = ['-action_time']

    def has_add_permission(self, request):
        # Prevent manual creation of log entries
        return False

    def has_change_permission(self, request, obj=None):
        # Prevent editing log entries
        return False

    def has_delete_permission(self, request, obj=None):
        # Only superusers can delete log entries (for maintenance purposes)
        return request.user.is_superuser

    def get_queryset(self, request):
        # Optimize queries by selecting related user
        return super().get_queryset(request).select_related('user')

admin.site.register(MetaData)