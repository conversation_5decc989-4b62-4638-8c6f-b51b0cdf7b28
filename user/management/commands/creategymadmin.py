from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group
from user.models import User


class Command(BaseCommand):
    help = 'Creates a superuser with the admin role for the gym management system'

    def add_arguments(self, parser):
        parser.add_argument('--username', type=str, help='Username for the superuser')
        parser.add_argument('--email', type=str, help='Email for the superuser')
        parser.add_argument('--password', type=str, help='Password for the superuser')
        parser.add_argument('--name', type=str, help='Full name for the superuser')

    def handle(self, *args, **options):
        username = options.get('username')
        email = options.get('email')
        password = options.get('password')
        name = options.get('name')

        # If any of the required fields are not provided, prompt for them
        if not username:
            username = input('Username: ')
        if not email:
            email = input('Email: ')
        if not password:
            password = input('Password: ')
        if not name:
            name = input('Full name: ')

        # Check if the user already exists
        if User.objects.filter(username=username).exists():
            self.stdout.write(self.style.ERROR(f'User with username "{username}" already exists'))
            return

        # Create the superuser
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password
        )

        # Set superuser attributes
        user.is_staff = True
        user.is_superuser = True
        user.role = 'admin'
        user.is_manager = True
        user.name = name
        user.save()

        # Add to admin group
        admin_group, _ = Group.objects.get_or_create(name='admin')
        user.groups.add(admin_group)

        self.stdout.write(self.style.SUCCESS(f'Successfully created superuser "{username}" with admin role'))
