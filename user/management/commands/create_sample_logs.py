from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from user.models import User, UserActionLog
import random


class Command(BaseCommand):
    help = 'Creates sample user action logs for testing the logging system'

    def add_arguments(self, parser):
        parser.add_argument('--count', type=int, default=50, help='Number of log entries to create')

    def handle(self, *args, **options):
        count = options['count']
        
        # Get all users
        users = list(User.objects.all())
        if not users:
            self.stdout.write(self.style.ERROR('No users found. Please create some users first.'))
            return

        # Sample action types and modules
        action_types = [
            'delete_sale', 'delete_payment', 'delete_paypervisit', 'delete_finance_transaction',
            'edit_sale', 'edit_payment', 'edit_member_balance', 'login', 'logout',
            'create_user', 'edit_user', 'delete_user', 'settings_change'
        ]
        
        modules = ['pos', 'payment', 'paypervisit', 'finance', 'user', 'member', 'settings']
        
        statuses = ['success', 'failed', 'partial']
        
        # Sample descriptions
        descriptions = [
            'Deleted sale transaction due to customer request',
            'Removed payment record for refund processing',
            'Edited member balance after payment verification',
            'User logged into the system',
            'User logged out of the system',
            'Created new user account for employee',
            'Updated user profile information',
            'Deleted inactive user account',
            'Modified system settings configuration',
            'Processed pay-per-visit transaction deletion',
            'Updated finance transaction details',
            'Corrected payment amount entry',
        ]
        
        # Sample IP addresses
        ip_addresses = [
            '*************', '*************', '*************', '*********',
            '***********', '************', '*********'
        ]

        created_count = 0
        
        for i in range(count):
            # Random time within the last 30 days
            random_time = timezone.now() - timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )
            
            action_type = random.choice(action_types)
            module = random.choice(modules)
            user = random.choice(users)
            status = random.choice(statuses)
            description = random.choice(descriptions)
            ip_address = random.choice(ip_addresses)
            
            # Create target information based on action type
            target_model = ''
            target_id = ''
            target_description = ''
            financial_impact = None
            
            if 'delete' in action_type:
                if 'sale' in action_type:
                    target_model = 'Sale'
                    target_id = f'TXN{random.randint(1000, 9999)}'
                    target_description = f'Sale transaction {target_id}'
                    financial_impact = random.randint(5000, 50000)  # KHR
                elif 'payment' in action_type:
                    target_model = 'Payment'
                    target_id = f'PAY{random.randint(1000, 9999)}'
                    target_description = f'Payment record {target_id}'
                    financial_impact = random.randint(10000, 100000)  # KHR
                elif 'finance' in action_type:
                    target_model = 'Transaction'
                    target_id = f'FIN{random.randint(1000, 9999)}'
                    target_description = f'Finance transaction {target_id}'
                    financial_impact = random.randint(5000, 200000)  # KHR
            elif 'edit' in action_type:
                if 'user' in action_type:
                    target_model = 'User'
                    target_id = str(random.choice(users).id)
                    target_description = f'User account {random.choice(users).username}'
                elif 'member' in action_type:
                    target_model = 'Member'
                    target_id = f'MEM{random.randint(100, 999)}'
                    target_description = f'Member balance for {target_id}'
                    financial_impact = random.randint(1000, 50000)  # KHR
            
            # Create the log entry
            try:
                log_entry = UserActionLog.objects.create(
                    user=user,
                    action_type=action_type,
                    module=module,
                    action_time=random_time,
                    status=status,
                    target_model=target_model,
                    target_id=target_id,
                    target_description=target_description,
                    ip_address=ip_address,
                    description=description,
                    financial_impact=financial_impact,
                    user_agent=f'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                )
                created_count += 1
                
                if created_count % 10 == 0:
                    self.stdout.write(f'Created {created_count} log entries...')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating log entry {i+1}: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {created_count} user action log entries')
        )
