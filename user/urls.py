from django.urls import path
from . import views

app_name = "user"

urlpatterns = [
    # Employee management
    path('', views.index, name='index'),
    path('add/', views.add_employee, name='add_employee'),
    path('register/', views.user_registration, name='register'),
    path('edit/<int:pk>', views.edit, name='edit'),
    path('edit-system-user/<int:pk>', views.edit_system_user, name='edit_system_user'),
    path('edit-admin-user/<int:pk>', views.edit_admin_user, name='edit_admin_user'),
    path('active/<int:pk>', views.active, name='active'),
    path('deactivate/<int:pk>', views.deactivate, name='deactivate'),
    path('delete/<int:pk>', views.delete, name='delete'),
    path('admin-users/', views.admin_users, name='admin_users'),

    # User profile and self-service
    path('profile/', views.profile, name='profile'),
    path('change-password/', views.change_password, name='change_password'),

    # Admin security features
    path('admin-action-logs/', views.admin_action_logs, name='admin_action_logs'),
    path('user-action-logs/', views.user_action_logs, name='user_action_logs'),

    # Removed features as per requirements
]
