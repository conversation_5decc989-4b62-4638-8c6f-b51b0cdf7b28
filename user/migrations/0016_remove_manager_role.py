# Generated manually

from django.db import migrations


def update_manager_users(apps, schema_editor):
    """
    Update existing users with 'manager' role to 'admin' role
    """
    User = apps.get_model('user', 'User')
    
    # Get all users with manager role
    manager_users = User.objects.filter(role='manager')
    
    # Update them to admin role
    for user in manager_users:
        user.role = 'admin'
        user.save()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0015_remove_user_management_features'),
    ]

    operations = [
        migrations.RunPython(update_manager_users),
    ]
