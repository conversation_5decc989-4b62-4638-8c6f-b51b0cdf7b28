# Generated by Django 5.0.2 on 2025-05-16 21:49

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0011_alter_user_create_day'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='email_verified',
            field=models.BooleanField(default=False, verbose_name='Email Verified'),
        ),
        migrations.AddField(
            model_name='user',
            name='last_activity',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Last Activity'),
        ),
        migrations.AddField(
            model_name='user',
            name='password_reset_expires',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Password Reset Expires'),
        ),
        migrations.AddField(
            model_name='user',
            name='password_reset_token',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Password Reset Token'),
        ),
        migrations.Add<PERSON>ield(
            model_name='user',
            name='receive_email_notifications',
            field=models.<PERSON><PERSON>an<PERSON>ield(default=True, verbose_name='Receive Email Notifications'),
        ),
        migrations.AddField(
            model_name='user',
            name='receive_system_notifications',
            field=models.BooleanField(default=True, verbose_name='Receive System Notifications'),
        ),
        migrations.AlterField(
            model_name='user',
            name='emp_id',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Employee ID'),
        ),
    ]
