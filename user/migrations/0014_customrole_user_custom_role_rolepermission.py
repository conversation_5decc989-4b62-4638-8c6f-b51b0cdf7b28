# Generated by Django 5.0.2 on 2025-05-16 22:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0013_activitylog'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True, verbose_name='Role Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_roles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Custom Role',
                'verbose_name_plural': 'Custom Roles',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='custom_role',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='user.customrole', verbose_name='Custom Role'),
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module', models.CharField(choices=[('dashboard', 'Dashboard'), ('employee', 'Employee Management'), ('member', 'Member Management'), ('package', 'Package Management'), ('payment', 'Payment Processing'), ('payroll', 'Payroll Management'), ('product', 'Product Management'), ('inventory', 'Inventory Management'), ('purchase', 'Purchase Management'), ('pos', 'Point of Sale'), ('paypervisit', 'Pay-per-visit'), ('bill', 'Bill Management'), ('report', 'Reports'), ('settings', 'Settings')], max_length=50, verbose_name='Module')),
                ('level', models.CharField(choices=[('none', 'No Access'), ('view', 'View Only'), ('edit', 'View and Edit'), ('full', 'Full Access')], default='none', max_length=10, verbose_name='Permission Level')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permissions', to='user.customrole')),
            ],
            options={
                'verbose_name': 'Role Permission',
                'verbose_name_plural': 'Role Permissions',
                'unique_together': {('role', 'module')},
            },
        ),
    ]
