from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0002_alter_metadata_funds_alter_user_due_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='metadata',
            name='auto_deactivate_out_of_stock',
            field=models.BooleanField(default=True, help_text='Automatically deactivate products when they go out of stock', verbose_name='Auto-deactivate Out of Stock Products'),
        ),
        migrations.AddField(
            model_name='metadata',
            name='default_items_per_page',
            field=models.IntegerField(default=10, help_text='Default number of items to show per page in product list', verbose_name='Default Items Per Page'),
        ),
    ]
