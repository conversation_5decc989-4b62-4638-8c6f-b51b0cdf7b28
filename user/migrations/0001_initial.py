# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.conf import settings
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import user.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.<PERSON>r<PERSON>ield(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone', models.CharField(blank=True, max_length=14, null=True, unique=True, verbose_name='Phone')),
                ('name', models.CharField(blank=True, max_length=50, null=True, verbose_name='Full Name')),
                ('dob', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('gender', models.CharField(blank=True, choices=[('male', 'Male'), ('female', 'Female')], max_length=50, null=True, verbose_name='Gender')),
                ('emp_id', models.CharField(blank=True, max_length=20, null=True, verbose_name='Employee ID')),
                ('nid', models.CharField(blank=True, max_length=20, null=True, verbose_name='NID')),
                ('is_manager', models.BooleanField(default=True, verbose_name='Manager')),
                ('is_employee', models.BooleanField(default=False, verbose_name='Employee')),
                ('salary', models.IntegerField(blank=True, null=True, verbose_name='Salary')),
                ('due', models.IntegerField(default=0, verbose_name='Due Salary')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('photo', models.CharField(blank=True, max_length=255, null=True, verbose_name='Photo')),
                ('role', models.CharField(blank=True, choices=[('admin', 'Admin'), ('cashier', 'Cashier'), ('coach', 'Coach'), ('cleaner', 'Cleaner'), ('security', 'Security Guard')], max_length=20, null=True, verbose_name='Role')),
                ('join_date', models.DateField(blank=True, null=True, verbose_name='Join Date')),
                ('create_day', models.DateTimeField(auto_now_add=True, verbose_name='Create Date')),
                ('last_activity', models.DateTimeField(blank=True, null=True, verbose_name='Last Activity')),
                ('status', models.BooleanField(default=True, verbose_name='Active Status')),
                ('email_verified', models.BooleanField(default=False, verbose_name='Email Verified')),
                ('password_reset_token', models.CharField(blank=True, max_length=100, null=True, verbose_name='Password Reset Token')),
                ('password_reset_expires', models.DateTimeField(blank=True, null=True, verbose_name='Password Reset Expires')),
                ('receive_email_notifications', models.BooleanField(default=True, verbose_name='Receive Email Notifications')),
                ('receive_system_notifications', models.BooleanField(default=True, verbose_name='Receive System Notifications')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
            managers=[
                ('objects', user.models.GymUserManager()),
            ],
        ),
        migrations.CreateModel(
            name='MetaData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lastChecked', models.DateField(blank=True, null=True, verbose_name='Last Checked')),
                ('funds', models.IntegerField(blank=True, default=0, null=True, verbose_name='Funds')),
                ('auto_deactivate_out_of_stock', models.BooleanField(default=True, help_text='Automatically deactivate products when they go out of stock', verbose_name='Auto-deactivate Out of Stock Products')),
                ('auto_reactivate_in_stock', models.BooleanField(default=True, help_text='Automatically reactivate products when they come back in stock', verbose_name='Auto-reactivate In Stock Products')),
                ('default_items_per_page', models.IntegerField(default=10, help_text='Default number of items to show per page in product list', verbose_name='Default Items Per Page')),
            ],
        ),
        migrations.CreateModel(
            name='AdminActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create_user', 'Create User'), ('delete_user', 'Delete User'), ('deactivate_user', 'Deactivate User'), ('activate_user', 'Activate User'), ('edit_user', 'Edit User'), ('login', 'Login'), ('logout', 'Logout'), ('password_change', 'Password Change'), ('settings_change', 'Settings Change'), ('other', 'Other Action')], max_length=50, verbose_name='Action Type')),
                ('action_time', models.DateTimeField(auto_now_add=True, verbose_name='Action Time')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('target_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='target_actions', to=settings.AUTH_USER_MODEL, verbose_name='Target User')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='admin_actions', to=settings.AUTH_USER_MODEL, verbose_name='Admin User')),
            ],
            options={
                'verbose_name': 'Admin Action Log',
                'verbose_name_plural': 'Admin Action Logs',
                'ordering': ['-action_time'],
            },
        ),
        migrations.CreateModel(
            name='UserActionLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('failed_login', 'Failed Login'), ('create_user', 'Create User'), ('create_employee', 'Create Employee'), ('create_systemuser', 'Create System User'), ('create_adminuser', 'Create Admin User'), ('edit_user', 'Edit User'), ('edit_employee', 'Edit Employee'), ('edit_systemuser', 'Edit System User'), ('edit_adminuser', 'Edit Admin User'), ('delete_user', 'Delete User'), ('activate_user', 'Activate User'), ('deactivate_user', 'Deactivate User'), ('password_change', 'Password Change'), ('create_member', 'Create Member'), ('edit_member', 'Edit Member'), ('delete_member', 'Delete Member'), ('activate_member', 'Activate Member'), ('deactivate_member', 'Deactivate Member'), ('create_package', 'Create Package'), ('edit_package', 'Edit Package'), ('delete_package', 'Delete Package'), ('create_payment', 'Create Payment'), ('edit_payment', 'Edit Payment'), ('delete_payment', 'Delete Payment'), ('create_product', 'Create Product'), ('edit_product', 'Edit Product'), ('delete_product', 'Delete Product'), ('create_category', 'Create Category'), ('edit_category', 'Edit Category'), ('delete_category', 'Delete Category'), ('create_supplier', 'Create Supplier'), ('edit_supplier', 'Edit Supplier'), ('delete_supplier', 'Delete Supplier'), ('create_purchase', 'Create Purchase'), ('edit_purchase', 'Edit Purchase'), ('delete_purchase', 'Delete Purchase'), ('create_sale', 'Create Sale'), ('edit_sale', 'Edit Sale'), ('delete_sale', 'Delete Sale'), ('create_paypervisit', 'Create Pay-per-visit'), ('edit_paypervisit', 'Edit Pay-per-visit'), ('delete_paypervisit', 'Delete Pay-per-visit'), ('create_transaction', 'Create Finance Transaction'), ('edit_transaction', 'Edit Finance Transaction'), ('delete_transaction', 'Delete Finance Transaction'), ('create_finance_transaction', 'Create Finance Transaction'), ('edit_finance_transaction', 'Edit Finance Transaction'), ('delete_finance_transaction', 'Delete Finance Transaction'), ('create_salary_payment', 'Create Salary Payment'), ('edit_salary_payment', 'Edit Salary Payment'), ('delete_salary_payment', 'Delete Salary Payment'), ('create_salarypayment', 'Create Salary Payment'), ('edit_salarypayment', 'Edit Salary Payment'), ('delete_salarypayment', 'Delete Salary Payment'), ('create_bill', 'Create Bill'), ('edit_bill', 'Edit Bill'), ('delete_bill', 'Delete Bill'), ('create_template', 'Create Template'), ('edit_template', 'Edit Template'), ('delete_template', 'Delete Template'), ('create_paymenttemplate', 'Create Payment Template'), ('edit_paymenttemplate', 'Edit Payment Template'), ('delete_paymenttemplate', 'Delete Payment Template'), ('change_permissions', 'Change Permissions'), ('change_role', 'Change Role'), ('export_data', 'Export Data'), ('generate_report', 'Generate Report'), ('settings_change', 'Settings Change'), ('template_change', 'Template Change'), ('bulk_delete', 'Bulk Delete'), ('bulk_edit', 'Bulk Edit'), ('clean_data', 'Clean Data'), ('other', 'Other Action')], max_length=50, verbose_name='Action Type')),
                ('module', models.CharField(choices=[('user', 'User Management'), ('member', 'Member Management'), ('payment', 'Payment Management'), ('paypervisit', 'Pay-per-visit'), ('product', 'Product Management'), ('pos', 'Point of Sale'), ('finance', 'Finance Management'), ('payroll', 'Payroll Management'), ('bill', 'Bill Management'), ('settings', 'Settings'), ('auth', 'Authentication'), ('system', 'System')], max_length=20, verbose_name='Module')),
                ('action_time', models.DateTimeField(auto_now_add=True, verbose_name='Action Time')),
                ('status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed'), ('partial', 'Partial Success')], default='success', max_length=10, verbose_name='Status')),
                ('target_model', models.CharField(blank=True, max_length=50, verbose_name='Target Model')),
                ('target_id', models.CharField(blank=True, max_length=50, verbose_name='Target ID')),
                ('target_description', models.TextField(blank=True, verbose_name='Target Description')),
                ('before_values', models.JSONField(blank=True, null=True, verbose_name='Before Values')),
                ('after_values', models.JSONField(blank=True, null=True, verbose_name='After Values')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP Address')),
                ('user_agent', models.TextField(blank=True, verbose_name='User Agent')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('additional_data', models.JSONField(blank=True, null=True, verbose_name='Additional Data')),
                ('financial_impact', models.DecimalField(blank=True, decimal_places=2, help_text='Amount in KHR', max_digits=15, null=True, verbose_name='Financial Impact')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='action_logs', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'User Action Log',
                'verbose_name_plural': 'User Action Logs',
                'ordering': ['-action_time'],
                'indexes': [models.Index(fields=['user', 'action_time'], name='user_userac_user_id_d64df7_idx'), models.Index(fields=['action_type', 'action_time'], name='user_userac_action__3ccd63_idx'), models.Index(fields=['module', 'action_time'], name='user_userac_module_afa3ec_idx'), models.Index(fields=['ip_address', 'action_time'], name='user_userac_ip_addr_87a7ab_idx')],
            },
        ),
    ]
