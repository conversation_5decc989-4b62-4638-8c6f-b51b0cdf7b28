# Generated manually

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0014_customrole_user_custom_role_rolepermission'),
    ]

    operations = [
        # Remove custom_role field from User model
        migrations.RemoveField(
            model_name='user',
            name='custom_role',
        ),
        
        # Delete RolePermission model
        migrations.DeleteModel(
            name='RolePermission',
        ),
        
        # Delete CustomRole model
        migrations.DeleteModel(
            name='CustomRole',
        ),
        
        # Delete ActivityLog model
        migrations.DeleteModel(
            name='ActivityLog',
        ),
    ]
