# Generated by Django 5.0.2 on 2025-05-28 21:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0022_useractionlog'),
    ]

    operations = [
        migrations.AlterField(
            model_name='useractionlog',
            name='action_type',
            field=models.CharField(choices=[('login', 'Login'), ('logout', 'Logout'), ('failed_login', 'Failed Login'), ('create_user', 'Create User'), ('create_employee', 'Create Employee'), ('create_systemuser', 'Create System User'), ('create_adminuser', 'Create Admin User'), ('edit_user', 'Edit User'), ('edit_employee', 'Edit Employee'), ('edit_systemuser', 'Edit System User'), ('edit_adminuser', 'Edit Admin User'), ('delete_user', 'Delete User'), ('activate_user', 'Activate User'), ('deactivate_user', 'Deactivate User'), ('password_change', 'Password Change'), ('create_member', 'Create Member'), ('edit_member', 'Edit Member'), ('delete_member', 'Delete Member'), ('activate_member', 'Activate Member'), ('deactivate_member', 'Deactivate Member'), ('create_package', 'Create Package'), ('edit_package', 'Edit Package'), ('delete_package', 'Delete Package'), ('create_payment', 'Create Payment'), ('edit_payment', 'Edit Payment'), ('delete_payment', 'Delete Payment'), ('create_product', 'Create Product'), ('edit_product', 'Edit Product'), ('delete_product', 'Delete Product'), ('create_category', 'Create Category'), ('edit_category', 'Edit Category'), ('delete_category', 'Delete Category'), ('create_supplier', 'Create Supplier'), ('edit_supplier', 'Edit Supplier'), ('delete_supplier', 'Delete Supplier'), ('create_purchase', 'Create Purchase'), ('edit_purchase', 'Edit Purchase'), ('delete_purchase', 'Delete Purchase'), ('create_sale', 'Create Sale'), ('edit_sale', 'Edit Sale'), ('delete_sale', 'Delete Sale'), ('create_paypervisit', 'Create Pay-per-visit'), ('edit_paypervisit', 'Edit Pay-per-visit'), ('delete_paypervisit', 'Delete Pay-per-visit'), ('create_transaction', 'Create Finance Transaction'), ('edit_transaction', 'Edit Finance Transaction'), ('delete_transaction', 'Delete Finance Transaction'), ('create_finance_transaction', 'Create Finance Transaction'), ('edit_finance_transaction', 'Edit Finance Transaction'), ('delete_finance_transaction', 'Delete Finance Transaction'), ('create_salary_payment', 'Create Salary Payment'), ('edit_salary_payment', 'Edit Salary Payment'), ('delete_salary_payment', 'Delete Salary Payment'), ('create_salarypayment', 'Create Salary Payment'), ('edit_salarypayment', 'Edit Salary Payment'), ('delete_salarypayment', 'Delete Salary Payment'), ('create_bill', 'Create Bill'), ('edit_bill', 'Edit Bill'), ('delete_bill', 'Delete Bill'), ('create_template', 'Create Template'), ('edit_template', 'Edit Template'), ('delete_template', 'Delete Template'), ('create_paymenttemplate', 'Create Payment Template'), ('edit_paymenttemplate', 'Edit Payment Template'), ('delete_paymenttemplate', 'Delete Payment Template'), ('change_permissions', 'Change Permissions'), ('change_role', 'Change Role'), ('export_data', 'Export Data'), ('generate_report', 'Generate Report'), ('settings_change', 'Settings Change'), ('template_change', 'Template Change'), ('bulk_delete', 'Bulk Delete'), ('bulk_edit', 'Bulk Edit'), ('clean_data', 'Clean Data'), ('other', 'Other Action')], max_length=50, verbose_name='Action Type'),
        ),
    ]
