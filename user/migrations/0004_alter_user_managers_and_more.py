# Generated by Django 5.0.2 on 2025-05-06 23:41

import user.models
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_metadata_auto_deactivate_out_of_stock_metadata_default_items_per_page'),
    ]

    operations = [
        migrations.AlterModelManagers(
            name='user',
            managers=[
                ('objects', user.models.GymUserManager()),
            ],
        ),
        migrations.AddField(
            model_name='metadata',
            name='auto_reactivate_in_stock',
            field=models.BooleanField(default=True, help_text='Automatically reactivate products when they come back in stock', verbose_name='Auto-reactivate In Stock Products'),
        ),
        migrations.AddField(
            model_name='metadata',
            name='notification_email',
            field=models.EmailField(blank=True, help_text='Email address to send notifications to', max_length=254, null=True, verbose_name='Notification Email'),
        ),
        migrations.AddField(
            model_name='metadata',
            name='send_email_notifications',
            field=models.BooleanField(default=False, help_text='Send email notifications when products are auto-deactivated', verbose_name='Send Email Notifications'),
        ),
        migrations.AddField(
            model_name='user',
            name='photo',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Photo'),
        ),
        migrations.AddField(
            model_name='user',
            name='role',
            field=models.CharField(blank=True, choices=[('admin', 'Admin'), ('manager', 'Manager'), ('cashier', 'Cashier'), ('coach', 'Coach'), ('cleaner', 'Cleaner'), ('security', 'Security Guard')], max_length=20, null=True, verbose_name='Role'),
        ),
    ]
