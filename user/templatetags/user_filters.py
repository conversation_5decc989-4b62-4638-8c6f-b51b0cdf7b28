from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Get an item from a dictionary using a key.
    Usage: {{ dictionary|get_item:key }}
    """
    if not dictionary:
        return None

    # Convert key to int if it's a string representation of an integer
    if isinstance(key, str) and key.isdigit():
        key = int(key)

    return dictionary.get(key)

@register.filter
def split_address_schedule(combined_text, part):
    """
    Split the combined address and work schedule text.
    Usage: {{ combined_text|split_address_schedule:"address" }} or {{ combined_text|split_address_schedule:"schedule" }}
    """
    if not combined_text:
        return ""

    # Check if the text contains the expected format
    if "Address:" in combined_text and "Work Schedule:" in combined_text:
        parts = combined_text.split("Work Schedule:")
        address = parts[0].replace("Address:", "").strip()
        schedule = parts[1].strip() if len(parts) > 1 else ""

        if part == "address":
            return address
        elif part == "schedule":
            return schedule

    # If the format is not as expected, return the whole text for address and empty for schedule
    if part == "address":
        return combined_text
    else:
        return ""
