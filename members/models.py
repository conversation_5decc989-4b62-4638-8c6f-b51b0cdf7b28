from django.db import models
from django.utils.translation import gettext_lazy as _

class Package(models.Model):
    package_id = models.CharField(_("Package ID"), max_length=10, unique=True)
    name = models.Char<PERSON>ield(_("Name"), max_length=100)
    duration = models.IntegerField(_("Duration (Months)"))
    price_khr = models.IntegerField(_("Price (KHR)"))
    price_usd = models.DecimalField(_("Price (USD)"), max_digits=10, decimal_places=2, null=True, blank=True)
    access_type = models.CharField(_("Access Type"), max_length=50, choices=[
        ('all_hours', 'All Hours'),
        ('peak_only', 'Peak Hours Only')
    ])
    description = models.TextField(_("Description"), null=True, blank=True)

    def __str__(self):
        return f"{self.name} - {self.duration} Months"

    class Meta:
        ordering = ['name', 'duration']


class Member(models.Model):
    GENDER_CHOICES = [
        ('male', 'Male'),
        ('female', 'Female'),
        ('other', 'Other'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('paid', 'Paid'),
        ('pending', 'Pending'),
        ('overdue', 'Overdue'),
    ]

    member_id = models.CharField(_("Member ID"), max_length=10, unique=True)
    name = models.CharField(_("Name"), max_length=100)
    photo = models.ImageField(_("Photo"), upload_to='members/', null=True, blank=True)
    gender = models.CharField(_("Gender"), max_length=10, choices=GENDER_CHOICES)
    dob = models.DateField(_("Date of Birth"), null=True, blank=True)
    contact = models.CharField(_("Contact"), max_length=20)
    telegram = models.CharField(_("Telegram/Messenger"), max_length=100, null=True, blank=True)
    address = models.TextField(_("Address"), null=True, blank=True)
    package = models.ForeignKey(Package, on_delete=models.SET_NULL, null=True, related_name='members')
    start_date = models.DateField(_("Start Date"))
    end_date = models.DateField(_("End Date"))
    payment_status = models.CharField(_("Payment Status"), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    discount = models.IntegerField(_("Discount Amount"), default=0)
    due_payment = models.IntegerField(_("Due Payment"), default=0)

    status = models.BooleanField(_("Active Status"), default=True)

    def __str__(self):
        return f"{self.member_id} - {self.name}"

    @property
    def days_remaining(self):
        """Calculate days remaining until membership expires"""
        from django.utils import timezone
        today = timezone.now().date()
        if self.end_date < today:
            # Return negative number of days expired
            return (self.end_date - today).days
        return (self.end_date - today).days

    @property
    def is_expiring_soon(self):
        """Check if membership is expiring within 30 days"""
        return 0 <= self.days_remaining <= 30

    @property
    def expiration_status(self):
        """Return the expiration status"""
        if self.days_remaining < 0:
            return "expired"
        elif self.days_remaining == 0:
            return "expires_today"  # Expires today
        elif self.days_remaining <= 7:
            return "critical"  # Less than a week
        elif self.days_remaining <= 30:
            return "warning"   # Less than a month
        else:
            return "good"      # More than a month

    @property
    def total_amount(self):
        """Calculate total amount after discount"""
        if not self.package:
            return 0
        return self.package.price_khr - self.discount

    class Meta:
        ordering = ['-start_date']
