# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('package_id', models.CharField(max_length=10, unique=True, verbose_name='Package ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('duration', models.IntegerField(verbose_name='Duration (Months)')),
                ('price_khr', models.IntegerField(verbose_name='Price (KHR)')),
                ('price_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Price (USD)')),
                ('access_type', models.CharField(choices=[('all_hours', 'All Hours'), ('peak_only', 'Peak Hours Only')], max_length=50, verbose_name='Access Type')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
            options={
                'ordering': ['name', 'duration'],
            },
        ),
        migrations.CreateModel(
            name='Member',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('member_id', models.CharField(max_length=10, unique=True, verbose_name='Member ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='members/', verbose_name='Photo')),
                ('gender', models.CharField(choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other')], max_length=10, verbose_name='Gender')),
                ('dob', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('contact', models.CharField(max_length=20, verbose_name='Contact')),
                ('telegram', models.CharField(blank=True, max_length=100, null=True, verbose_name='Telegram/Messenger')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('payment_status', models.CharField(choices=[('paid', 'Paid'), ('pending', 'Pending'), ('overdue', 'Overdue')], default='pending', max_length=20, verbose_name='Payment Status')),
                ('discount', models.IntegerField(default=0, verbose_name='Discount Amount')),
                ('due_payment', models.IntegerField(default=0, verbose_name='Due Payment')),
                ('status', models.BooleanField(default=True, verbose_name='Active Status')),
                ('package', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='members', to='members.package')),
            ],
            options={
                'ordering': ['-start_date'],
            },
        ),
    ]
