from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.cache import cache
from user.models import ROLE_CHOICES

class Settings(models.Model):
    """
    Centralized settings model for the entire application.
    This model follows the singleton pattern - only one instance should exist.
    """

    # ===== General Settings =====
    gym_name = models.CharField(_("Gym Name"), max_length=100, default="Legend Fitness")
    contact_email = models.EmailField(_("Contact Email"), blank=True, null=True)
    contact_phone = models.CharField(_("Contact Phone"), max_length=20, blank=True, null=True)
    address = models.TextField(_("Address"), blank=True, null=True)

    # ===== Product Settings =====
    auto_deactivate_out_of_stock = models.BooleanField(
        _("Auto-deactivate Out of Stock Products"),
        default=True,
        help_text=_("Automatically deactivate products when they go out of stock")
    )
    auto_reactivate_in_stock = models.<PERSON><PERSON>anField(
        _("Auto-reactivate In Stock Products"),
        default=True,
        help_text=_("Automatically reactivate products when they come back in stock")
    )
    default_items_per_page = models.IntegerField(
        _("Default Items Per Page"),
        default=10,
        help_text=_("Default number of items to show per page in lists")
    )

    # ===== Pay-per-visit Settings =====
    paypervisit_price_per_person = models.IntegerField(_("Pay-per-visit Price Per Person"), default=4000)
    paypervisit_quick_select_1 = models.IntegerField(_("Quick Selection 1 People Count"), default=2)
    paypervisit_quick_select_2 = models.IntegerField(_("Quick Selection 2 People Count"), default=5)
    paypervisit_quick_select_3 = models.IntegerField(_("Quick Selection 3 People Count"), default=10)
    paypervisit_custom_price_1 = models.IntegerField(_("Custom Price for Quick Selection 1"), default=8000)
    paypervisit_custom_price_2 = models.IntegerField(_("Custom Price for Quick Selection 2"), default=20000)
    paypervisit_custom_price_3 = models.IntegerField(_("Custom Price for Quick Selection 3"), default=40000)

    # ===== UI Settings =====
    notification_success_color = models.CharField(_("Success Notification Color"), max_length=20, default="#4CAF50")
    notification_error_color = models.CharField(_("Error Notification Color"), max_length=20, default="#F44336")
    notification_warning_color = models.CharField(_("Warning Notification Color"), max_length=20, default="#FF9800")
    notification_info_color = models.CharField(_("Info Notification Color"), max_length=20, default="#2196F3")
    notification_text_color = models.CharField(_("Notification Text Color"), max_length=20, default="#FFFFFF")

    # ===== Payment Settings =====
    default_currency = models.CharField(_("Default Currency"), max_length=10, default="KHR", choices=[
        ('KHR', _('Cambodian Riel (៛)')),
        ('USD', _('US Dollar ($)')),
    ])
    exchange_rate_usd_to_khr = models.IntegerField(
        _("Exchange Rate (USD to KHR)"),
        default=4000,
        help_text=_("Exchange rate for converting between USD and KHR (e.g., 4000 means 1 USD = 4,000 KHR)")
    )

    # ===== System Settings =====
    last_backup_date = models.DateTimeField(_("Last Backup Date"), null=True, blank=True)
    last_data_cleanup = models.DateTimeField(_("Last Data Cleanup"), null=True, blank=True)

    # ===== Metadata =====
    funds = models.IntegerField(_("Funds"), default=0, help_text=_("Current funds in the system"))
    last_checked = models.DateField(_("Last Checked"), null=True, blank=True)
    last_updated = models.DateTimeField(_("Last Updated"), auto_now=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)

    class Meta:
        verbose_name = _("Settings")
        verbose_name_plural = _("Settings")

    def save(self, *args, **kwargs):
        """Override save to ensure singleton pattern and clear cache"""
        self.pk = 1  # Always use ID 1
        super().save(*args, **kwargs)

        # Clear cache when settings are updated
        cache.delete('system_settings')

        # Also clear exchange rate cache if it exists
        cache.delete('exchange_rate_usd_to_khr')

    @classmethod
    def get_settings(cls):
        """Get the settings object, creating it if it doesn't exist"""
        # Try to get from cache first
        settings = cache.get('system_settings')
        if settings is None:
            # If not in cache, get from database
            settings, created = cls.objects.get_or_create(
                id=1,
                defaults={
                    'last_checked': timezone.now().date(),
                }
            )

            # Cache for 1 hour
            cache.set('system_settings', settings, 3600)

        return settings

    def __str__(self):
        return f"System Settings (Last updated: {self.last_updated})"


# Define module choices for permissions
MODULE_CHOICES = [
    ('dashboard', _('Dashboard')),
    ('member', _('Member Management')),
    ('payment', _('Payment Processing')),
    ('payroll', _('Payroll Management')),
    ('product', _('Product Management')),
    ('purchase', _('Purchase Management')),
    ('pos', _('Point of Sale')),
    ('paypervisit', _('Pay-per-visit')),
    ('bill', _('Bill Management')),
    ('finance', _('Finance Management')),
    ('financialreport', _('Financial Reports')),
    ('user', _('User Management')),
    ('settings', _('Settings')),
]

# Define permission levels
PERMISSION_LEVELS = [
    ('none', _('No Access')),
    ('view', _('View Only')),
    ('edit', _('View and Edit')),
    ('full', _('Full Access')),
]

class RolePermission(models.Model):
    """
    Model to store role-based permissions for different modules
    """
    role = models.CharField(_("Role"), max_length=20, choices=ROLE_CHOICES)
    module = models.CharField(_("Module"), max_length=50, choices=MODULE_CHOICES)
    permission_level = models.CharField(_("Permission Level"), max_length=10, choices=PERMISSION_LEVELS, default='none')

    class Meta:
        verbose_name = _("Role Permission")
        verbose_name_plural = _("Role Permissions")
        unique_together = ('role', 'module')

    def __str__(self):
        return f"{self.get_role_display()} - {self.get_module_display()} - {self.get_permission_level_display()}"

    @classmethod
    def get_role_permissions(cls, role):
        """Get all permissions for a specific role"""
        return cls.objects.filter(role=role)

    @classmethod
    def get_module_permission(cls, role, module):
        """Get permission level for a specific role and module"""
        try:
            permission = cls.objects.get(role=role, module=module)
            return permission.permission_level
        except cls.DoesNotExist:
            return 'none'

    @classmethod
    def has_permission(cls, role, module, required_level='view'):
        """
        Check if a role has the required permission level for a module

        Args:
            role (str): The role to check
            module (str): The module to check
            required_level (str): The minimum required permission level
                                 ('view', 'edit', or 'full')

        Returns:
            bool: True if the role has the required permission level, False otherwise
        """
        # Get the permission level for this role and module
        # Note: Removed admin bypass to respect granular permission settings
        permission_level = cls.get_module_permission(role, module)

        # If permission doesn't exist in database, create it with default values
        # But if it exists and is set to 'none', respect that setting
        if permission_level == 'none':
            # Check if the permission record actually exists in the database
            try:
                existing_perm = cls.objects.get(role=role, module=module)
                # If it exists and is set to 'none', respect that setting
                # Don't auto-restore to defaults
                pass
            except cls.DoesNotExist:
                # Only create default permissions if the record doesn't exist at all
                from settings.utils import get_default_permissions
                default_permissions = get_default_permissions()

                if role in default_permissions and module in default_permissions[role]:
                    default_level = default_permissions[role][module]
                    try:
                        cls.objects.create(role=role, module=module, permission_level=default_level)
                        permission_level = default_level
                    except Exception:
                        # If creation fails, keep the 'none' level
                        pass

        # Define permission hierarchy
        permission_hierarchy = {
            'none': 0,
            'view': 1,
            'edit': 2,
            'full': 3
        }

        # Get numeric values for comparison
        user_permission_value = permission_hierarchy.get(permission_level, 0)
        required_permission_value = permission_hierarchy.get(required_level, 0)

        # Check if the user's permission level is sufficient
        return user_permission_value >= required_permission_value
