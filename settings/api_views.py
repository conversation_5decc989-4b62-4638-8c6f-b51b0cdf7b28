import json
import logging
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from .cache_manager import PermissionCacheManager
from .utils import get_user_permissions
from core.redis_utils import RedisHealthChecker

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["GET"])
def check_permissions(request):
    """
    API endpoint to check current user permissions
    Used for fallback polling when WebSocket is not available
    """
    try:
        user = request.user
        permissions = PermissionCacheManager.get_user_permissions_cached(user)

        return JsonResponse({
            'success': True,
            'permissions': permissions,
            'user_role': user.role,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Error checking permissions for user {request.user.username}: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to check permissions'
        }, status=500)

@method_decorator(login_required, name='dispatch')
@method_decorator(csrf_exempt, name='dispatch')
class PermissionStatusView(View):
    """
    API view for permission status and updates
    """

    def get(self, request):
        """
        Get current permission status
        """
        try:
            user = request.user
            permissions = PermissionCacheManager.get_user_permissions_cached(user)

            return JsonResponse({
                'success': True,
                'permissions': permissions,
                'user_role': user.role,
                'user_id': user.id,
                'is_admin': user.role == 'admin',
                'timestamp': timezone.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Error getting permission status: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': 'Failed to get permission status'
            }, status=500)

    def post(self, request):
        """
        Request permission refresh
        """
        try:
            user = request.user

            # Clear cache for this user
            PermissionCacheManager.invalidate_user_cache(user.id)

            # Get fresh permissions
            permissions = PermissionCacheManager.get_user_permissions_cached(user)

            return JsonResponse({
                'success': True,
                'permissions': permissions,
                'message': 'Permissions refreshed successfully',
                'timestamp': timezone.now().isoformat()
            })

        except Exception as e:
            logger.error(f"Error refreshing permissions: {str(e)}")
            return JsonResponse({
                'success': False,
                'error': 'Failed to refresh permissions'
            }, status=500)

@login_required
@require_http_methods(["POST"])
def invalidate_permission_cache(request):
    """
    API endpoint to invalidate permission cache
    Admin only endpoint
    """
    try:
        if request.user.role != 'admin':
            return JsonResponse({
                'success': False,
                'error': 'Permission denied'
            }, status=403)

        data = json.loads(request.body)
        cache_type = data.get('type', 'all')

        if cache_type == 'user':
            user_id = data.get('user_id')
            if user_id:
                PermissionCacheManager.invalidate_user_cache(user_id)
                message = f'Cache invalidated for user {user_id}'
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'User ID required'
                }, status=400)

        elif cache_type == 'role':
            role = data.get('role')
            if role:
                PermissionCacheManager.invalidate_role_cache(role)
                message = f'Cache invalidated for role {role}'
            else:
                return JsonResponse({
                    'success': False,
                    'error': 'Role required'
                }, status=400)

        elif cache_type == 'all':
            PermissionCacheManager.invalidate_all_permission_caches()
            message = 'All permission caches invalidated'

        else:
            return JsonResponse({
                'success': False,
                'error': 'Invalid cache type'
            }, status=400)

        return JsonResponse({
            'success': True,
            'message': message
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'error': 'Invalid JSON'
        }, status=400)
    except Exception as e:
        logger.error(f"Error invalidating cache: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': 'Failed to invalidate cache'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def permission_health_check(request):
    """
    Health check endpoint for permission system
    """
    try:
        user = request.user

        # Test permission check
        test_permission = PermissionCacheManager.check_permission_cached(
            user.role, 'dashboard', 'view'
        )

        # Test cache functionality
        permissions = PermissionCacheManager.get_user_permissions_cached(user)

        return JsonResponse({
            'success': True,
            'status': 'healthy',
            'cache_working': True,
            'permission_check_working': True,
            'user_role': user.role,
            'test_permission': test_permission,
            'permission_count': len(permissions),
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Permission health check failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)

@login_required
@require_http_methods(["GET"])
def websocket_status(request):
    """
    Check WebSocket connection status
    """
    try:
        from channels.layers import get_channel_layer

        channel_layer = get_channel_layer()
        websocket_available = channel_layer is not None

        return JsonResponse({
            'success': True,
            'websocket_available': websocket_available,
            'channel_layer_configured': websocket_available,
            'fallback_polling': not websocket_available,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"WebSocket status check failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'websocket_available': False,
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        })

# Import timezone at the top level to avoid issues
from django.utils import timezone

@login_required
@require_http_methods(["GET"])
def redis_status(request):
    """
    Check Redis server status and performance
    """
    try:
        status = RedisHealthChecker.get_comprehensive_status()

        return JsonResponse({
            'success': True,
            'redis_status': status,
            'timestamp': timezone.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Redis status check failed: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=500)
