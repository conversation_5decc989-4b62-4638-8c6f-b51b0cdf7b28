# Generated by Django 5.0.2 on 2025-05-14 22:20

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('gym_name', models.Char<PERSON>ield(default='Legend Fitness', max_length=100, verbose_name='Gym Name')),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Contact Email')),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Contact Phone')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('auto_deactivate_out_of_stock', models.BooleanField(default=True, help_text='Automatically deactivate products when they go out of stock', verbose_name='Auto-deactivate Out of Stock Products')),
                ('auto_reactivate_in_stock', models.BooleanField(default=True, help_text='Automatically reactivate products when they come back in stock', verbose_name='Auto-reactivate In Stock Products')),
                ('default_items_per_page', models.IntegerField(default=10, help_text='Default number of items to show per page in lists', verbose_name='Default Items Per Page')),
                ('paypervisit_price_per_person', models.IntegerField(default=4000, verbose_name='Pay-per-visit Price Per Person')),
                ('paypervisit_quick_select_1', models.IntegerField(default=2, verbose_name='Quick Selection 1 People Count')),
                ('paypervisit_quick_select_2', models.IntegerField(default=5, verbose_name='Quick Selection 2 People Count')),
                ('paypervisit_quick_select_3', models.IntegerField(default=10, verbose_name='Quick Selection 3 People Count')),
                ('paypervisit_custom_price_1', models.IntegerField(default=8000, verbose_name='Custom Price for Quick Selection 1')),
                ('paypervisit_custom_price_2', models.IntegerField(default=20000, verbose_name='Custom Price for Quick Selection 2')),
                ('paypervisit_custom_price_3', models.IntegerField(default=40000, verbose_name='Custom Price for Quick Selection 3')),
                ('notification_success_color', models.CharField(default='#4CAF50', max_length=20, verbose_name='Success Notification Color')),
                ('notification_error_color', models.CharField(default='#F44336', max_length=20, verbose_name='Error Notification Color')),
                ('notification_warning_color', models.CharField(default='#FF9800', max_length=20, verbose_name='Warning Notification Color')),
                ('notification_info_color', models.CharField(default='#2196F3', max_length=20, verbose_name='Info Notification Color')),
                ('notification_text_color', models.CharField(default='#FFFFFF', max_length=20, verbose_name='Notification Text Color')),
                ('default_currency', models.CharField(choices=[('KHR', 'Cambodian Riel (៛)'), ('USD', 'US Dollar ($)')], default='KHR', max_length=10, verbose_name='Default Currency')),
                ('last_backup_date', models.DateTimeField(blank=True, null=True, verbose_name='Last Backup Date')),
                ('last_data_cleanup', models.DateTimeField(blank=True, null=True, verbose_name='Last Data Cleanup')),
                ('funds', models.IntegerField(default=0, help_text='Current funds in the system', verbose_name='Funds')),
                ('last_checked', models.DateField(blank=True, null=True, verbose_name='Last Checked')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
            options={
                'verbose_name': 'Settings',
                'verbose_name_plural': 'Settings',
            },
        ),
    ]
