# Generated by Django 5.0.2 on 2025-05-22 22:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings', '0010_alter_rolepermission_role'),
    ]

    operations = [
        migrations.AlterField(
            model_name='rolepermission',
            name='module',
            field=models.CharField(choices=[('dashboard', 'Dashboard'), ('member', 'Member Management'), ('payment', 'Payment Processing'), ('payroll', 'Payroll Management'), ('product', 'Product Management'), ('purchase', 'Purchase Management'), ('pos', 'Point of Sale'), ('paypervisit', 'Pay-per-visit'), ('bill', 'Bill Management'), ('finance', 'Finance Management'), ('financialreport', 'Financial Reports'), ('settings', 'Settings')], max_length=50, verbose_name='Module'),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='role',
            field=models.CharField(choices=[('admin', 'Admin'), ('cashier', 'Cashier'), ('coach', 'Coach'), ('cleaner', 'Cleaner'), ('security', 'Security Guard')], max_length=20, verbose_name='Role'),
        ),
    ]
