# Generated by Django 5.0.2 on 2025-05-18 00:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('settings', '0002_migrate_existing_settings'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PrintTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('template_type', models.CharField(choices=[('payment', 'Payment Receipt'), ('paypervisit', 'Pay-per-visit Receipt'), ('member_card', 'Member ID Card'), ('bill', 'Bill Receipt'), ('salary', 'Salary Slip'), ('report', 'Financial Report')], max_length=20, verbose_name='Template Type')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.CharField(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='print_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#000000', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#2563eb', max_length=20, verbose_name='Accent Color')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=True, verbose_name='Show Signatures')),
                ('show_logo', models.BooleanField(default=True, verbose_name='Show Logo')),
                ('table_header_color', models.CharField(blank=True, default='#f8f9fa', max_length=20, null=True, verbose_name='Table Header Color')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_templates', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Print Template',
                'verbose_name_plural': 'Print Templates',
                'unique_together': {('is_default', 'template_type')},
            },
        ),
    ]
