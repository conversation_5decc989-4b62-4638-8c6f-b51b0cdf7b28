import logging
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from .models import RolePermission
from .cache_manager import PermissionCacheManager

logger = logging.getLogger(__name__)
User = get_user_model()

@receiver(pre_save, sender=RolePermission)
def track_permission_changes(sender, instance, **kwargs):
    """
    Track permission changes before saving to detect what changed
    """
    if instance.pk:
        try:
            old_instance = RolePermission.objects.get(pk=instance.pk)
            instance._old_permission_level = old_instance.permission_level
        except RolePermission.DoesNotExist:
            instance._old_permission_level = 'none'
    else:
        instance._old_permission_level = 'none'

@receiver(post_save, sender=RolePermission)
def handle_permission_update(sender, instance, created, **kwargs):
    """
    Handle permission updates - invalidate cache and notify users
    """
    try:
        # Invalidate caches
        PermissionCacheManager.invalidate_role_cache(instance.role)
        
        # Get affected users
        affected_users = User.objects.filter(role=instance.role)
        
        # Invalidate user caches
        for user in affected_users:
            PermissionCacheManager.invalidate_user_cache(user.id)
        
        # Determine what changed
        old_level = getattr(instance, '_old_permission_level', 'none')
        new_level = instance.permission_level
        
        changes = {}
        if old_level != new_level:
            changes[instance.module] = {
                'old': old_level,
                'new': new_level
            }
        
        # Send notifications
        PermissionCacheManager.notify_permission_change(
            role=instance.role,
            changes=changes,
            affected_users=affected_users
        )
        
        action = "created" if created else "updated"
        logger.info(f"Permission {action}: {instance.role}.{instance.module} = {instance.permission_level}")
        
    except Exception as e:
        logger.error(f"Error handling permission update: {str(e)}")

@receiver(post_delete, sender=RolePermission)
def handle_permission_delete(sender, instance, **kwargs):
    """
    Handle permission deletion - invalidate cache and notify users
    """
    try:
        # Invalidate caches
        PermissionCacheManager.invalidate_role_cache(instance.role)
        
        # Get affected users
        affected_users = User.objects.filter(role=instance.role)
        
        # Invalidate user caches
        for user in affected_users:
            PermissionCacheManager.invalidate_user_cache(user.id)
        
        # Prepare change notification
        changes = {
            instance.module: {
                'old': instance.permission_level,
                'new': 'none'
            }
        }
        
        # Send notifications
        PermissionCacheManager.notify_permission_change(
            role=instance.role,
            changes=changes,
            affected_users=affected_users
        )
        
        logger.info(f"Permission deleted: {instance.role}.{instance.module}")
        
    except Exception as e:
        logger.error(f"Error handling permission deletion: {str(e)}")

@receiver(post_save, sender=User)
def handle_user_role_change(sender, instance, created, **kwargs):
    """
    Handle user role changes - invalidate cache for the user
    """
    if not created:
        try:
            # Check if role changed
            if hasattr(instance, '_old_role') and instance._old_role != instance.role:
                # Invalidate cache for the user
                PermissionCacheManager.invalidate_user_cache(instance.id)
                
                # Send notification about role change
                PermissionCacheManager.send_system_notification(
                    f"Your role has been changed to {instance.role}. Your permissions have been updated.",
                    notification_type='info'
                )
                
                logger.info(f"User role changed: {instance.username} from {instance._old_role} to {instance.role}")
        except Exception as e:
            logger.error(f"Error handling user role change: {str(e)}")

@receiver(pre_save, sender=User)
def track_user_role_changes(sender, instance, **kwargs):
    """
    Track user role changes before saving
    """
    if instance.pk:
        try:
            old_instance = User.objects.get(pk=instance.pk)
            instance._old_role = old_instance.role
        except User.DoesNotExist:
            instance._old_role = None
    else:
        instance._old_role = None
