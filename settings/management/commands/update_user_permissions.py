from django.core.management.base import BaseCommand
from django.db import transaction
from settings.models import <PERSON>Permission, MODULE_CHOICES
from settings.utils import get_default_permissions
from user.models import ROLE_CHOICES


class Command(BaseCommand):
    help = 'Update permissions to include the user management module for all roles'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force update even if permissions already exist',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS('Updating user management permissions...')
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        default_permissions = get_default_permissions()
        updated_count = 0
        created_count = 0
        skipped_count = 0
        
        # Check if 'user' module is in MODULE_CHOICES
        module_codes = [code for code, name in MODULE_CHOICES]
        if 'user' not in module_codes:
            self.stdout.write(
                self.style.ERROR(
                    "ERROR: 'user' module not found in MODULE_CHOICES. "
                    "Please add ('user', _('User Management')) to MODULE_CHOICES in settings/models.py"
                )
            )
            return
        
        self.stdout.write(f"Found 'user' module in MODULE_CHOICES ✓")
        
        with transaction.atomic():
            for role_code, role_name in ROLE_CHOICES:
                if role_code in default_permissions:
                    user_permission = default_permissions[role_code].get('user', 'none')
                    
                    try:
                        # Check if permission already exists
                        existing_permission = RolePermission.objects.get(
                            role=role_code, 
                            module='user'
                        )
                        
                        if force or existing_permission.permission_level != user_permission:
                            if not dry_run:
                                old_level = existing_permission.permission_level
                                existing_permission.permission_level = user_permission
                                existing_permission.save()
                                
                            self.stdout.write(
                                f"  {role_name}: Updated user permission "
                                f"from '{existing_permission.permission_level}' to '{user_permission}'"
                            )
                            updated_count += 1
                        else:
                            self.stdout.write(
                                f"  {role_name}: User permission already set to '{user_permission}' (skipped)"
                            )
                            skipped_count += 1
                            
                    except RolePermission.DoesNotExist:
                        # Create new permission
                        if not dry_run:
                            RolePermission.objects.create(
                                role=role_code,
                                module='user',
                                permission_level=user_permission
                            )
                        
                        self.stdout.write(
                            f"  {role_name}: Created user permission '{user_permission}'"
                        )
                        created_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f"  {role_name}: No default permissions defined (skipped)"
                        )
                    )
                    skipped_count += 1
        
        # Summary
        self.stdout.write("\n" + "="*50)
        self.stdout.write(self.style.SUCCESS("SUMMARY:"))
        self.stdout.write(f"  Created: {created_count}")
        self.stdout.write(f"  Updated: {updated_count}")
        self.stdout.write(f"  Skipped: {skipped_count}")
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    "\nThis was a dry run. Use --force to apply changes."
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    "\nUser management permissions have been successfully updated!"
                )
            )
            
            # Clear permission cache
            from settings.cache_manager import PermissionCacheManager
            PermissionCacheManager.invalidate_all_cache()
            self.stdout.write("Permission cache cleared ✓")
