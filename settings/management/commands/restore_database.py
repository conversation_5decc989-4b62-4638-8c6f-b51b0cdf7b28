import os
import subprocess
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Restore a MySQL database from a backup file'

    def add_arguments(self, parser):
        parser.add_argument(
            'backup_file',
            help='Path to the backup file to restore from',
        )

    def handle(self, *args, **options):
        # Get database settings
        db_settings = settings.DATABASES['default']
        db_name = db_settings['NAME']
        db_user = db_settings['USER']
        db_password = db_settings['PASSWORD']
        db_host = db_settings['HOST']
        db_port = db_settings['PORT']

        # Get backup file path
        backup_file = options['backup_file']
        
        # Check if the backup file exists
        if not os.path.exists(backup_file):
            self.stdout.write(self.style.ERROR(f'Backup file not found: {backup_file}'))
            return

        # Build the mysql command
        cmd = [
            'mysql',
            f'--host={db_host}',
            f'--port={db_port}',
            f'--user={db_user}',
        ]

        if db_password:
            cmd.append(f'--password={db_password}')

        cmd.append(db_name)

        # Execute the command
        try:
            self.stdout.write(self.style.WARNING(f'Restoring database "{db_name}" from {backup_file}...'))
            
            with open(backup_file, 'r') as f:
                process = subprocess.Popen(cmd, stdin=f, stderr=subprocess.PIPE)
                _, stderr = process.communicate()

                if process.returncode != 0:
                    self.stdout.write(self.style.ERROR(f'Error restoring backup: {stderr.decode()}'))
                    return

            self.stdout.write(self.style.SUCCESS(f'Database restored successfully from {backup_file}'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error restoring backup: {str(e)}'))
