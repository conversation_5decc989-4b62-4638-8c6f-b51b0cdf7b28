from settings.utils import get_settings, get_user_permissions
from settings.models import RolePer<PERSON>

def settings_processor(request):
    """
    Context processor that adds the settings object and user permissions to all templates.
    """
    context = {
        'settings': get_settings(),
    }

    # Add user permissions if the user is authenticated
    if hasattr(request, 'user') and request.user.is_authenticated:
        context['user_permissions'] = get_user_permissions(request.user)

    return context
