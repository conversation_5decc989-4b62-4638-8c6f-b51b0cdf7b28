# Generated by Django 5.0.2 on 2025-05-09 22:54

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BillReceiptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.Char<PERSON>ield(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='Bill Payment Receipt', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you for your business!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='bill_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#000000', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#0066cc', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=True, verbose_name='Show Signatures')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Bill Receipt Template',
                'verbose_name_plural': 'Bill Receipt Templates',
            },
        ),
        migrations.CreateModel(
            name='Bill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_id', models.CharField(max_length=30, unique=True, verbose_name='Bill ID')),
                ('category', models.CharField(choices=[('electricity', 'Electricity'), ('water', 'Water'), ('internet', 'Internet'), ('rent', 'Rent'), ('maintenance', 'Maintenance'), ('equipment', 'Equipment'), ('supplies', 'Supplies'), ('other', 'Other')], max_length=20, verbose_name='Category')),
                ('provider', models.CharField(max_length=100, verbose_name='Provider/Vendor')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('month_year', models.DateField(verbose_name='Month & Year')),
                ('payment_period', models.IntegerField(choices=[(1, '1 Month'), (3, '3 Months'), (12, '1 Year')], default=1, verbose_name='Payment Period')),
                ('amount_khr', models.IntegerField(verbose_name='Amount (KHR)')),
                ('amount_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Amount (USD)')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('aba', 'ABA'), ('wing', 'Wing'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid')], default='pending', max_length=20, verbose_name='Payment Status')),
                ('payment_date', models.DateTimeField(blank=True, null=True, verbose_name='Payment Date')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('paid_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='paid_bills', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bill',
                'verbose_name_plural': 'Bills',
                'ordering': ['-month_year', 'category'],
            },
        ),
    ]
