from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db.models import Sum
from django.utils import timezone
from datetime import datetime, date, timedelta
from user.models import MetaData
from .models import Bill, BillReceiptTemplate
from core.utils import generate_unique_transaction_id
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action

def process_recurring_bill(bill):
    """
    Helper function to create a new bill for the next period if the bill is recurring
    """
    if not bill.is_recurring:
        return

    # Calculate next month's date
    if bill.payment_period == 1:  # 1 month
        next_month = bill.month_year.replace(day=1) + timedelta(days=32)
        next_month = next_month.replace(day=1)
    elif bill.payment_period == 3:  # 3 months
        next_month = bill.month_year.replace(day=1) + timedelta(days=92)
        next_month = next_month.replace(day=1)
    elif bill.payment_period == 12:  # 1 year
        next_month = bill.month_year.replace(day=1)
        next_month = next_month.replace(year=next_month.year + 1)

    # Generate unique bill ID
    new_bill_id = generate_unique_transaction_id(prefix="BILL")

    # Create the new bill record
    Bill.objects.create(
        bill_id=new_bill_id,
        category=bill.category,
        provider=bill.provider,
        description=bill.description,
        month_year=next_month,
        payment_period=bill.payment_period,
        amount_khr=bill.amount_khr,
        amount_usd=bill.amount_usd,
        payment_method=bill.payment_method,
        is_recurring=bill.is_recurring,
        notes=bill.notes
    )

# Access control is now handled by module_permission_required decorator

@login_required
@module_permission_required(module='bill', required_level='view')
def index(request):
    """
    Display bill management dashboard with list of bills
    """
    # Get only pending bills by default
    status_filter = request.GET.get('status', 'pending')
    bills = Bill.objects.all().order_by('-month_year')

    # Get filter parameters
    category_filter = request.GET.get('category', '')
    month_filter = request.GET.get('month', '')

    # Apply filters if provided
    if category_filter:
        bills = bills.filter(category=category_filter)
    if month_filter:
        month_date = datetime.strptime(month_filter, '%Y-%m')
        bills = bills.filter(month_year__year=month_date.year, month_year__month=month_date.month)
    if status_filter:
        bills = bills.filter(payment_status=status_filter)

    # Calculate total pending and paid
    total_pending = bills.filter(payment_status='pending').aggregate(Sum('amount_khr'))['amount_khr__sum'] or 0
    total_paid = bills.filter(payment_status='paid').aggregate(Sum('amount_khr'))['amount_khr__sum'] or 0

    # Get upcoming bills
    upcoming_bills = bills.filter(payment_status='pending')

    # Get recent payments
    recent_payments = bills.filter(payment_status='paid').order_by('-payment_date')[:5]

    context = {
        'bills': bills,
        'total_pending': total_pending,
        'total_paid': total_paid,
        'upcoming_bills': upcoming_bills,
        'recent_payments': recent_payments,
        'category_filter': category_filter,
        'month_filter': month_filter,
        'status_filter': status_filter,
        'bill_categories': Bill.BILL_CATEGORY_CHOICES,
    }
    return render(request, 'billmanagement/index.html', context)

@login_required
@module_permission_required(module='bill', required_level='view')
def bill_list(request):
    """
    Display a list of paid bills with filtering options
    """
    # Default to showing only paid bills
    status_filter = request.GET.get('status', 'paid')
    bills = Bill.objects.filter(payment_status=status_filter).order_by('-payment_date')

    # Get filter parameters
    category_filter = request.GET.get('category', '')
    month_filter = request.GET.get('month', '')

    # Apply filters if provided
    if category_filter:
        bills = bills.filter(category=category_filter)
    if month_filter:
        month_date = datetime.strptime(month_filter, '%Y-%m')
        bills = bills.filter(month_year__year=month_date.year, month_year__month=month_date.month)

    context = {
        'bills': bills,
        'category_filter': category_filter,
        'month_filter': month_filter,
        'status_filter': status_filter,
        'bill_categories': Bill.BILL_CATEGORY_CHOICES,
    }
    return render(request, 'billmanagement/bill_list.html', context)

@login_required
@module_permission_required(module='bill', required_level='edit')
def create_bill(request):
    """
    Create a new bill
    """
    if request.method == 'POST':
        try:
            category = request.POST.get('category')
            provider = request.POST.get('provider')
            description = request.POST.get('description')
            month_year = request.POST.get('month_year')
            payment_period = int(request.POST.get('payment_period'))
            amount_khr = int(request.POST.get('amount_khr'))
            amount_usd = request.POST.get('amount_usd', None)
            payment_method = request.POST.get('payment_method')
            is_recurring = 'is_recurring' in request.POST
            notes = request.POST.get('notes')

            # Convert amount_usd to decimal if provided
            if amount_usd and amount_usd.strip():
                amount_usd = float(amount_usd)
            else:
                amount_usd = None

            # Convert month_year to date
            month_date = datetime.strptime(month_year, '%Y-%m').date()

            # Generate unique bill ID
            bill_id = generate_unique_transaction_id(prefix="BILL")

            # Create the bill record
            bill = Bill.objects.create(
                bill_id=bill_id,
                category=category,
                provider=provider,
                description=description,
                month_year=month_date,
                payment_period=payment_period,
                amount_khr=amount_khr,
                amount_usd=amount_usd,
                payment_method=payment_method,
                is_recurring=is_recurring,
                notes=notes
            )

            # Log the bill creation
            log_create_action(
                request=request,
                module='bill',
                target_model='Bill',
                target_id=bill_id,
                target_description=f'Bill {bill_id} for {provider}',
                additional_data={
                    'category': category,
                    'provider': provider,
                    'description': description,
                    'month_year': month_date.strftime('%B %Y'),
                    'payment_period': payment_period,
                    'amount_khr': amount_khr,
                    'amount_usd': amount_usd,
                    'payment_method': payment_method,
                    'is_recurring': is_recurring,
                    'notes': notes
                },
                financial_impact=-amount_khr  # Negative because it's an expense
            )

            messages.success(request, f"Bill record created successfully for {provider} for {month_date.strftime('%B %Y')}.")
            return redirect('billmanagement:index')

        except Exception as e:
            messages.error(request, f"Error creating bill record: {str(e)}")

    context = {
        'bill_categories': Bill.BILL_CATEGORY_CHOICES,
        'payment_periods': Bill.PAYMENT_PERIOD_CHOICES,
        'payment_methods': Bill.PAYMENT_METHOD_CHOICES,
        'current_month': date.today().strftime('%Y-%m'),
    }
    return render(request, 'billmanagement/create_bill.html', context)

@login_required
@module_permission_required(module='bill', required_level='view')
def view_bill(request, pk):
    """
    View a bill
    """
    bill = get_object_or_404(Bill, pk=pk)

    context = {
        'bill': bill,
    }
    return render(request, 'billmanagement/view_bill.html', context)

@login_required
@module_permission_required(module='bill', required_level='edit')
def edit_bill(request, pk):
    """
    Edit a bill
    """
    bill = get_object_or_404(Bill, pk=pk)

    if bill.payment_status == 'paid':
        messages.error(request, "Paid bills cannot be edited.")
        return redirect('billmanagement:view_bill', pk=pk)

    if request.method == 'POST':
        try:
            bill.category = request.POST.get('category')
            bill.provider = request.POST.get('provider')
            bill.description = request.POST.get('description')
            month_year = request.POST.get('month_year')
            bill.payment_period = int(request.POST.get('payment_period'))
            bill.amount_khr = int(request.POST.get('amount_khr'))
            amount_usd = request.POST.get('amount_usd', None)
            bill.payment_method = request.POST.get('payment_method')
            bill.is_recurring = 'is_recurring' in request.POST
            bill.notes = request.POST.get('notes')

            # Convert amount_usd to decimal if provided
            if amount_usd:
                bill.amount_usd = float(amount_usd)
            else:
                bill.amount_usd = None

            # Convert month_year to date
            bill.month_year = datetime.strptime(month_year, '%Y-%m').date()

            bill.save()

            # Log the bill edit
            log_edit_action(
                request=request,
                module='bill',
                target_model='Bill',
                target_id=bill.bill_id,
                target_description=f'Bill {bill.bill_id} for {bill.provider}',
                additional_data={
                    'category': bill.category,
                    'provider': bill.provider,
                    'description': bill.description,
                    'month_year': bill.month_year.strftime('%B %Y'),
                    'payment_period': bill.payment_period,
                    'amount_khr': bill.amount_khr,
                    'amount_usd': bill.amount_usd,
                    'payment_method': bill.payment_method,
                    'is_recurring': bill.is_recurring,
                    'notes': bill.notes
                }
            )

            messages.success(request, f"Bill record updated successfully.")
            return redirect('billmanagement:view_bill', pk=pk)

        except Exception as e:
            messages.error(request, f"Error updating bill record: {str(e)}")

    context = {
        'bill': bill,
        'bill_categories': Bill.BILL_CATEGORY_CHOICES,
        'payment_periods': Bill.PAYMENT_PERIOD_CHOICES,
        'payment_methods': Bill.PAYMENT_METHOD_CHOICES,
        'month_year': bill.month_year.strftime('%Y-%m'),
    }
    return render(request, 'billmanagement/edit_bill.html', context)

@login_required
@module_permission_required(module='bill', required_level='full')
def delete_bill(request, pk):
    """
    Delete a bill record and reverse its financial impact
    """
    bill = get_object_or_404(Bill, pk=pk)

    # Store information for success message
    bill_id = bill.bill_id
    provider = bill.provider
    amount_khr = bill.amount_khr
    category = bill.get_category_display()
    payment_status = bill.get_payment_status_display()
    processed_by = bill.paid_by.username if bill.paid_by else "System"

    try:
        # Log the bill deletion before deleting
        log_delete_action(
            request=request,
            module='bill',
            target_model='Bill',
            target_id=bill_id,
            target_description=f'Bill {bill_id} for {provider}',
            additional_data={
                'category': category,
                'provider': provider,
                'description': bill.description,
                'month_year': bill.month_year.strftime('%B %Y'),
                'payment_period': bill.payment_period,
                'amount_khr': amount_khr,
                'amount_usd': bill.amount_usd,
                'payment_method': bill.payment_method,
                'payment_status': payment_status,
                'is_recurring': bill.is_recurring,
                'notes': bill.notes,
                'processed_by': processed_by
            },
            financial_impact=amount_khr if bill.payment_status == 'paid' else 0
        )

        # Only reverse financial impact if the bill was already paid
        if bill.payment_status == 'paid':
            # Reverse the financial impact by adding the amount back to funds
            meta = MetaData.objects.last()
            if meta:
                meta.funds += amount_khr
                meta.save()

        # Delete the bill
        bill.delete()

        # Format amount for display
        formatted_amount = f"{amount_khr:,}៛"

        # Create success message based on payment status
        if bill.payment_status == 'paid':
            success_message = (
                f"Bill {bill_id} deleted successfully! "
                f"Amount {formatted_amount} for {provider} ({category}) has been added back to gym funds. "
                f"(Status: {payment_status}, Processed by: {processed_by})"
            )
        else:
            success_message = (
                f"Bill {bill_id} deleted successfully! "
                f"Pending bill of {formatted_amount} for {provider} ({category}) has been removed. "
                f"(Status: {payment_status})"
            )

        messages.success(request, success_message)

    except Exception as e:
        messages.error(request, f"Error deleting bill {bill_id}: {str(e)}")

    return redirect('billmanagement:index')

@login_required
@module_permission_required(module='bill', required_level='edit')
def process_payment(request, pk):
    """
    Process a bill payment with option to edit the amount
    """
    bill = get_object_or_404(Bill, pk=pk)

    if bill.payment_status == 'paid':
        messages.error(request, "This bill has already been paid.")
        return redirect('billmanagement:view_bill', pk=pk)

    if request.method == 'POST':
        try:
            # Get the updated amount from the form
            amount_khr = int(request.POST.get('amount_khr'))
            amount_usd = request.POST.get('amount_usd', None)

            # Convert amount_usd to decimal if provided
            if amount_usd and amount_usd.strip():
                amount_usd = float(amount_usd)
            else:
                amount_usd = None

            # Update bill with new amounts
            bill.amount_khr = amount_khr
            bill.amount_usd = amount_usd

            # Update bill status
            bill.payment_status = 'paid'
            bill.payment_date = timezone.now()
            bill.paid_by = request.user
            bill.save()

            # Update funds
            meta = MetaData.objects.last()
            if not meta:
                meta = MetaData.objects.create(funds=0)
            meta.funds -= bill.amount_khr
            meta.save()

            # Process the rest of the payment logic
            if bill.is_recurring:
                # Calculate next month's date for the message
                if bill.payment_period == 1:  # 1 month
                    next_month = bill.month_year.replace(day=1) + timedelta(days=32)
                    next_month = next_month.replace(day=1)
                elif bill.payment_period == 3:  # 3 months
                    next_month = bill.month_year.replace(day=1) + timedelta(days=92)
                    next_month = next_month.replace(day=1)
                elif bill.payment_period == 12:  # 1 year
                    next_month = bill.month_year.replace(day=1)
                    next_month = next_month.replace(year=next_month.year + 1)

                process_recurring_bill(bill)
                messages.success(request, f"Payment of {bill.amount_khr}៛ successfully processed for {bill.provider}. A new bill for {next_month.strftime('%B %Y')} has been created.")
            else:
                messages.success(request, f"Payment of {bill.amount_khr}៛ successfully processed for {bill.provider}")

            return redirect('billmanagement:view_bill', pk=pk)

        except Exception as e:
            messages.error(request, f"Error processing payment: {str(e)}")

    # If GET request, show the payment form
    context = {
        'bill': bill,
        'payment_methods': Bill.PAYMENT_METHOD_CHOICES,
    }
    return render(request, 'billmanagement/process_payment.html', context)

@login_required
@module_permission_required(module='bill', required_level='view')
def print_receipt(request, pk):
    """
    Print a receipt for a bill payment
    """
    bill = get_object_or_404(Bill, pk=pk)

    if bill.payment_status != 'paid':
        messages.error(request, "Cannot print receipt for unpaid bill.")
        return redirect('billmanagement:view_bill', pk=pk)

    # Get default template or first available
    template = BillReceiptTemplate.objects.filter(is_default=True).first()
    if not template:
        template = BillReceiptTemplate.objects.first()
        if not template:
            template = BillReceiptTemplate.objects.create(
                name="Default Template",
                is_default=True
            )

    context = {
        'bill': bill,
        'template': template,
    }
    return render(request, 'billmanagement/print_receipt.html', context)

@login_required
@module_permission_required(module='bill', required_level='edit')
def bulk_actions(request):
    """
    Handle bulk actions for bills
    """
    if request.method != "POST":
        return redirect('billmanagement:index')

    action = request.POST.get('bulk_action')
    selected_ids = request.POST.getlist('selected_bills')

    if not selected_ids:
        messages.error(request, "No bills selected.")
        return redirect('billmanagement:index')

    bills = Bill.objects.filter(id__in=selected_ids)

    if action == 'delete':
        try:
            count = bills.count()
            bills.delete()
            messages.success(request, f"Successfully deleted {count} bills.")
        except Exception as e:
            messages.error(request, f"Error deleting bills: {str(e)}")

    elif action == 'process':
        try:
            # Only process pending bills
            pending_bills = bills.filter(payment_status='pending')
            count = pending_bills.count()

            if count == 0:
                messages.warning(request, "No pending bills selected.")
                return redirect('billmanagement:index')

            # Update bill status
            for bill in pending_bills:
                bill.payment_status = 'paid'
                bill.payment_date = timezone.now()
                bill.paid_by = request.user
                bill.save()

                # Update funds
                meta = MetaData.objects.last()
                if not meta:
                    meta = MetaData.objects.create(funds=0)
                meta.funds -= bill.amount_khr
                meta.save()

                # Create next month's bill if this is a recurring bill
                if bill.is_recurring:
                    process_recurring_bill(bill)

            messages.success(request, f"Successfully processed {count} bills.")
        except Exception as e:
            messages.error(request, f"Error processing bills: {str(e)}")

    elif action == 'print':
        try:
            # Get the selected bills
            paid_bills = bills.filter(payment_status='paid')

            if paid_bills.count() == 0:
                messages.warning(request, "No paid bills selected.")
                return redirect('billmanagement:index')

            # Render the bulk print template
            context = {
                'bills': paid_bills,
            }
            return render(request, 'billmanagement/bulk_print.html', context)
        except Exception as e:
            messages.error(request, f"Error printing receipts: {str(e)}")

    return redirect('billmanagement:index')

# Template management views
@login_required
@module_permission_required(module='bill', required_level='view')
def template_list(request):
    """
    List all bill receipt templates
    """
    templates = BillReceiptTemplate.objects.all().order_by('-is_default', 'name')

    context = {
        'templates': templates,
    }
    return render(request, 'billmanagement/template_list.html', context)

@login_required
@module_permission_required(module='bill', required_level='edit')
def create_template(request):
    """
    Create a new bill receipt template
    """
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            language = request.POST.get('language')
            header_text = request.POST.get('header_text')
            subheader_text = request.POST.get('subheader_text')
            footer_text = request.POST.get('footer_text')
            background_color = request.POST.get('background_color')
            text_color = request.POST.get('text_color')
            accent_color = request.POST.get('accent_color')
            is_default = 'is_default' in request.POST
            show_company_info = 'show_company_info' in request.POST
            show_signatures = 'show_signatures' in request.POST
            custom_css = request.POST.get('custom_css')

            template = BillReceiptTemplate.objects.create(
                name=name,
                language=language,
                header_text=header_text,
                subheader_text=subheader_text,
                footer_text=footer_text,
                background_color=background_color,
                text_color=text_color,
                accent_color=accent_color,
                is_default=is_default,
                show_company_info=show_company_info,
                show_signatures=show_signatures,
                custom_css=custom_css
            )

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']
                template.save()

            messages.success(request, f"Template '{name}' created successfully.")
            return redirect('billmanagement:template_list')

        except Exception as e:
            messages.error(request, f"Error creating template: {str(e)}")

    context = {
        'languages': BillReceiptTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'billmanagement/create_template.html', context)

@login_required
@module_permission_required(module='bill', required_level='edit')
def edit_template(request, pk):
    """
    Edit a bill receipt template
    """
    template = get_object_or_404(BillReceiptTemplate, pk=pk)

    if request.method == 'POST':
        try:
            template.name = request.POST.get('name')
            template.language = request.POST.get('language')
            template.header_text = request.POST.get('header_text')
            template.subheader_text = request.POST.get('subheader_text')
            template.footer_text = request.POST.get('footer_text')
            template.background_color = request.POST.get('background_color')
            template.text_color = request.POST.get('text_color')
            template.accent_color = request.POST.get('accent_color')
            template.is_default = 'is_default' in request.POST
            template.show_company_info = 'show_company_info' in request.POST
            template.show_signatures = 'show_signatures' in request.POST
            template.custom_css = request.POST.get('custom_css')

            # Handle logo upload
            if 'company_logo' in request.FILES:
                template.company_logo = request.FILES['company_logo']

            template.save()

            messages.success(request, f"Template '{template.name}' updated successfully.")
            return redirect('billmanagement:template_list')

        except Exception as e:
            messages.error(request, f"Error updating template: {str(e)}")

    context = {
        'template': template,
        'languages': BillReceiptTemplate.LANGUAGE_CHOICES,
    }
    return render(request, 'billmanagement/edit_template.html', context)

@login_required
@module_permission_required(module='bill', required_level='full')
def delete_template(request, pk):
    """
    Delete a bill receipt template
    """
    template = get_object_or_404(BillReceiptTemplate, pk=pk)

    if request.method == 'POST':
        template_name = template.name
        template.delete()
        messages.success(request, f"Template '{template_name}' deleted successfully.")
        return redirect('billmanagement:template_list')

    context = {
        'template': template,
    }
    return render(request, 'billmanagement/delete_template.html', context)

@login_required
@module_permission_required(module='bill', required_level='view')
def preview_template(request, pk):
    """
    Preview a bill receipt template
    """
    template = get_object_or_404(BillReceiptTemplate, pk=pk)

    # Create a sample bill for preview
    sample_bill = Bill(
        bill_id="BILL-SAMPLE",
        category="electricity",
        provider="Sample Provider",
        description="Sample bill for template preview",
        month_year=date.today(),
        payment_period=1,
        amount_khr=100000,
        amount_usd=25.00,
        payment_method="cash",
        payment_status="paid",
        payment_date=timezone.now(),
        paid_by=request.user,
        notes="This is a sample bill for template preview"
    )

    context = {
        'template': template,
        'bill': sample_bill,
        'is_preview': True
    }
    return render(request, 'billmanagement/preview_template.html', context)
