from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from user.models import User, MetaData
from core.utils import generate_unique_transaction_id

class Bill(models.Model):
    """
    Model for tracking bills and expenses
    """
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('other', _('Other')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
    ]

    BILL_CATEGORY_CHOICES = [
        ('electricity', 'Electricity'),
        ('water', 'Water'),
        ('internet', 'Internet'),
        ('rent', 'Rent'),
        ('maintenance', 'Maintenance'),
        ('equipment', 'Equipment'),
        ('supplies', 'Supplies'),
        ('other', 'Other'),
    ]

    PAYMENT_PERIOD_CHOICES = [
        (1, '1 Month'),
        (3, '3 Months'),
        (12, '1 Year'),
    ]

    bill_id = models.CharField(_("Bill ID"), max_length=30, unique=True)
    category = models.CharField(_("Category"), max_length=20, choices=BILL_CATEGORY_CHOICES)
    provider = models.CharField(_("Provider/Vendor"), max_length=100)
    description = models.TextField(_("Description"), null=True, blank=True)
    month_year = models.DateField(_("Month & Year"))
    payment_period = models.IntegerField(_("Payment Period"), choices=PAYMENT_PERIOD_CHOICES, default=1)
    amount_khr = models.IntegerField(_("Amount (KHR)"))
    amount_usd = models.DecimalField(_("Amount (USD)"), max_digits=10, decimal_places=2, null=True, blank=True)
    payment_method = models.CharField(_("Payment Method"), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    payment_status = models.CharField(_("Payment Status"), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    payment_date = models.DateTimeField(_("Payment Date"), null=True, blank=True)
    paid_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='paid_bills')
    is_recurring = models.BooleanField(_("Recurring Bill"), default=False, help_text=_("If checked, a new bill will be created automatically for the next period when this bill is paid."))
    notes = models.TextField(_("Notes"), null=True, blank=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # Generate bill_id if not provided
        if not self.bill_id:
            self.bill_id = generate_unique_transaction_id(prefix="BILL")

        # Set payment date when status changes to paid
        if self.payment_status == 'paid' and not self.payment_date:
            self.payment_date = timezone.now()

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.bill_id} - {self.provider} ({self.category})"

    class Meta:
        ordering = ['-month_year', 'category']
        verbose_name = _("Bill")
        verbose_name_plural = _("Bills")


class BillReceiptTemplate(models.Model):
    """
    Model for bill receipt templates
    """
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('km', 'Khmer'),
        ('both', 'Both (Bilingual)'),
    ]

    name = models.CharField(_("Template Name"), max_length=100)
    is_default = models.BooleanField(_("Default Template"), default=False)
    language = models.CharField(_("Language"), max_length=10, choices=LANGUAGE_CHOICES, default='en')
    header_text = models.CharField(_("Header Text"), max_length=200, default="LEGEND FITNESS")
    subheader_text = models.CharField(_("Subheader Text"), max_length=200, default="Bill Payment Receipt")
    footer_text = models.CharField(_("Footer Text"), max_length=200, default="Thank you for your business!")
    company_logo = models.ImageField(_("Company Logo"), upload_to='bill_templates/', null=True, blank=True)
    background_color = models.CharField(_("Background Color"), max_length=20, default="#ffffff")
    text_color = models.CharField(_("Text Color"), max_length=20, default="#000000")
    accent_color = models.CharField(_("Accent Color"), max_length=20, default="#0066cc")
    show_company_info = models.BooleanField(_("Show Company Info"), default=True)
    show_signatures = models.BooleanField(_("Show Signatures"), default=True)
    custom_css = models.TextField(_("Custom CSS"), null=True, blank=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # If this is set as default, unset all others
        if self.is_default:
            BillReceiptTemplate.objects.filter(is_default=True).update(is_default=False)

        # If no default exists, set this as default
        if not BillReceiptTemplate.objects.filter(is_default=True).exists():
            self.is_default = True

        super().save(*args, **kwargs)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _("Bill Receipt Template")
        verbose_name_plural = _("Bill Receipt Templates")
