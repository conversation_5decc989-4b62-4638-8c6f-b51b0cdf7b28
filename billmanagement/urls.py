from django.urls import path
from . import views

app_name = "billmanagement"

urlpatterns = [
    path('', views.index, name='index'),
    path('create/', views.create_bill, name='create_bill'),
    path('view/<int:pk>/', views.view_bill, name='view_bill'),
    path('edit/<int:pk>/', views.edit_bill, name='edit_bill'),
    path('delete/<int:pk>/', views.delete_bill, name='delete_bill'),
    path('process/<int:pk>/', views.process_payment, name='process_payment'),
    path('print/<int:pk>/', views.print_receipt, name='print_receipt'),
    path('list/', views.bill_list, name='bill_list'),
    path('bulk-actions/', views.bulk_actions, name='bulk_actions'),
    
    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/edit/<int:pk>/', views.edit_template, name='edit_template'),
    path('templates/delete/<int:pk>/', views.delete_template, name='delete_template'),
    path('templates/preview/<int:pk>/', views.preview_template, name='preview_template'),
]
