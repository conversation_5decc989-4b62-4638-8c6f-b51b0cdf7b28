/**
 * Dashboard Reports JavaScript
 * Handles loading and updating of dashboard report cards
 */

class DashboardReports {
    constructor() {
        console.log('Dashboard Reports: Constructor called');
        this.currentPeriod = 'day';
        this.updateInterval = null;
        this.init();
    }

    init() {
        console.log('Dashboard Reports: Initializing...');
        this.setupEventListeners();
        this.loadAllReports();
        this.startAutoRefresh();
        console.log('Dashboard Reports: Initialization complete');
    }

    setupEventListeners() {
        // Period selector buttons - use more specific event handling
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('period-btn')) {
                console.log('Period button clicked:', e.target.dataset.period);
                e.preventDefault();
                e.stopPropagation(); // Prevent event bubbling to report card
                this.handlePeriodChange(e.target);
            }
        });

        // Report card click handlers - only for non-button areas
        document.addEventListener('click', (e) => {
            // Don't handle card clicks if the target is a button or inside a button container
            if (e.target.classList.contains('period-btn') ||
                e.target.closest('.period-selector') ||
                e.target.closest('.trend-indicator')) {
                return;
            }

            const reportCard = e.target.closest('.report-card');
            if (reportCard) {
                this.handleReportCardClick(reportCard);
            }
        });
    }

    handlePeriodChange(button) {
        console.log('handlePeriodChange called with button:', button);

        const period = button.dataset.period;
        const container = button.closest('.report-card');

        if (!period) {
            console.error('No period data found on button:', button);
            return;
        }

        if (!container) {
            console.error('No report card container found for button:', button);
            return;
        }

        const reportType = container.dataset.reportType;
        console.log(`Changing period to "${period}" for report type "${reportType}"`);

        // Update active state for this specific card
        container.querySelectorAll('.period-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        button.classList.add('active');

        // Add visual feedback - flash the card border
        container.style.borderLeft = '4px solid #3b82f6';
        container.style.transition = 'border-left-color 0.3s ease';

        console.log('Active state updated, loading report...');

        // Update report data for this specific card only
        this.loadReport(reportType, period);
    }

    handleReportCardClick(card) {
        const reportType = card.dataset.reportType;
        const detailUrl = this.getDetailUrl(reportType);

        if (detailUrl) {
            window.location.href = detailUrl;
        }
    }

    getDetailUrl(reportType) {
        const urls = {
            'paypervisit': '/paypervisit/',
            'product': '/product/',
            'overview': '/member/',
            'income': '/financialreport/income/',
            'expense': '/financialreport/expense/',
            'balance': '/financialreport/balance/'
        };
        return urls[reportType] || null;
    }

    async loadAllReports() {
        const reportTypes = ['paypervisit', 'product', 'overview', 'income', 'expense', 'balance'];

        for (const type of reportTypes) {
            await this.loadReport(type, this.currentPeriod);
        }
    }

    async loadReport(reportType, period = 'day') {
        console.log(`loadReport called: reportType="${reportType}", period="${period}"`);

        const card = document.querySelector(`[data-report-type="${reportType}"]`);
        if (!card) {
            console.error(`No card found for report type: ${reportType}`);
            return;
        }

        try {
            this.showLoading(card);

            // Map report types to correct API endpoints
            const apiEndpoints = {
                'paypervisit': 'paypervisit-report',
                'product': 'product-sales-report',
                'overview': 'overview-report',
                'income': 'income-report',
                'expense': 'expense-report',
                'balance': 'balance-report'
            };

            const endpoint = apiEndpoints[reportType] || `${reportType}-report`;
            const apiUrl = `/api/dashboard/${endpoint}/?period=${period}`;
            console.log(`Making API call to: ${apiUrl}`);

            const response = await fetch(apiUrl);
            if (!response.ok) {
                throw new Error(`API call failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log(`API response for ${reportType}:`, data);

            this.updateReportCard(card, data, reportType);
            console.log(`Report card updated successfully for ${reportType}`);

        } catch (error) {
            console.error(`Error loading ${reportType} report:`, error);
            this.showError(card, 'Failed to load data');
        }
    }

    showLoading(card) {
        const content = card.querySelector('.report-card-content');
        content.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                Loading...
            </div>
        `;
    }

    showError(card, message) {
        const content = card.querySelector('.report-card-content');
        content.innerHTML = `
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                ${message}
            </div>
        `;
    }

    updateReportCard(card, data, reportType) {
        const content = card.querySelector('.report-card-content');

        switch (reportType) {
            case 'paypervisit':
                this.updatePayPerVisitCard(content, data);
                break;
            case 'product':
                this.updateProductCard(content, data);
                break;
            case 'overview':
                this.updateOverviewCard(content, data);
                break;
            case 'income':
                this.updateIncomeCard(content, data);
                break;
            case 'expense':
                this.updateExpenseCard(content, data);
                break;
            case 'balance':
                this.updateBalanceCard(content, data);
                break;
        }

        // Add period indicator to subtitle
        const subtitle = content.querySelector('.report-card-subtitle');
        if (subtitle && data.period) {
            const periodText = data.period.charAt(0).toUpperCase() + data.period.slice(1);
            subtitle.textContent = subtitle.textContent.split(' (')[0] + ` (${periodText})`;
        }

        // Add animation effect
        content.classList.add('value-update');
        setTimeout(() => content.classList.remove('value-update'), 300);

        // Reset border color after update
        setTimeout(() => {
            const originalBorderColor = card.classList.contains('income') ? '#3b82f6' :
                                      card.classList.contains('expense') ? '#ef4444' :
                                      card.classList.contains('balance') ? '#10b981' :
                                      card.classList.contains('overview') ? '#8b5cf6' :
                                      card.classList.contains('paypervisit') ? '#f59e0b' :
                                      card.classList.contains('product') ? '#06b6d4' : '#3b82f6';
            card.style.borderLeftColor = originalBorderColor;
        }, 500);
    }

    updatePayPerVisitCard(content, data) {
        content.innerHTML = `
            <div class="report-card-main-value">${this.formatCurrency(data.total_revenue)}</div>
            <div class="report-card-subtitle">Total Revenue</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${data.total_transactions}</div>
                    <div class="metric-label">Transactions</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${data.total_visitors}</div>
                    <div class="metric-label">Visitors</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.avg_per_transaction)}</div>
                    <div class="metric-label">Avg/Transaction</div>
                </div>
            </div>
        `;
    }

    updateProductCard(content, data) {
        const topProductsHtml = data.top_products.length > 0
            ? data.top_products.map(product => `
                <div class="product-item">
                    <span class="product-name">${product.product__name}</span>
                    <span class="product-quantity">${product.total_quantity} sold</span>
                </div>
            `).join('')
            : '<div class="product-item"><span class="product-name">No sales data</span></div>';

        content.innerHTML = `
            <div class="report-card-main-value">${this.formatCurrency(data.total_sales)}</div>
            <div class="report-card-subtitle">Total Sales</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${data.total_transactions}</div>
                    <div class="metric-label">Transactions</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${data.total_items}</div>
                    <div class="metric-label">Items Sold</div>
                </div>
            </div>
            <div class="top-products-list">
                <div style="font-size: 0.875rem; font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Top Products</div>
                ${topProductsHtml}
            </div>
        `;
    }

    updateOverviewCard(content, data) {
        content.innerHTML = `
            <div class="report-card-main-value">${data.total_members}</div>
            <div class="report-card-subtitle">Total Members</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${data.active_members}</div>
                    <div class="metric-label">Active</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${data.new_members}</div>
                    <div class="metric-label">New</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${data.expiring_soon}</div>
                    <div class="metric-label">Expiring</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${data.recent_payments}</div>
                    <div class="metric-label">Payments</div>
                </div>
            </div>
        `;
    }

    updateIncomeCard(content, data) {
        content.innerHTML = `
            <div class="report-card-main-value">${this.formatCurrency(data.total_income)}</div>
            <div class="report-card-subtitle">Total Income</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.membership_income)}</div>
                    <div class="metric-label">Memberships</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.product_income)}</div>
                    <div class="metric-label">Products</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.paypervisit_income)}</div>
                    <div class="metric-label">Pay-per-visit</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.deposit_income)}</div>
                    <div class="metric-label">Deposits</div>
                </div>
            </div>
        `;
    }

    updateExpenseCard(content, data) {
        content.innerHTML = `
            <div class="report-card-main-value">${this.formatCurrency(data.total_expenses)}</div>
            <div class="report-card-subtitle">Total Expenses</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.withdrawal_expenses)}</div>
                    <div class="metric-label">Withdrawals</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.salary_expenses)}</div>
                    <div class="metric-label">Salaries</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.bill_expenses)}</div>
                    <div class="metric-label">Bills</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.purchase_expenses)}</div>
                    <div class="metric-label">Purchases</div>
                </div>
            </div>
        `;
    }

    updateBalanceCard(content, data) {
        const trendIcon = data.trend === 'positive' ? 'fa-arrow-up' :
                         data.trend === 'negative' ? 'fa-arrow-down' : 'fa-minus';

        content.innerHTML = `
            <div class="report-card-main-value">${this.formatCurrency(data.current_balance)}</div>
            <div class="report-card-subtitle">Current Balance</div>
            <div class="report-card-metrics">
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.period_income)}</div>
                    <div class="metric-label">Period Income</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.period_expenses)}</div>
                    <div class="metric-label">Period Expenses</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">${this.formatCurrency(data.period_balance)}</div>
                    <div class="metric-label">Period Balance</div>
                </div>
            </div>
        `;

        // Update trend indicator in footer
        const footer = content.closest('.report-card').querySelector('.report-card-footer');
        const trendIndicator = footer.querySelector('.trend-indicator');
        if (trendIndicator) {
            trendIndicator.className = `trend-indicator ${data.trend}`;
            trendIndicator.innerHTML = `<i class="fas ${trendIcon}"></i> ${data.trend.charAt(0).toUpperCase() + data.trend.slice(1)} Trend`;
        }
    }

    formatCurrency(amount) {
        if (amount === null || amount === undefined) return '0៛';
        return new Intl.NumberFormat('en-US').format(amount) + '៛';
    }

    startAutoRefresh() {
        // Refresh data every 5 minutes
        this.updateInterval = setInterval(() => {
            this.loadAllReports();
        }, 300000);
    }

    stopAutoRefresh() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }
}

// Initialize dashboard reports when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('Dashboard Reports: DOM loaded, initializing...');
    try {
        window.dashboardReports = new DashboardReports();
        console.log('Dashboard Reports: Initialized successfully');
    } catch (error) {
        console.error('Dashboard Reports: Initialization failed', error);
    }
});

// Clean up on page unload
window.addEventListener('beforeunload', () => {
    if (window.dashboardReports) {
        window.dashboardReports.stopAutoRefresh();
    }
});
