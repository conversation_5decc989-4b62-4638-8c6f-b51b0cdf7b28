/**
 * Currency Formatter Utility
 * 
 * This utility provides functions for formatting currency values in JavaScript.
 */

/**
 * Format a number as Cambodian Riel (KHR) currency with thousand separators
 * @param {number|string} value - The value to format
 * @returns {string} - Formatted value with <PERSON><PERSON> symbol (e.g., "10,000៛")
 */
function formatKHR(value) {
    if (value === null || value === undefined || value === '') {
        return "0៛";
    }
    
    try {
        // Remove any existing commas if it's a string
        if (typeof value === 'string') {
            value = value.replace(/,/g, '');
        }
        
        // Convert to integer
        const valueInt = parseInt(parseFloat(value));
        
        // Format with thousand separators
        const formatted = valueInt.toLocaleString('en-US');
        
        // Add Riel symbol
        return `${formatted}៛`;
    } catch (error) {
        console.error("Error formatting KHR value:", error);
        return "0៛";
    }
}

/**
 * Format a number as US Dollar (USD) currency with thousand separators
 * @param {number|string} value - The value to format
 * @returns {string} - Formatted value with dollar symbol (e.g., "$10,000.00")
 */
function formatUSD(value) {
    if (value === null || value === undefined || value === '') {
        return "$0.00";
    }
    
    try {
        // Remove any existing commas if it's a string
        if (typeof value === 'string') {
            value = value.replace(/,/g, '');
        }
        
        // Convert to float
        const valueFloat = parseFloat(value);
        
        // Format with thousand separators and 2 decimal places
        const formatted = valueFloat.toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        // Add dollar symbol
        return `$${formatted}`;
    } catch (error) {
        console.error("Error formatting USD value:", error);
        return "$0.00";
    }
}

/**
 * Format a number with thousand separators without any currency symbol
 * @param {number|string} value - The value to format
 * @param {number} [decimals=0] - Number of decimal places (default: 0)
 * @returns {string} - Formatted value with thousand separators (e.g., "10,000")
 */
function formatNumber(value, decimals = 0) {
    if (value === null || value === undefined || value === '') {
        return "0";
    }
    
    try {
        // Remove any existing commas if it's a string
        if (typeof value === 'string') {
            value = value.replace(/,/g, '');
        }
        
        // Convert to float
        const valueFloat = parseFloat(value);
        
        // Format with thousand separators and specified decimal places
        const formatted = valueFloat.toLocaleString('en-US', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
        
        return formatted;
    } catch (error) {
        console.error("Error formatting number:", error);
        return "0";
    }
}

/**
 * Apply formatting to input fields as the user types
 * @param {HTMLElement} inputElement - The input element to format
 * @param {string} formatType - The type of formatting to apply ('khr', 'usd', or 'number')
 */
function setupFormattedInput(inputElement, formatType = 'number') {
    if (!inputElement) return;
    
    // Store the original value for form submission
    inputElement.addEventListener('focus', function() {
        // When focused, show the unformatted value for editing
        const value = this.value.replace(/[^\d.-]/g, '');
        this.value = value;
    });
    
    inputElement.addEventListener('blur', function() {
        // When blurred, format the value for display
        let formattedValue;
        
        switch (formatType.toLowerCase()) {
            case 'khr':
                formattedValue = formatKHR(this.value);
                break;
            case 'usd':
                formattedValue = formatUSD(this.value);
                break;
            default:
                formattedValue = formatNumber(this.value);
        }
        
        this.value = formattedValue;
    });
}
