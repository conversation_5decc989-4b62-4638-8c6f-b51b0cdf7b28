/**
 * Notifications System for Legend Fitness Club
 * 
 * This script provides a consistent notification system across the application.
 */

// Create notification container if it doesn't exist
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('notification-container')) {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container';
        document.body.appendChild(container);
    }
});

/**
 * Show a notification
 * @param {string} type - The type of notification: 'success', 'error', 'warning', or 'info'
 * @param {string} title - The notification title
 * @param {string} message - The notification message
 * @param {number} duration - How long to show the notification in milliseconds
 * @returns {HTMLElement} - The notification element
 */
function showNotification(type, title, message, duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;

    let icon = 'info-circle';
    if (type === 'success') icon = 'check-circle';
    if (type === 'error') icon = 'exclamation-circle';
    if (type === 'warning') icon = 'exclamation-triangle';

    notification.innerHTML = `
        <i class="fas fa-${icon} notification-icon"></i>
        <div class="notification-content">
            <div class="notification-title">${title}</div>
            <div class="notification-message">${message}</div>
        </div>
        <button type="button" class="notification-close">&times;</button>
        <div class="notification-progress"></div>
    `;

    const notificationContainer = document.getElementById('notification-container');
    notificationContainer.appendChild(notification);

    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
        closeNotification(notification);
    });

    // Auto close after duration
    setTimeout(() => {
        closeNotification(notification);
    }, duration);

    return notification;
}

/**
 * Close a notification with animation
 * @param {HTMLElement} notification - The notification element to close
 */
function closeNotification(notification) {
    notification.classList.add('closing');
    setTimeout(() => {
        notification.remove();
    }, 400); // Match the animation duration
}

/**
 * Process Django messages and convert them to notifications
 * This function should be called in templates where Django messages are used
 */
function processDjangoMessages() {
    // This function will be called from templates that have Django messages
    // The template will pass the messages to this function
}
