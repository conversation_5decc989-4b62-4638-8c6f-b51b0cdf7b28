/* Notifications System CSS for Legend Fitness Club */

/* Notification Container */
.notification-container {
    position: fixed;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-width: 450px;
    width: calc(100% - 17rem); /* Account for sidebar width (16rem) plus margin */
    margin-right: 0;
}

/* Notification Styling */
.notification {
    padding: 1rem 1.25rem;
    border-radius: 0.75rem;
    background-color: #1e3a8a;
    color: white;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    overflow: hidden;
    animation: slideInRight 0.4s ease-out;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.notification.closing {
    animation: slideOutRight 0.4s ease-in forwards;
}

.notification-icon {
    margin-right: 1rem;
    font-size: 1.5rem;
    color: white;
    opacity: 0.9;
}

.notification-content {
    flex-grow: 1;
}

.notification-title {
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.25rem;
    color: #ffffff; /* Bright white for better contrast */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1); /* Add subtle text shadow for better readability */
}

.notification-message {
    font-size: 0.9rem;
    color: #f8fafc; /* Very light blue-white for better readability */
}

.notification-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0.25rem;
    margin-left: 0.5rem;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 50%;
}

.notification-close:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
}

.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 4px;
    background-color: rgba(255, 255, 255, 0.3);
    width: 100%;
    transform-origin: left;
    animation: progress 5s linear forwards;
}

/* Notification Types */
.notification.success {
    background-color: #065f46;
}

.notification.error {
    background-color: #b91c1c;
}

.notification.warning {
    background-color: #b45309;
}

/* Animations */
@keyframes progress {
    0% { transform: scaleX(1); }
    100% { transform: scaleX(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

/* Mobile responsive styles for notifications */
@media (max-width: 640px) {
    .notification-container {
        width: calc(100% - 2rem); /* Full width minus margins */
        max-width: 100%;
        right: 1rem;
    }
}
