/* POS System Improvements CSS - Professional Blue Theme & Responsive Design */

/* POS Page Specific Optimizations */
.pos-page-container {
    /* Override default component section styles for better spacing */
    min-height: calc(100vh - 140px) !important;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
}

.pos-page-container .componentWrapper {
    /* Ensure wrapper doesn't add extra spacing */
    padding: 0;
    margin: 0;
}

/* Enhanced Professional Header Styling */
.pos-header {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: white !important;
    border-bottom: 2px solid #0ea5e9 !important;
    box-shadow: 0 4px 6px rgba(30, 58, 138, 0.1) !important;
}

.pos-title {
    color: white !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.pos-subtitle {
    color: #cbd5e1 !important;
}

.pos-header a {
    color: #93c5fd !important;
    transition: color 0.2s ease;
}

.pos-header a:hover {
    color: white !important;
    text-decoration: none !important;
}

/* Enhanced Cart Total Styling */
.cart-summary {
    background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
    color: white !important;
    border-radius: 0 0 0.75rem 0.75rem !important;
    padding: 1.25rem !important;
    margin-top: 1rem !important;
    box-shadow: 0 4px 6px rgba(30, 58, 138, 0.2) !important;
}

.cart-total {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

.cart-total-label {
    color: #cbd5e1 !important;
    font-weight: 600 !important;
    font-size: 1.1rem !important;
}

.cart-total-value {
    color: white !important;
    font-weight: 700 !important;
    font-size: 1.5rem !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.cart-total-icon {
    color: #93c5fd !important;
}

/* Enhanced Currency Display */
.currency-display {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.currency-khr {
    color: #1e40af;
    font-weight: 700;
}

.currency-usd {
    color: #059669;
    font-weight: 700;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(3px);
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    max-width: 90%;
    width: 300px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

.loading-text {
    font-size: 1rem;
    color: #333;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stock Warning */
.stock-warning {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(239, 68, 68, 0.1);
    border: 2px solid #ef4444;
    border-radius: 0.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    pointer-events: none;
}

.stock-warning-icon {
    font-size: 1.5rem;
    color: #ef4444;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 0.8; }
}

/* Button Loading State */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    top: calc(50% - 10px);
    left: calc(50% - 10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 0.8s linear infinite;
}

/* Improved Receipt */
.receipt-container {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 1rem;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
}

.receipt-container::-webkit-scrollbar {
    width: 6px;
}

.receipt-container::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.receipt-container::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 3px;
}

.receipt-items-header {
    display: grid;
    grid-template-columns: 2fr 0.5fr 1fr 1fr;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 600;
    font-size: 0.8rem;
    color: #4b5563;
    border-bottom: 1px dashed #e5e7eb;
}

.receipt-item {
    display: grid;
    grid-template-columns: 2fr 0.5fr 1fr 1fr;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f3f4f6;
}

.receipt-item-name {
    font-size: 0.85rem;
    color: #1f2937;
}

.receipt-item-qty {
    font-size: 0.85rem;
    color: #4b5563;
    text-align: center;
}

.receipt-item-unit-price {
    font-size: 0.85rem;
    color: #4b5563;
    text-align: right;
}

.receipt-item-price {
    font-size: 0.85rem;
    color: #1f2937;
    font-weight: 500;
    text-align: right;
}

.receipt-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.email-button {
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.email-button:hover {
    background-color: #2563eb;
}

/* Transaction Success Animation */
.transaction-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(16, 185, 129, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9998;
    animation: fadeOut 1.5s forwards;
    animation-delay: 1.5s;
    pointer-events: none;
}

.success-checkmark {
    width: 80px;
    height: 80px;
    background-color: #10b981;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: scaleIn 0.5s forwards;
}

.success-checkmark i {
    color: white;
    font-size: 40px;
    animation: fadeIn 0.5s forwards;
    animation-delay: 0.2s;
    opacity: 0;
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes scaleIn {
    from { transform: scale(0); }
    to { transform: scale(1); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); }
    50% { transform: scale(1.05); box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3); }
    100% { transform: scale(1); box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); }
}

.pulse {
    animation: pulse 0.5s cubic-bezier(0.4, 0, 0.6, 1);
}

/* Stock Level Indicator */
.stock-level-indicator {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background-color: #e5e7eb;
    overflow: hidden;
    border-radius: 0 0 0.75rem 0.75rem;
}

.stock-level-bar {
    height: 100%;
    background-color: #10b981;
    transition: width 0.3s ease;
}

.stock-level-bar.low {
    background-color: #ef4444;
}

.stock-level-bar.medium {
    background-color: #f59e0b;
}

/* Improved Cart Item */
.cart-item-enhanced {
    position: relative;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .receipt-paper, .receipt-paper * {
        visibility: visible;
    }

    .receipt-paper {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 1rem;
    }

    .receipt-actions {
        display: none;
    }
}

/* Mobile Optimizations for POS Page */
@media (max-width: 640px) {
    .pos-page-container {
        padding: 0.5rem !important;
        min-height: calc(100vh - 120px) !important;
    }

    .pos-container {
        min-height: calc(100vh - 160px) !important;
    }
}

/* Tablet Optimizations for POS Page */
@media (min-width: 641px) and (max-width: 1024px) {
    .pos-page-container {
        padding: 0.625rem !important;
        min-height: calc(100vh - 130px) !important;
    }

    .pos-container {
        min-height: calc(100vh - 170px) !important;
    }
}
