/* Header Styles for Legend Fitness Club */

.app-header {
    background-color: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 30;
    transition: all 0.3s ease;
    padding: 0.75rem 1rem;
    margin-left: 16rem; /* Match sidebar width */
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e3a8a;
    margin-left: 0.5rem;
}

.header-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    display: none;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Language Switcher Styles */
.language-dropdown {
    margin-right: 0.5rem;
}

.language-dropdown .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    background-color: #f3f4f6;
    color: #1e3a8a;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.language-dropdown .dropdown-toggle:hover {
    background-color: #e5e7eb;
}

.language-dropdown .dropdown-menu {
    min-width: 8rem;
}

.language-dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
}

.language-dropdown .dropdown-item.active {
    background-color: #1e3a8a;
    color: white;
}

.language-dropdown .dropdown-item:hover:not(.active) {
    background-color: #f3f4f6;
    color: #1e3a8a;
}

.lang-flag {
    width: 20px;
    height: 14px;
    margin-right: 8px;
    border-radius: 2px;
    object-fit: cover;
    vertical-align: middle;
}

.header-action-btn {
    background-color: transparent;
    color: #4b5563;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.header-action-btn:hover {
    background-color: #f3f4f6;
    color: #1e3a8a;
}

.header-action-btn i {
    font-size: 1.125rem;
}



.user-dropdown {
    position: relative;
}

.user-dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.user-dropdown-toggle:hover {
    background-color: #f3f4f6;
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #1e3a8a;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
}

.user-info {
    display: none;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #1f2937;
}

.user-role {
    font-size: 0.75rem;
    color: #6b7280;
}

.user-dropdown-menu {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    width: 12rem;
    z-index: 40;
    overflow: hidden;
    display: none;
}

.user-dropdown-menu.show {
    display: block;
    animation: fadeInDown 0.3s ease forwards;
}

.user-dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    text-align: center;
}

.user-dropdown-name {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.user-dropdown-email {
    font-size: 0.75rem;
    color: #6b7280;
}

.user-dropdown-items {
    padding: 0.5rem 0;
}

.user-dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #4b5563;
    transition: all 0.2s ease;
}

.user-dropdown-item:hover {
    background-color: #f3f4f6;
    color: #1e3a8a;
}

.user-dropdown-item i {
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

/* Settings section in dropdown */
.user-dropdown-section {
    padding: 0.5rem 0;
    border-top: 1px solid #e5e7eb;
    border-bottom: 1px solid #e5e7eb;
    margin: 0.5rem 0;
}

.user-dropdown-section-title {
    padding: 0.25rem 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.user-dropdown-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 0.5rem 0;
}

.user-dropdown-logout {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #ef4444;
    transition: all 0.2s ease;
}

.user-dropdown-logout:hover {
    background-color: #fef2f2;
}

/* Responsive styles */
@media (min-width: 768px) {
    .app-header {
        padding: 0.75rem 1.5rem;
    }

    .header-subtitle {
        display: block;
    }

    .user-info {
        display: block;
    }
}

@media (max-width: 640px) {
    .app-header {
        margin-left: 0;
        padding-left: 3.5rem; /* Add space for the sidebar toggle button */
    }

    .header-title {
        font-size: 1.125rem; /* Slightly smaller font on mobile */
    }



    .header-right {
        gap: 0.5rem; /* Reduce gap between elements */
    }
}

/* Animations */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
