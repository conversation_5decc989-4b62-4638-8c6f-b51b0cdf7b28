from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from user.models import User
from django.core.exceptions import ValidationError
import json

# Create your models here.
class SalaryPayment(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('other', _('Other')),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('paid', 'Paid'),
    ]

    EMPLOYMENT_TYPE_CHOICES = [
        ('full_time', 'Full-time'),
        ('part_time', 'Part-time'),
    ]

    payroll_id = models.CharField(_("Payroll ID"), max_length=30, unique=True)
    employee = models.ForeignKey(User, on_delete=models.CASCADE, related_name='payroll_payments')
    month = models.DateField(_("Month & Year"))
    base_salary = models.IntegerField(_("Base Salary"))
    bonus = models.IntegerField(_("Bonus"), default=0)
    deduction = models.IntegerField(_("Deduction"), default=0)
    overtime_hours = models.IntegerField(_("Overtime Hours"), default=0)
    final_pay = models.IntegerField(_("Final Pay"))
    payment_date = models.DateTimeField(_("Payment Date"), null=True, blank=True)
    payment_method = models.CharField(_("Payment Method"), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    payment_status = models.CharField(_("Payment Status"), max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    employment_type = models.CharField(_("Employment Type"), max_length=20, choices=EMPLOYMENT_TYPE_CHOICES, default='full_time')
    notes = models.TextField(_("Notes"), null=True, blank=True)

    def save(self, *args, **kwargs):
        # Calculate final pay if not explicitly set
        if not self.final_pay:
            # No overtime rate calculation anymore
            self.final_pay = self.base_salary + self.bonus - self.deduction

        # Set payment date when status changes to paid
        if self.payment_status == 'paid' and not self.payment_date:
            self.payment_date = timezone.now()

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.payroll_id} - {self.employee.name} ({self.month.strftime('%b %Y')})"

    class Meta:
        ordering = ['-month', 'employee__name']

class SlipTemplate(models.Model):
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('km', 'Khmer'),
        ('both', 'Both (Bilingual)'),
    ]

    name = models.CharField(_("Template Name"), max_length=100)
    is_default = models.BooleanField(_("Default Template"), default=False)
    language = models.CharField(_("Language"), max_length=10, choices=LANGUAGE_CHOICES, default='en')
    header_text = models.CharField(_("Header Text"), max_length=200, default="LEGEND FITNESS")
    subheader_text = models.CharField(_("Subheader Text"), max_length=200, default="Salary Slip")
    footer_text = models.CharField(_("Footer Text"), max_length=200, default="Thank you for your service!")
    company_logo = models.ImageField(_("Company Logo"), upload_to='slip_templates/', null=True, blank=True)
    background_color = models.CharField(_("Background Color"), max_length=20, default="#ffffff")
    text_color = models.CharField(_("Text Color"), max_length=20, default="#000000")
    accent_color = models.CharField(_("Accent Color"), max_length=20, default="#0c4a6e")
    show_company_info = models.BooleanField(_("Show Company Info"), default=True)
    show_signatures = models.BooleanField(_("Show Signature Lines"), default=True)
    custom_css = models.TextField(_("Custom CSS"), blank=True, null=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # If this template is being set as default, unset any other default templates
        if self.is_default:
            SlipTemplate.objects.filter(is_default=True).update(is_default=False)

        # If no default template exists, make this one default
        if not SlipTemplate.objects.filter(is_default=True).exists():
            self.is_default = True

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({'Default' if self.is_default else 'Custom'})"
