# Generated manually

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SalaryPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payroll_id', models.CharField(max_length=30, unique=True, verbose_name='Payroll ID')),
                ('month', models.DateField(verbose_name='Month & Year')),
                ('base_salary', models.IntegerField(verbose_name='Base Salary')),
                ('bonus', models.IntegerField(default=0, verbose_name='Bonus')),
                ('deduction', models.IntegerField(default=0, verbose_name='Deduction')),
                ('overtime_hours', models.IntegerField(default=0, verbose_name='Overtime Hours')),
                ('overtime_rate', models.IntegerField(default=0, verbose_name='Overtime Rate Per Hour')),
                ('final_pay', models.IntegerField(verbose_name='Final Pay')),
                ('payment_date', models.DateTimeField(blank=True, null=True, verbose_name='Payment Date')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('aba', 'ABA'), ('wing', 'Wing'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid')], default='pending', max_length=20, verbose_name='Payment Status')),
                ('employment_type', models.CharField(choices=[('full_time', 'Full-time'), ('part_time', 'Part-time')], default='full_time', max_length=20, verbose_name='Employment Type')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payroll_payments', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-month', 'employee__name'],
            },
        ),
    ]
