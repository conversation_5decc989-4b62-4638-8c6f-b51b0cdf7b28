# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SalaryPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payroll_id', models.CharField(max_length=30, unique=True, verbose_name='Payroll ID')),
                ('month', models.DateField(verbose_name='Month & Year')),
                ('base_salary', models.IntegerField(verbose_name='Base Salary')),
                ('bonus', models.IntegerField(default=0, verbose_name='Bonus')),
                ('deduction', models.IntegerField(default=0, verbose_name='Deduction')),
                ('overtime_hours', models.IntegerField(default=0, verbose_name='Overtime Hours')),
                ('final_pay', models.IntegerField(verbose_name='Final Pay')),
                ('payment_date', models.DateTimeField(blank=True, null=True, verbose_name='Payment Date')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid')], default='pending', max_length=20, verbose_name='Payment Status')),
                ('employment_type', models.CharField(choices=[('full_time', 'Full-time'), ('part_time', 'Part-time')], default='full_time', max_length=20, verbose_name='Employment Type')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
            ],
            options={
                'ordering': ['-month', 'employee__name'],
            },
        ),
        migrations.CreateModel(
            name='SlipTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.CharField(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='Salary Slip', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you for your service!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='slip_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#000000', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#0c4a6e', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=True, verbose_name='Show Signature Lines')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
        ),
    ]
