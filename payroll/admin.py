from django.contrib import admin
from .models import SalaryPayment, SlipTemplate

# Register your models here.
@admin.register(SalaryPayment)
class SalaryPaymentAdmin(admin.ModelAdmin):
    list_display = ('payroll_id', 'employee', 'month', 'base_salary', 'bonus', 'deduction', 'final_pay', 'payment_status')
    list_filter = ('payment_status', 'payment_method', 'month', 'employment_type')
    search_fields = ('payroll_id', 'employee__name', 'employee__username', 'notes')
    date_hierarchy = 'month'

@admin.register(SlipTemplate)
class SlipTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'language', 'is_default', 'created_at', 'updated_at')
    list_filter = ('language', 'is_default')
    search_fields = ('name',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'is_default', 'language')
        }),
        ('Content', {
            'fields': ('header_text', 'subheader_text', 'footer_text', 'company_logo')
        }),
        ('Styling', {
            'fields': ('background_color', 'text_color', 'accent_color', 'custom_css')
        }),
        ('Display Options', {
            'fields': ('show_company_info', 'show_signatures')
        }),
    )
