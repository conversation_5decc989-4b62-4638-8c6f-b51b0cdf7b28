from django.urls import path
from . import views

app_name = "payroll"

urlpatterns = [
    path('', views.index, name='index'),
    path('create/', views.create_salary, name='create_salary'),
    path('process/<int:pk>/', views.process_payment, name='process'),
    path('view/<int:pk>/', views.view_payment, name='view'),
    path('edit/<int:pk>/', views.edit_payment, name='edit'),
    path('delete/<int:pk>/', views.delete_payment, name='delete'),
    path('print/<int:pk>/', views.print_slip, name='print'),
    path('employee/', views.employee_salary, name='employee'),
    path('bulk-actions/', views.bulk_actions, name='bulk_actions'),

    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/edit/<int:pk>/', views.edit_template, name='edit_template'),
    path('templates/delete/<int:pk>/', views.delete_template, name='delete_template'),
    path('templates/preview/<int:pk>/', views.preview_template, name='preview_template'),
]
