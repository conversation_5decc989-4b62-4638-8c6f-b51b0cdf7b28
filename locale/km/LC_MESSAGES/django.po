# Khmer translation for Legend Fitness Club.
# Copyright (C) 2025 Legend Fitness Club
# This file is distributed under the same license as the Legend Fitness Club package.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Legend Fitness Club 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-05-29 16:24+0700\n"
"PO-Revision-Date: 2025-05-15 12:00+0700\n"
"Last-Translator: \n"
"Language-Team: Khmer\n"
"Language: km\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#: .\billmanagement\models.py:12 .\finance\models.py:17 .\payment\models.py:9
#: .\paypervisit\models.py:64 .\payroll\models.py:11 .\product\models.py:146
#: .\templates\paypervisit\index.html:177
#: .\templates\paypervisit\index.html:209
msgid "Cash"
msgstr "សាច់ប្រាក់"

#: .\billmanagement\models.py:13 .\finance\models.py:18 .\payment\models.py:10
#: .\paypervisit\models.py:65 .\payroll\models.py:12 .\product\models.py:147
#: .\templates\paypervisit\index.html:178
msgid "Bank Transfer"
msgstr ""

#: .\billmanagement\models.py:14 .\finance\models.py:19 .\payment\models.py:11
#: .\paypervisit\models.py:66 .\payroll\models.py:13 .\product\models.py:148
#: .\templates\paypervisit\index.html:179
msgid "Other"
msgstr ""

#: .\billmanagement\models.py:39
msgid "Bill ID"
msgstr ""

#: .\billmanagement\models.py:40 .\templates\sidebar.html:224
msgid "Category"
msgstr "ប្រភេទ"

#: .\billmanagement\models.py:41
msgid "Provider/Vendor"
msgstr ""

#: .\billmanagement\models.py:42 .\members\models.py:14 .\product\models.py:9
#: .\product\models.py:31 .\templates\user\admin_action_logs.html:20
#: .\templates\user\admin_action_logs_fixed.html:99
#: .\templates\user\admin_action_logs_fixed.html:123
#: .\templates\user\user_action_logs.html:219 .\user\models.py:182
#: .\user\models.py:351
#, fuzzy
#| msgid "Duration"
msgid "Description"
msgstr "រយៈពេល"

#: .\billmanagement\models.py:43 .\payroll\models.py:28
msgid "Month & Year"
msgstr ""

#: .\billmanagement\models.py:44
#, fuzzy
#| msgid "Payment Method"
msgid "Payment Period"
msgstr "វិធីទូទាត់"

#: .\billmanagement\models.py:45 .\finance\models.py:32 .\payment\models.py:16
#, fuzzy
#| msgid "Amount"
msgid "Amount (KHR)"
msgstr "ចំនួនទឹកប្រាក់"

#: .\billmanagement\models.py:46 .\finance\models.py:33 .\payment\models.py:17
#, fuzzy
#| msgid "Amount"
msgid "Amount (USD)"
msgstr "ចំនួនទឹកប្រាក់"

#: .\billmanagement\models.py:47 .\finance\models.py:34 .\payment\models.py:18
#: .\paypervisit\models.py:73 .\payroll\models.py:35 .\product\models.py:144
#: .\templates\paypervisit\index.html:174
#: .\templates\paypervisit\transaction.html:96
msgid "Payment Method"
msgstr "វិធីទូទាត់"

# Payment-related terms
#: .\billmanagement\models.py:48 .\members\models.py:47 .\payroll\models.py:36
#, fuzzy
#| msgid "Payment Date"
msgid "Payment Status"
msgstr "កាលបរិច្ឆេទទូទាត់"

# Payment-related terms
#: .\billmanagement\models.py:49 .\payment\models.py:19 .\payroll\models.py:34
msgid "Payment Date"
msgstr "កាលបរិច្ឆេទទូទាត់"

#: .\billmanagement\models.py:51
msgid "Recurring Bill"
msgstr ""

#: .\billmanagement\models.py:51
msgid ""
"If checked, a new bill will be created automatically for the next period "
"when this bill is paid."
msgstr ""

#: .\billmanagement\models.py:52 .\finance\models.py:38 .\payment\models.py:21
#: .\payroll\models.py:38 .\product\models.py:80 .\product\models.py:151
#: .\templates\product\purchase_form.html:23
msgid "Notes"
msgstr ""

#: .\billmanagement\models.py:53 .\billmanagement\models.py:99
#: .\finance\models.py:103 .\payment\models.py:49 .\payroll\models.py:78
#: .\product\models.py:49 .\product\models.py:82 .\settings\models.py:71
msgid "Created At"
msgstr ""

#: .\billmanagement\models.py:54 .\billmanagement\models.py:100
#: .\finance\models.py:48 .\finance\models.py:104 .\payment\models.py:50
#: .\payroll\models.py:79 .\product\models.py:50
msgid "Updated At"
msgstr ""

#: .\billmanagement\models.py:72
msgid "Bill"
msgstr ""

#: .\billmanagement\models.py:73
msgid "Bills"
msgstr ""

#: .\billmanagement\models.py:86 .\finance\models.py:90 .\payment\models.py:36
#: .\payroll\models.py:65
msgid "Template Name"
msgstr ""

#: .\billmanagement\models.py:87 .\finance\models.py:91 .\payment\models.py:37
#: .\payroll\models.py:66
msgid "Default Template"
msgstr ""

#: .\billmanagement\models.py:88 .\finance\models.py:92 .\payment\models.py:38
#: .\payroll\models.py:67
msgid "Language"
msgstr "ភាសា"

#: .\billmanagement\models.py:89 .\finance\models.py:93 .\payment\models.py:39
#: .\payroll\models.py:68
msgid "Header Text"
msgstr ""

#: .\billmanagement\models.py:90 .\finance\models.py:94 .\payment\models.py:40
#: .\payroll\models.py:69
msgid "Subheader Text"
msgstr ""

#: .\billmanagement\models.py:91 .\finance\models.py:95 .\payment\models.py:41
#: .\payroll\models.py:70
msgid "Footer Text"
msgstr ""

#: .\billmanagement\models.py:92 .\finance\models.py:96 .\payment\models.py:42
#: .\payroll\models.py:71
msgid "Company Logo"
msgstr ""

#: .\billmanagement\models.py:93 .\finance\models.py:97 .\payment\models.py:43
#: .\payroll\models.py:72
msgid "Background Color"
msgstr ""

#: .\billmanagement\models.py:94 .\finance\models.py:98 .\payment\models.py:44
#: .\payroll\models.py:73
msgid "Text Color"
msgstr ""

#: .\billmanagement\models.py:95 .\finance\models.py:99 .\payment\models.py:45
#: .\payroll\models.py:74
msgid "Accent Color"
msgstr ""

#: .\billmanagement\models.py:96 .\finance\models.py:100 .\payment\models.py:46
#: .\payroll\models.py:75
msgid "Show Company Info"
msgstr ""

#: .\billmanagement\models.py:97
msgid "Show Signatures"
msgstr ""

#: .\billmanagement\models.py:98 .\finance\models.py:102 .\payment\models.py:48
#: .\payroll\models.py:77
msgid "Custom CSS"
msgstr ""

#: .\billmanagement\models.py:117
msgid "Bill Receipt Template"
msgstr ""

#: .\billmanagement\models.py:118
msgid "Bill Receipt Templates"
msgstr ""

#: .\core\settings.py:274 .\finance\models.py:85
msgid "English"
msgstr ""

#: .\core\settings.py:275 .\finance\models.py:86
msgid "Khmer"
msgstr ""

# Login error messages
#: .\core\views.py:77
msgid "Your account has been deactivated. Please contact the administrator."
msgstr "គណនីរបស់អ្នកត្រូវបានបិទ។ សូមទាក់ទងអ្នកគ្រប់គ្រង។"

#: .\core\views.py:80
msgid "Incorrect password"
msgstr "ពាក្យសម្ងាត់មិនត្រឹមត្រូវ"

#: .\core\views.py:91
msgid "User not found"
msgstr "រកមិនឃើញអ្នកប្រើប្រាស់"

#: .\core\views.py:103
msgid "Please enter your username"
msgstr "សូមបញ្ចូលឈ្មោះអ្នកប្រើប្រាស់របស់អ្នក"

#: .\core\views.py:105
msgid "Please enter your password"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\finance\apps.py:8 .\settings\models.py:122
#: .\templates\dashboard\employee.html:453
#, fuzzy
#| msgid "Gym Management System"
msgid "Finance Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\finance\models.py:12 .\templates\dashboard\admin.html:102
#: .\templates\sidebar.html:397
msgid "Deposit"
msgstr ""

#: .\finance\models.py:13
msgid "Withdrawal"
msgstr ""

#: .\finance\models.py:23
msgid "Completed"
msgstr ""

#: .\finance\models.py:24 .\templates\member\index.html:160
#: .\templates\member\index.html:350 .\templates\member\index.html:423
msgid "Pending"
msgstr ""

#: .\finance\models.py:25
msgid "Rejected"
msgstr ""

#: .\finance\models.py:30 .\paypervisit\models.py:69 .\product\models.py:76
#: .\product\models.py:141 .\templates\paypervisit\transaction.html:179
msgid "Transaction ID"
msgstr ""

#: .\finance\models.py:31
msgid "Transaction Type"
msgstr ""

#: .\finance\models.py:39 .\templates\member\index.html:134
#: .\templates\member\index.html:253 .\templates\member\index.html:845
#: .\templates\user\user_action_logs.html:90
#: .\templates\user\user_action_logs.html:161
#: .\templates\user\user_action_logs.html:217 .\user\models.py:335
#, fuzzy
#| msgid "Stock Status"
msgid "Status"
msgstr "ស្ថានភាពស្តុក"

#: .\finance\models.py:47
msgid "Transaction Date"
msgstr ""

#: .\finance\models.py:76
#, fuzzy
#| msgid "Duration"
msgid "Transaction"
msgstr "រយៈពេល"

#: .\finance\models.py:77 .\templates\paypervisit\transaction.html:165
#, fuzzy
#| msgid "Actions"
msgid "Transactions"
msgstr "សកម្មភាព"

#: .\finance\models.py:87
msgid "Both (Bilingual)"
msgstr ""

#: .\finance\models.py:101 .\payment\models.py:47 .\payroll\models.py:76
msgid "Show Signature Lines"
msgstr ""

#: .\finance\models.py:118
#, fuzzy
#| msgid "Export Selected"
msgid "Transaction Template"
msgstr "នាំចេញអ្វីដែលបានជ្រើសរើស"

#: .\finance\models.py:119
#, fuzzy
#| msgid "Reports"
msgid "Transaction Templates"
msgstr "របាយការណ៍"

#: .\financialreport\models.py:33
#, fuzzy
#| msgid "Export Selected"
msgid "Report Template"
msgstr "នាំចេញអ្វីដែលបានជ្រើសរើស"

#: .\financialreport\models.py:34
#, fuzzy
#| msgid "Reports"
msgid "Report Templates"
msgstr "របាយការណ៍"

#: .\members\models.py:5
#, fuzzy
#| msgid "Packages"
msgid "Package ID"
msgstr "កញ្ចប់"

#: .\members\models.py:6 .\members\models.py:37 .\product\models.py:8
#: .\product\models.py:18 .\product\models.py:28
#: .\templates\member\index.html:247 .\templates\member\index.html:839
msgid "Name"
msgstr "ឈ្មោះ"

#: .\members\models.py:7
#, fuzzy
#| msgid "Duration"
msgid "Duration (Months)"
msgstr "រយៈពេល"

#: .\members\models.py:8
#, fuzzy
#| msgid "Price"
msgid "Price (KHR)"
msgstr "តម្លៃ"

#: .\members\models.py:9
#, fuzzy
#| msgid "Price"
msgid "Price (USD)"
msgstr "តម្លៃ"

#: .\members\models.py:10
msgid "Access Type"
msgstr ""

# Member-related terms
#: .\members\models.py:36 .\templates\member\index.html:246
#: .\templates\member\index.html:838
msgid "Member ID"
msgstr "លេខសម្គាល់សមាជិក"

#: .\members\models.py:38 .\templates\member\index.html:245 .\user\models.py:64
msgid "Photo"
msgstr ""

#: .\members\models.py:39 .\user\models.py:50
msgid "Gender"
msgstr "ភេទ"

#: .\members\models.py:40 .\user\models.py:49
msgid "Date of Birth"
msgstr "ថ្ងៃខែឆ្នាំកំណើត"

#: .\members\models.py:41 .\templates\member\index.html:248
#: .\templates\member\index.html:840
msgid "Contact"
msgstr ""

#: .\members\models.py:42 .\product\models.py:20
msgid "Telegram/Messenger"
msgstr ""

#: .\members\models.py:43 .\product\models.py:21 .\settings\models.py:17
#: .\user\models.py:61
msgid "Address"
msgstr "អាសយដ្ឋាន"

#: .\members\models.py:45 .\templates\member\index.html:250
#: .\templates\member\index.html:842
#, fuzzy
#| msgid "Sale Date"
msgid "Start Date"
msgstr "កាលបរិច្ឆេទលក់"

#: .\members\models.py:46 .\templates\member\index.html:251
#: .\templates\member\index.html:843
#, fuzzy
#| msgid "Join Date"
msgid "End Date"
msgstr "ថ្ងៃចូលរួម"

#: .\members\models.py:48
msgid "Discount Amount"
msgstr ""

#: .\members\models.py:49
#, fuzzy
#| msgid "Payment"
msgid "Due Payment"
msgstr "ការទូទាត់"

#: .\members\models.py:51 .\user\models.py:73
msgid "Active Status"
msgstr "សកម្ម"

#: .\payment\models.py:14
#, fuzzy
#| msgid "Invoice"
msgid "Invoice No."
msgstr "វិក្កយបត្រ"

#: .\paypervisit\models.py:7
msgid "Price Per Person"
msgstr ""

#: .\paypervisit\models.py:10
msgid "Quick Selection 1"
msgstr ""

#: .\paypervisit\models.py:11
msgid "Quick Selection 2"
msgstr ""

#: .\paypervisit\models.py:12
msgid "Quick Selection 3"
msgstr ""

#: .\paypervisit\models.py:15
msgid "Price For 2 People"
msgstr ""

#: .\paypervisit\models.py:16
msgid "Price For 5 People"
msgstr ""

#: .\paypervisit\models.py:17
msgid "Price For 10 People"
msgstr ""

#: .\paypervisit\models.py:20
msgid "Custom Price 1"
msgstr ""

#: .\paypervisit\models.py:21
msgid "Custom Price 2"
msgstr ""

#: .\paypervisit\models.py:22
msgid "Custom Price 3"
msgstr ""

#: .\paypervisit\models.py:24 .\settings\models.py:70
msgid "Last Updated"
msgstr ""

#: .\paypervisit\models.py:70 .\templates\paypervisit\transaction.html:181
msgid "Amount"
msgstr "ចំនួនទឹកប្រាក់"

#: .\paypervisit\models.py:71 .\templates\paypervisit\index.html:68
msgid "Number of People"
msgstr ""

#: .\paypervisit\models.py:72 .\product\models.py:78 .\product\models.py:142
#, fuzzy
#| msgid "Join Date"
msgid "Date"
msgstr "ថ្ងៃចូលរួម"

#: .\payroll\models.py:26
msgid "Payroll ID"
msgstr ""

#: .\payroll\models.py:29
#, fuzzy
#| msgid "Salary"
msgid "Base Salary"
msgstr "ប្រាក់ខែ"

#: .\payroll\models.py:30
msgid "Bonus"
msgstr ""

#: .\payroll\models.py:31
#, fuzzy
#| msgid "Duration"
msgid "Deduction"
msgstr "រយៈពេល"

#: .\payroll\models.py:32
msgid "Overtime Hours"
msgstr ""

#: .\payroll\models.py:33
msgid "Final Pay"
msgstr ""

#: .\payroll\models.py:37
msgid "Employment Type"
msgstr ""

#: .\product\models.py:19 .\user\models.py:47
msgid "Phone"
msgstr ""

#: .\product\models.py:22
msgid "Note"
msgstr ""

#: .\product\models.py:29
msgid "SKU"
msgstr "លេខកូដផលិតផល"

#: .\product\models.py:32
#: .\venv\Lib\site-packages\django\db\models\fields\files.py:393
msgid "Image"
msgstr "រូបភាព"

#: .\product\models.py:35 .\product\models.py:92
#, fuzzy
#| msgid "Price"
msgid "Cost Price"
msgstr "តម្លៃ"

#: .\product\models.py:36
msgid "Retail Price"
msgstr "តម្លៃលក់រាយ"

#: .\product\models.py:39 .\product\models.py:91 .\product\models.py:159
#: .\templates\product\purchase_form.html:64
#: .\templates\product\purchase_form.html:65
msgid "Quantity"
msgstr "បរិមាណ"

#: .\product\models.py:42 .\product\models.py:94 .\product\models.py:162
#, fuzzy
#| msgid "Quantity"
msgid "Box Quantity"
msgstr "បរិមាណ"

#: .\product\models.py:43 .\templates\product\purchase_form.html:73
msgid "Box Cost"
msgstr ""

#: .\product\models.py:46 .\templates\member\index.html:137
#: .\templates\member\index.html:341 .\templates\member\index.html:417
msgid "Active"
msgstr "សកម្ម"

#: .\product\models.py:79 .\product\models.py:143
#, fuzzy
#| msgid "Amount"
msgid "Total Amount"
msgstr "ចំនួនទឹកប្រាក់"

#: .\product\models.py:93
#, fuzzy
#| msgid "Purchase Date"
msgid "Box Purchase"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\product\models.py:95
#, fuzzy
#| msgid "Quantity"
msgid "Original Quantity"
msgstr "បរិមាណ"

#: .\product\models.py:96
msgid "Stored Box Cost"
msgstr ""

#: .\product\models.py:160
msgid "Price"
msgstr "តម្លៃ"

#: .\product\models.py:161
msgid "Box Equivalent"
msgstr ""

#: .\settings\models.py:14
#, fuzzy
#| msgid "Name"
msgid "Gym Name"
msgstr "ឈ្មោះ"

#: .\settings\models.py:15
msgid "Contact Email"
msgstr ""

#: .\settings\models.py:16
msgid "Contact Phone"
msgstr ""

#: .\settings\models.py:21 .\user\models.py:147
#, fuzzy
#| msgid "Auto-deactivated (Out of Stock)"
msgid "Auto-deactivate Out of Stock Products"
msgstr "បិទដោយស្វ័យប្រវត្តិ (អស់ស្តុក)"

#: .\settings\models.py:23 .\user\models.py:148
msgid "Automatically deactivate products when they go out of stock"
msgstr ""

#: .\settings\models.py:26 .\user\models.py:149
#, fuzzy
#| msgid "Auto-deactivated (Out of Stock)"
msgid "Auto-reactivate In Stock Products"
msgstr "បិទដោយស្វ័យប្រវត្តិ (អស់ស្តុក)"

#: .\settings\models.py:28 .\user\models.py:150
msgid "Automatically reactivate products when they come back in stock"
msgstr ""

#: .\settings\models.py:31 .\user\models.py:153
msgid "Default Items Per Page"
msgstr ""

#: .\settings\models.py:33
msgid "Default number of items to show per page in lists"
msgstr ""

#: .\settings\models.py:37
#, fuzzy
#| msgid "Pay-per-visit Settings"
msgid "Pay-per-visit Price Per Person"
msgstr "ការកំណត់បង់ប្រាក់តាមការចូល"

#: .\settings\models.py:38
msgid "Quick Selection 1 People Count"
msgstr ""

#: .\settings\models.py:39
msgid "Quick Selection 2 People Count"
msgstr ""

#: .\settings\models.py:40
msgid "Quick Selection 3 People Count"
msgstr ""

#: .\settings\models.py:41
msgid "Custom Price for Quick Selection 1"
msgstr ""

#: .\settings\models.py:42
msgid "Custom Price for Quick Selection 2"
msgstr ""

#: .\settings\models.py:43
msgid "Custom Price for Quick Selection 3"
msgstr ""

#: .\settings\models.py:46
msgid "Success Notification Color"
msgstr ""

#: .\settings\models.py:47
msgid "Error Notification Color"
msgstr ""

#: .\settings\models.py:48
msgid "Warning Notification Color"
msgstr ""

#: .\settings\models.py:49
msgid "Info Notification Color"
msgstr ""

#: .\settings\models.py:50
msgid "Notification Text Color"
msgstr ""

#: .\settings\models.py:53
msgid "Default Currency"
msgstr ""

#: .\settings\models.py:54
msgid "Cambodian Riel (៛)"
msgstr ""

#: .\settings\models.py:55
msgid "US Dollar ($)"
msgstr ""

#: .\settings\models.py:58
msgid "Exchange Rate (USD to KHR)"
msgstr ""

#: .\settings\models.py:60
msgid ""
"Exchange rate for converting between USD and KHR (e.g., 4000 means 1 USD = "
"4,000 KHR)"
msgstr ""

#: .\settings\models.py:64
msgid "Last Backup Date"
msgstr ""

#: .\settings\models.py:65
msgid "Last Data Cleanup"
msgstr ""

#: .\settings\models.py:68 .\user\models.py:144
msgid "Funds"
msgstr ""

#: .\settings\models.py:68
msgid "Current funds in the system"
msgstr ""

#: .\settings\models.py:69 .\user\models.py:143
#, fuzzy
#| msgid "Last Week"
msgid "Last Checked"
msgstr "សប្តាហ៍មុន"

#: .\settings\models.py:74 .\settings\models.py:75 .\settings\models.py:125
msgid "Settings"
msgstr "ការកំណត់"

#: .\settings\models.py:113 .\templates\sidebar.html:41
#: .\templates\sidebar.html:434 .\templates\user\change_password.html:357
msgid "Dashboard"
msgstr "ផ្ទាំងគ្រប់គ្រង"

#: .\settings\models.py:114 .\templates\dashboard\employee.html:373
#: .\templates\member\index.html:97
#, fuzzy
#| msgid "Gym Management System"
msgid "Member Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\settings\models.py:115 .\templates\dashboard\employee.html:437
#, fuzzy
#| msgid "Payment Method"
msgid "Payment Processing"
msgstr "វិធីទូទាត់"

#: .\settings\models.py:116
#, fuzzy
#| msgid "Gym Management System"
msgid "Payroll Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

# Product-related terms
#: .\settings\models.py:117 .\templates\dashboard\employee.html:405
#, fuzzy
#| msgid "Product Name"
msgid "Product Management"
msgstr "ឈ្មោះផលិតផល"

#: .\settings\models.py:118 .\templates\dashboard\employee.html:421
#, fuzzy
#| msgid "Gym Management System"
msgid "Purchase Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\settings\models.py:119
msgid "Point of Sale"
msgstr ""

#: .\settings\models.py:120 .\templates\dashboard\employee.html:389
#: .\templates\sidebar.html:54 .\templates\sidebar.html:272
#: .\templates\sidebar.html:447
msgid "Pay-per-visit"
msgstr "បង់ប្រាក់ច្រចាំថ្ងៃ"

#: .\settings\models.py:121 .\templates\dashboard\employee.html:469
#: .\templates\sidebar.html:296
#, fuzzy
#| msgid "Gym Management System"
msgid "Bill Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\settings\models.py:123 .\templates\sidebar.html:316
#, fuzzy
#| msgid "Financial Report"
msgid "Financial Reports"
msgstr "របាយការណ៍ហិរញ្ញវត្ថុ"

#: .\settings\models.py:124
#, fuzzy
#| msgid "Gym Management System"
msgid "User Management"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\settings\models.py:130 .\templates\dashboard\employee.html:485
#: .\templates\paypervisit\transaction.html:224
msgid "No Access"
msgstr ""

#: .\settings\models.py:131
msgid "View Only"
msgstr ""

#: .\settings\models.py:132
msgid "View and Edit"
msgstr ""

#: .\settings\models.py:133
#, fuzzy
#| msgid "Full Name"
msgid "Full Access"
msgstr "ឈ្មោះពេញ"

# Staff-related terms
#: .\settings\models.py:140 .\user\models.py:65
msgid "Role"
msgstr "តួនាទី"

#: .\settings\models.py:141 .\templates\user\user_action_logs.html:84
#: .\templates\user\user_action_logs.html:146
#: .\templates\user\user_action_logs.html:216 .\user\models.py:333
msgid "Module"
msgstr ""

#: .\settings\models.py:142
msgid "Permission Level"
msgstr ""

#: .\settings\models.py:145
msgid "Role Permission"
msgstr ""

#: .\settings\models.py:146
msgid "Role Permissions"
msgstr ""

#: .\templates\auth\login.html:10 .\templates\auth\login.html:164
#: .\templates\paypervisit\index.html:191
msgid "Legend Fitness Club"
msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#: .\templates\auth\login.html:158
#, fuzzy
#| msgid "Legend Fitness Club"
msgid "Legend Fitness Club Logo"
msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#: .\templates\auth\login.html:167
msgid "Gym Management System"
msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#: .\templates\auth\login.html:170
msgid "ប្រព័ន្ធគ្រប់គ្រង​ ក្លឹបហាត់ប្រាណ"
msgstr ""

#: .\templates\auth\login.html:177
msgid "Professional gym management made simple and efficient"
msgstr ""

#: .\templates\auth\login.html:192
msgid "Welcome Back"
msgstr ""

#: .\templates\auth\login.html:195
msgid "Please sign in to your account"
msgstr ""

#: .\templates\auth\login.html:218
msgid "Error"
msgstr ""

#: .\templates\auth\login.html:220
#, fuzzy
#| msgid "Full Name"
msgid "Success"
msgstr "ឈ្មោះពេញ"

#: .\templates\auth\login.html:222
msgid "Warning"
msgstr ""

#: .\templates\auth\login.html:224
#, fuzzy
#| msgid "Duration"
msgid "Information"
msgstr "រយៈពេល"

#: .\templates\auth\login.html:234
#, fuzzy
#| msgid "Username"
msgid "Username"
msgstr "ឈ្មោះអ្នកប្រើប្រាស់"

#: .\templates\auth\login.html:245
msgid "Enter your username"
msgstr ""

#: .\templates\auth\login.html:254
#, fuzzy
#| msgid "Password"
msgid "Password"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\auth\login.html:265
msgid "Enter your password"
msgstr ""

#: .\templates\auth\login.html:272
msgid "Toggle password visibility"
msgstr ""

#: .\templates\auth\login.html:284
msgid "Sign In"
msgstr ""

#: .\templates\auth\login.html:315 .\templates\user\change_password.html:579
#, fuzzy
#| msgid "Password"
msgid "Hide password"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\auth\login.html:320 .\templates\user\change_password.html:584
#, fuzzy
#| msgid "Password"
msgid "Show password"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\dashboard\admin.html:26
msgid "Quick Payment"
msgstr "ការទូទាត់រហ័ស"

#: .\templates\dashboard\admin.html:38
#, fuzzy
#| msgid "Pay-per-visit"
msgid "Pay-Per-Visit"
msgstr "បង់ប្រាក់ច្រចាំថ្ងៃ"

#: .\templates\dashboard\admin.html:39
msgid "Process visitor payments"
msgstr ""

#: .\templates\dashboard\admin.html:54 .\templates\sidebar.html:68
#: .\templates\sidebar.html:190 .\templates\sidebar.html:461
msgid "POS"
msgstr "ការលក់ផលិតផល"

#: .\templates\dashboard\admin.html:55
msgid "Point of Sale system"
msgstr ""

#: .\templates\dashboard\admin.html:70
#, fuzzy
#| msgid "Payment Method"
msgid "Payment Member"
msgstr "វិធីទូទាត់"

#: .\templates\dashboard\admin.html:71
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process membership payments"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\dashboard\admin.html:91
#, fuzzy
#| msgid "Cancel"
msgid "Quick Finance"
msgstr "បោះបង់"

#: .\templates\dashboard\admin.html:103
#, fuzzy
#| msgid "Duration"
msgid "Add income transaction"
msgstr "រយៈពេល"

#: .\templates\dashboard\admin.html:116 .\templates\sidebar.html:406
msgid "Withdraw"
msgstr ""

#: .\templates\dashboard\admin.html:117
msgid "Add expense transaction"
msgstr ""

#: .\templates\dashboard\admin.html:135
msgid "Quick Reports"
msgstr "របាយការណ៍រហ័ស"

#: .\templates\dashboard\admin.html:143
#, fuzzy
#| msgid "Payment Report"
msgid "Pay-Per-Visit Report"
msgstr "របាយការណ៍បង់ប្រាក់តាមការចូល"

#: .\templates\dashboard\admin.html:151 .\templates\dashboard\admin.html:174
#: .\templates\dashboard\admin.html:197 .\templates\dashboard\admin.html:220
#: .\templates\dashboard\admin.html:243 .\templates\dashboard\admin.html:266
msgid "Loading..."
msgstr "កំពុងផ្ទុក..."

#: .\templates\dashboard\admin.html:156 .\templates\dashboard\admin.html:179
#: .\templates\dashboard\admin.html:202 .\templates\dashboard\admin.html:225
#: .\templates\dashboard\admin.html:248 .\templates\dashboard\admin.html:271
#, fuzzy
#| msgid "Days"
msgid "Day"
msgstr "ថ្ងៃ"

#: .\templates\dashboard\admin.html:157 .\templates\dashboard\admin.html:180
#: .\templates\dashboard\admin.html:203 .\templates\dashboard\admin.html:226
#: .\templates\dashboard\admin.html:249 .\templates\dashboard\admin.html:272
#, fuzzy
#| msgid "This Week"
msgid "Week"
msgstr "សប្តាហ៍នេះ"

#: .\templates\dashboard\admin.html:158 .\templates\dashboard\admin.html:181
#: .\templates\dashboard\admin.html:204 .\templates\dashboard\admin.html:227
#: .\templates\dashboard\admin.html:250 .\templates\dashboard\admin.html:273
#, fuzzy
#| msgid "This Month"
msgid "Month"
msgstr "ខែនេះ"

#: .\templates\dashboard\admin.html:166
#, fuzzy
#| msgid "Product Category"
msgid "Product Sales Report"
msgstr "របាយការណ៍ការលក់ផលិតផល"

#: .\templates\dashboard\admin.html:189
#, fuzzy
#| msgid "Member Report"
msgid "Member Report"
msgstr "របាយការណ៍ចំណូល"

#: .\templates\dashboard\admin.html:212 .\templates\sidebar.html:337
#, fuzzy
#| msgid "Member Report"
msgid "Income Report"
msgstr "របាយការណ៍ចំណូល"

#: .\templates\dashboard\admin.html:235
#, fuzzy
#| msgid "Member Report"
msgid "Expense Report"
msgstr "របាយការណ៍ចំណាយ"

#: .\templates\dashboard\admin.html:258 .\templates\sidebar.html:355
#, fuzzy
#| msgid "Financial Report"
msgid "Balance Report"
msgstr "របាយការណ៍សមតុល្យ"

#: .\templates\dashboard\admin.html:276
msgid "Neutral Trend"
msgstr "ទំនោរអព្យាក្រឹត"

#: .\templates\dashboard\employee.html:274
msgid "Welcome back"
msgstr ""

#: .\templates\dashboard\employee.html:278
msgid "Your role"
msgstr ""

# Time-related terms
#: .\templates\dashboard\employee.html:283
msgid "Today"
msgstr "ថ្ងៃនេះ"

#: .\templates\dashboard\employee.html:299
#, fuzzy
#| msgid "Bulk Actions"
msgid "Quick Actions"
msgstr "សកម្មភាពច្រើន"

#: .\templates\dashboard\employee.html:309
msgid "New Visit"
msgstr ""

#: .\templates\dashboard\employee.html:310
msgid "Register walk-in"
msgstr ""

#: .\templates\dashboard\employee.html:323
msgid "POS System"
msgstr ""

#: .\templates\dashboard\employee.html:324
#, fuzzy
#| msgid "Products"
msgid "Sell products"
msgstr "ផលិតផល"

#: .\templates\dashboard\employee.html:337
#, fuzzy
#| msgid "Payment"
msgid "New Payment"
msgstr "ការទូទាត់"

#: .\templates\dashboard\employee.html:338
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process payment"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\dashboard\employee.html:351
#, fuzzy
#| msgid "Active Member"
msgid "New Member"
msgstr "សកម្ម"

#: .\templates\dashboard\employee.html:352
#, fuzzy
#| msgid "Remember me"
msgid "Register member"
msgstr "ចងចាំខ្ញុំ"

#: .\templates\dashboard\employee.html:363
#, fuzzy
#| msgid "Full Name"
msgid "Quick Access"
msgstr "ឈ្មោះពេញ"

#: .\templates\dashboard\employee.html:375
msgid "Manage gym members, memberships, and member information"
msgstr ""

#: .\templates\dashboard\employee.html:378
#, fuzzy
#| msgid "Inactive Member"
msgid "Manage Members"
msgstr "អសកម្ម"

#: .\templates\dashboard\employee.html:391
msgid "Process daily gym visits and manage walk-in customers"
msgstr ""

#: .\templates\dashboard\employee.html:394
#, fuzzy
#| msgid "Manager"
msgid "Manage Visits"
msgstr "អ្នកគ្រប់គ្រង"

#: .\templates\dashboard\employee.html:407
msgid "Manage gym products, inventory, and sales"
msgstr ""

#: .\templates\dashboard\employee.html:410
#, fuzzy
#| msgid "Products"
msgid "Manage Products"
msgstr "ផលិតផល"

#: .\templates\dashboard\employee.html:423
msgid "Record and track product purchases from suppliers"
msgstr ""

#: .\templates\dashboard\employee.html:426
#, fuzzy
#| msgid "Purchase Date"
msgid "Manage Purchases"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\templates\dashboard\employee.html:439
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process member payments and manage payment records"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\dashboard\employee.html:442
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process Payments"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\dashboard\employee.html:455
msgid "View financial transactions and manage gym finances"
msgstr ""

#: .\templates\dashboard\employee.html:458
#, fuzzy
#| msgid "Actions"
msgid "View Transactions"
msgstr "សកម្មភាព"

#: .\templates\dashboard\employee.html:471
msgid "Manage gym bills and expense tracking"
msgstr ""

#: .\templates\dashboard\employee.html:474
#, fuzzy
#| msgid "Manager"
msgid "Manage Bills"
msgstr "អ្នកគ្រប់គ្រង"

#: .\templates\dashboard\employee.html:487
msgid ""
"Your role doesn't have access to any modules. Please contact an "
"administrator for assistance."
msgstr ""

#: .\templates\member\index.html:98
msgid "Manage gym members and their memberships"
msgstr ""

#: .\templates\member\index.html:112
#, fuzzy
#| msgid "Search Items"
msgid "Search Members"
msgstr "ស្វែងរក"

#: .\templates\member\index.html:117
msgid "Search by name, ID, phone, or status..."
msgstr ""

#: .\templates\member\index.html:123 .\templates\member\index.html:249
#: .\templates\member\index.html:433 .\templates\member\index.html:841
#, fuzzy
#| msgid "Packages"
msgid "Package"
msgstr "កញ្ចប់"

#: .\templates\member\index.html:125
#, fuzzy
#| msgid "Packages"
msgid "All Packages"
msgstr "កញ្ចប់"

#: .\templates\member\index.html:136 .\templates\user\user_action_logs.html:163
#, fuzzy
#| msgid "Stock Status"
msgid "All Status"
msgstr "ស្ថានភាពស្តុក"

#: .\templates\member\index.html:138 .\templates\member\index.html:343
#: .\templates\member\index.html:419
#, fuzzy
#| msgid "Set Inactive"
msgid "Inactive"
msgstr "កំណត់ឱ្យអសកម្ម"

#: .\templates\member\index.html:144 .\templates\member\index.html:252
#: .\templates\member\index.html:446 .\templates\member\index.html:844
#, fuzzy
#| msgid "Duration"
msgid "Expiration"
msgstr "រយៈពេល"

#: .\templates\member\index.html:146
msgid "All"
msgstr ""

#: .\templates\member\index.html:147 .\templates\member\index.html:317
#: .\templates\member\index.html:450
#, fuzzy
#| msgid "Expiry Date"
msgid "Expired"
msgstr "ថ្ងៃផុតកំណត់"

#: .\templates\member\index.html:148
msgid "Critical (≤7 days)"
msgstr ""

#: .\templates\member\index.html:149
msgid "Warning (≤30 days)"
msgstr ""

#: .\templates\member\index.html:150
msgid "Good (>30 days)"
msgstr ""

#: .\templates\member\index.html:156 .\templates\member\index.html:254
#: .\templates\member\index.html:475 .\templates\member\index.html:846
#, fuzzy
#| msgid "Payments"
msgid "Payment"
msgstr "ការទូទាត់"

#: .\templates\member\index.html:158
#, fuzzy
#| msgid "Payments"
msgid "All Payments"
msgstr "ការទូទាត់"

#: .\templates\member\index.html:159 .\templates\member\index.html:352
#: .\templates\member\index.html:425
msgid "Paid"
msgstr ""

#: .\templates\member\index.html:167 .\templates\paypervisit\index.html:96
#: .\templates\paypervisit\index.html:238
#: .\templates\paypervisit\index.html:265
#: .\templates\user\admin_action_logs_fixed.html:51
#: .\venv\Lib\site-packages\django\forms\widgets.py:461
#, fuzzy
#| msgid "Cleaner"
msgid "Clear"
msgstr "អ្នកសម្អាត"

#: .\templates\member\index.html:174 .\templates\member\index.html:546
#: .\templates\paypervisit\transaction.html:284
#: .\templates\user\user_action_logs.html:101
#: .\templates\user\user_action_logs.html:204
#: .\templates\user\user_action_logs.html:337
msgid "Showing"
msgstr ""

#: .\templates\member\index.html:174 .\templates\member\index.html:440
#: .\templates\member\index.html:546
#: .\templates\paypervisit\transaction.html:284
#: .\templates\user\user_action_logs.html:337
msgid "to"
msgstr ""

#: .\templates\member\index.html:174 .\templates\member\index.html:525
#: .\templates\member\index.html:546
#: .\templates\paypervisit\transaction.html:263
#: .\templates\paypervisit\transaction.html:284
#: .\templates\user\user_action_logs.html:204
#: .\templates\user\user_action_logs.html:316
#: .\templates\user\user_action_logs.html:337
msgid "of"
msgstr ""

#: .\templates\member\index.html:174
#, fuzzy
#| msgid "Members"
msgid "members"
msgstr "សមាជិក"

#: .\templates\member\index.html:177 .\templates\member\index.html:798
#, fuzzy
#| msgid "Coach"
msgid "Card View"
msgstr "គ្រូបង្វឹក"

#: .\templates\member\index.html:202
msgid "Memberships Expiring Soon"
msgstr ""

#: .\templates\member\index.html:211 .\templates\member\index.html:321
#: .\templates\member\index.html:454
#, fuzzy
#| msgid "Expiry Date"
msgid "Expires today"
msgstr "ថ្ងៃផុតកំណត់"

# Time-related terms
#: .\templates\member\index.html:215 .\templates\member\index.html:219
#: .\templates\member\index.html:317 .\templates\member\index.html:325
#: .\templates\member\index.html:329 .\templates\member\index.html:333
#: .\templates\member\index.html:450 .\templates\member\index.html:458
#: .\templates\member\index.html:462 .\templates\member\index.html:466
#, fuzzy
#| msgid "Today"
msgid "day"
msgstr "ថ្ងៃនេះ"

#: .\templates\member\index.html:215 .\templates\member\index.html:219
#: .\templates\member\index.html:325 .\templates\member\index.html:329
#: .\templates\member\index.html:333 .\templates\member\index.html:458
#: .\templates\member\index.html:462 .\templates\member\index.html:466
msgid "left"
msgstr ""

#: .\templates\member\index.html:222 .\templates\member\index.html:372
#: .\templates\member\index.html:489
msgid "View"
msgstr ""

#: .\templates\member\index.html:239
#, fuzzy
#| msgid "Members"
msgid "Members List"
msgstr "សមាជិក"

#: .\templates\member\index.html:255 .\templates\member\index.html:847
msgid "Due/Advanced"
msgstr ""

#: .\templates\member\index.html:256
#: .\templates\paypervisit\transaction.html:184
#, fuzzy
#| msgid "Actions"
msgid "Actions"
msgstr "សកម្មភាព"

#: .\templates\member\index.html:300 .\templates\member\index.html:435
#, fuzzy
#| msgid "This Month"
msgid "months"
msgstr "ខែនេះ"

#: .\templates\member\index.html:317 .\templates\member\index.html:450
msgid "ago"
msgstr ""

#: .\templates\member\index.html:360 .\templates\member\index.html:479
msgid "Advanced"
msgstr ""

#: .\templates\member\index.html:364 .\templates\member\index.html:483
msgid "Due"
msgstr ""

#: .\templates\member\index.html:438
#, fuzzy
#| msgid "Payment Method"
msgid "Period"
msgstr "វិធីទូទាត់"

#: .\templates\member\index.html:503
msgid "No members found"
msgstr ""

#: .\templates\member\index.html:504
msgid "Try adjusting your search criteria or filters"
msgstr ""

#: .\templates\member\index.html:516 .\templates\member\index.html:520
#: .\templates\member\index.html:583 .\templates\member\index.html:588
#: .\templates\user\user_action_logs.html:307
#: .\templates\user\user_action_logs.html:311
#: .\templates\user\user_action_logs.html:375
#: .\templates\user\user_action_logs.html:380
msgid "Previous"
msgstr ""

#: .\templates\member\index.html:525
#: .\templates\paypervisit\transaction.html:263
#: .\templates\user\user_action_logs.html:316
msgid "Page"
msgstr ""

#: .\templates\member\index.html:531 .\templates\member\index.html:535
#: .\templates\member\index.html:614 .\templates\member\index.html:619
#: .\templates\user\user_action_logs.html:322
#: .\templates\user\user_action_logs.html:326
#: .\templates\user\user_action_logs.html:406
#: .\templates\user\user_action_logs.html:411
msgid "Next"
msgstr ""

#: .\templates\member\index.html:546
#: .\templates\paypervisit\transaction.html:284
#: .\templates\user\user_action_logs.html:204
#: .\templates\user\user_action_logs.html:337
msgid "entries"
msgstr ""

#: .\templates\member\index.html:550 .\templates\user\user_action_logs.html:341
msgid "Items per page"
msgstr ""

#: .\templates\member\index.html:568 .\templates\user\user_action_logs.html:360
msgid "First page"
msgstr ""

#: .\templates\member\index.html:569 .\templates\member\index.html:574
#: .\templates\user\user_action_logs.html:361
#: .\templates\user\user_action_logs.html:366
msgid "First"
msgstr ""

#: .\templates\member\index.html:628 .\templates\user\user_action_logs.html:420
#, fuzzy
#| msgid "Last Year"
msgid "Last page"
msgstr "ឆ្នាំមុន"

#: .\templates\member\index.html:629 .\templates\member\index.html:634
#: .\templates\user\user_action_logs.html:421
#: .\templates\user\user_action_logs.html:426
#, fuzzy
#| msgid "Last Year"
msgid "Last"
msgstr "ឆ្នាំមុន"

#: .\templates\member\index.html:642 .\templates\user\user_action_logs.html:434
msgid "Go to page"
msgstr ""

#: .\templates\member\index.html:645
#: .\templates\paypervisit\transaction.html:384
#: .\templates\user\user_action_logs.html:437
msgid "Go"
msgstr ""

#: .\templates\member\index.html:804
#, fuzzy
#| msgid "Coach"
msgid "Table View"
msgstr "គ្រូបង្វឹក"

#: .\templates\member\index.html:832
msgid "No data to export"
msgstr ""

#: .\templates\paypervisit\index.html:22
#, fuzzy
#| msgid "Pay-per-visit"
msgid "Pay-per-visit POS"
msgstr "បង់ប្រាក់ច្រចាំថ្ងៃ"

#: .\templates\paypervisit\index.html:25 .\templates\sidebar.html:416
msgid "Transaction History"
msgstr ""

#: .\templates\paypervisit\index.html:28
#, fuzzy
#| msgid "Settings"
msgid "Price Settings"
msgstr "ការកំណត់"

#: .\templates\paypervisit\index.html:35
#, fuzzy
#| msgid "Bulk Actions"
msgid "Quick Selection"
msgstr "សកម្មភាពច្រើន"

#: .\templates\paypervisit\index.html:39
msgid "Person"
msgstr ""

#: .\templates\paypervisit\index.html:43 .\templates\paypervisit\index.html:47
#: .\templates\paypervisit\index.html:51
msgid "People"
msgstr ""

#: .\templates\paypervisit\index.html:58
#, fuzzy
#| msgid "Phone Number"
msgid "Custom Number"
msgstr "លេខទូរស័ព្ទ"

#: .\templates\paypervisit\index.html:62 .\templates\paypervisit\index.html:118
msgid "Number of Visitors"
msgstr ""

#: .\templates\paypervisit\index.html:77
msgid "Base rate:"
msgstr ""

#: .\templates\paypervisit\index.html:77 .\templates\paypervisit\index.html:218
msgid "per person"
msgstr ""

#: .\templates\paypervisit\index.html:83
msgid "Number Pad"
msgstr ""

#: .\templates\paypervisit\index.html:100
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process Payment"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

# Payment-related terms
#: .\templates\paypervisit\index.html:112
#, fuzzy
#| msgid "Payment Date"
msgid "Payment Summary"
msgstr "កាលបរិច្ឆេទទូទាត់"

#: .\templates\paypervisit\index.html:121
#: .\templates\paypervisit\index.html:214
#: .\templates\paypervisit\index.html:406
msgid "person"
msgstr ""

#: .\templates\paypervisit\index.html:127
msgid "Rate per Person"
msgstr ""

#: .\templates\paypervisit\index.html:140
#, fuzzy
#| msgid "Amount"
msgid "Total Amount:"
msgstr "ចំនួនទឹកប្រាក់"

#: .\templates\paypervisit\index.html:146
msgid "This is the total amount to be paid for all visitors"
msgstr ""

# Payment-related terms
#: .\templates\paypervisit\index.html:159
#, fuzzy
#| msgid "Payment Date"
msgid "Payment Details"
msgstr "កាលបរិច្ឆេទទូទាត់"

#: .\templates\paypervisit\index.html:161
msgid "Enter the number of visitors and process payment"
msgstr ""

#: .\templates\paypervisit\index.html:183
msgid "Select the payment method used by the customer"
msgstr ""

#: .\templates\paypervisit\index.html:192
#, fuzzy
#| msgid "Payment Report"
msgid "Pay-per-visit Receipt"
msgstr "របាយការណ៍បង់ប្រាក់តាមការចូល"

#: .\templates\paypervisit\index.html:193
msgid "Kompongkrobey, Svaypoa, Battambang, Cambodia"
msgstr ""

#: .\templates\paypervisit\index.html:194
msgid "Tel: 070 201 530"
msgstr ""

#: .\templates\paypervisit\index.html:196
#, fuzzy
#| msgid "Duration"
msgid "Transaction ID:"
msgstr "រយៈពេល"

#: .\templates\paypervisit\index.html:201
#, fuzzy
#| msgid "Cashier"
msgid "Cashier:"
msgstr "អ្នកគិតលុយ"

#: .\templates\paypervisit\index.html:208
#, fuzzy
#| msgid "Payment Method"
msgid "Payment Method:"
msgstr "វិធីទូទាត់"

#: .\templates\paypervisit\index.html:213
msgid "Visitors:"
msgstr ""

#: .\templates\paypervisit\index.html:217
msgid "Rate:"
msgstr ""

#: .\templates\paypervisit\index.html:224
msgid "TOTAL:"
msgstr ""

#: .\templates\paypervisit\index.html:229
#, fuzzy
#| msgid "Legend Fitness Club"
msgid "Thank you for visiting Legend Fitness Club!"
msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#: .\templates\paypervisit\index.html:230
#, fuzzy
#| msgid "Legend Fitness"
msgid "Telegram: @LegendFitness"
msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#: .\templates\paypervisit\index.html:241
#: .\templates\paypervisit\index.html:261
#: .\templates\paypervisit\index.html:532
#, fuzzy
#| msgid "Mobile Payment"
msgid "Process"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\paypervisit\index.html:248
msgid "Keyboard Shortcuts:"
msgstr ""

#: .\templates\paypervisit\index.html:253
msgid "Increase"
msgstr ""

#: .\templates\paypervisit\index.html:257
msgid "Decrease"
msgstr ""

#: .\templates\paypervisit\index.html:406
msgid "people"
msgstr ""

#: .\templates\paypervisit\index.html:456
#, fuzzy
#| msgid "Amount"
msgid "Total amount:"
msgstr "ចំនួនទឹកប្រាក់"

#: .\templates\paypervisit\index.html:463
msgid "Reset to 1 Visitor"
msgstr ""

#: .\templates\paypervisit\index.html:463
msgid "Number of visitors has been reset to 1"
msgstr ""

#: .\templates\paypervisit\index.html:469
#, fuzzy
#| msgid "Receipt"
msgid "Print Receipt"
msgstr "បង្កាន់ដៃ"

#: .\templates\paypervisit\index.html:469
msgid "Opening receipt preview..."
msgstr ""

#: .\templates\paypervisit\index.html:513
msgid "visitor"
msgstr ""

#: .\templates\paypervisit\index.html:513
msgid "visitors"
msgstr ""

#: .\templates\paypervisit\index.html:514
#, fuzzy
#| msgid "Mobile Payment"
msgid "Processing Payment"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\paypervisit\index.html:514
#, fuzzy
#| msgid "Mobile Payment"
msgid "Processing payment for"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\paypervisit\index.html:518
#, fuzzy
#| msgid "Payment Method"
msgid "Processing..."
msgstr "វិធីទូទាត់"

#: .\templates\paypervisit\index.html:543
#: .\templates\paypervisit\index.html:548
#: .\templates\paypervisit\index.html:553
msgid "Invalid Input"
msgstr ""

#: .\templates\paypervisit\index.html:543
msgid "Number of people must be at least 1"
msgstr ""

#: .\templates\paypervisit\index.html:548
msgid "Amount must be greater than 0"
msgstr ""

#: .\templates\paypervisit\index.html:553
msgid "Please select a payment method"
msgstr ""

#: .\templates\paypervisit\index.html:775
#, fuzzy
#| msgid "Pay-per-visit"
msgid "Pay-per-visit System"
msgstr "បង់ប្រាក់ច្រចាំថ្ងៃ"

#: .\templates\paypervisit\index.html:775
msgid ""
"Select the number of visitors using the quick buttons or numpad, then "
"process payment"
msgstr ""

#: .\templates\paypervisit\settings.html:21
#, fuzzy
#| msgid "Pay-per-visit Settings"
msgid "Pay-per-visit Settings"
msgstr "ការកំណត់បង់ប្រាក់តាមការចូល"

#: .\templates\paypervisit\settings.html:43
#, fuzzy
#| msgid "Price"
msgid "Price Per Person (KHR)"
msgstr "តម្លៃ"

#: .\templates\paypervisit\settings.html:55
msgid "Enter price"
msgstr ""

#: .\templates\paypervisit\settings.html:61
msgid "Base price per person for pay-per-visit access"
msgstr ""

#: .\templates\paypervisit\settings.html:64
msgid "Current value:"
msgstr ""

#: .\templates\paypervisit\transaction.html:63
#, fuzzy
#| msgid "Pay-per-visit Settings"
msgid "Pay-per-visit Transaction History"
msgstr "ការកំណត់បង់ប្រាក់តាមការចូល"

#: .\templates\paypervisit\transaction.html:71
#, fuzzy
#| msgid "Actions"
msgid "Filter Transactions"
msgstr "សកម្មភាព"

#: .\templates\paypervisit\transaction.html:78
#, fuzzy
#| msgid "Join Date"
msgid "From Date"
msgstr "ថ្ងៃចូលរួម"

#: .\templates\paypervisit\transaction.html:86
#, fuzzy
#| msgid "Join Date"
msgid "To Date"
msgstr "ថ្ងៃចូលរួម"

#: .\templates\paypervisit\transaction.html:99
#, fuzzy
#| msgid "Payment Method"
msgid "All Payment Methods"
msgstr "វិធីទូទាត់"

#: .\templates\paypervisit\transaction.html:110
#: .\templates\paypervisit\transaction.html:183
msgid "Cashier"
msgstr "អ្នកគិតលុយ"

#: .\templates\paypervisit\transaction.html:113
#, fuzzy
#| msgid "Cashier"
msgid "All Cashiers"
msgstr "អ្នកគិតលុយ"

#: .\templates\paypervisit\transaction.html:123
#: .\templates\user\admin_action_logs_fixed.html:48
#: .\templates\user\user_action_logs.html:189
msgid "Apply Filters"
msgstr ""

#: .\templates\paypervisit\transaction.html:127
msgid "Reset"
msgstr ""

#: .\templates\paypervisit\transaction.html:143
msgid "Total Visitors"
msgstr ""

#: .\templates\paypervisit\transaction.html:154
msgid "Total Revenue"
msgstr ""

#: .\templates\paypervisit\transaction.html:180
msgid "Visitors"
msgstr ""

#: .\templates\paypervisit\transaction.html:182
msgid "Date & Time"
msgstr ""

#: .\templates\paypervisit\transaction.html:208
msgid "Print"
msgstr ""

#: .\templates\paypervisit\transaction.html:220
#, fuzzy
#| msgid "Duration"
msgid "Delete Transaction"
msgstr "រយៈពេល"

#: .\templates\paypervisit\transaction.html:221
#: .\venv\Lib\site-packages\django\forms\formsets.py:499
#, fuzzy
#| msgid "Delete Item"
msgid "Delete"
msgstr "លុប"

#: .\templates\paypervisit\transaction.html:234
#, fuzzy
#| msgid "Actions"
msgid "No transactions found"
msgstr "សកម្មភាព"

#: .\templates\paypervisit\transaction.html:235
msgid "No pay-per-visit payments found for the selected filters."
msgstr ""

#: .\templates\paypervisit\transaction.html:237
#, fuzzy
#| msgid "Actions"
msgid "No transactions yet"
msgstr "សកម្មភាព"

#: .\templates\paypervisit\transaction.html:238
msgid "No pay-per-visit payments recorded yet."
msgstr ""

#: .\templates\paypervisit\transaction.html:288
msgid "Items per page:"
msgstr ""

#: .\templates\paypervisit\transaction.html:381
msgid "Go to page:"
msgstr ""

#: .\templates\product\purchase_form.html:10
#, fuzzy
#| msgid "Purchase Date"
msgid "Create New Purchase"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\templates\product\purchase_form.html:20
#, fuzzy
#| msgid "Duration"
msgid "Purchase Information"
msgstr "រយៈពេល"

#: .\templates\product\purchase_form.html:23
#, fuzzy
#| msgid "Actions"
msgid "Optional"
msgstr "សកម្មភាព"

#: .\templates\product\purchase_form.html:24
msgid "Enter any additional notes about this purchase..."
msgstr ""

#: .\templates\product\purchase_form.html:31
#, fuzzy
#| msgid "Purchase Date"
msgid "Purchase Items"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\templates\product\purchase_form.html:39
#, fuzzy
#| msgid "Products"
msgid "Product"
msgstr "ផលិតផល"

#: .\templates\product\purchase_form.html:41
#, fuzzy
#| msgid "Products"
msgid "Select Product"
msgstr "ផលិតផល"

#: .\templates\product\purchase_form.html:53
#, fuzzy
#| msgid "Supplier"
msgid "Supplier"
msgstr "អ្នកផ្គត់ផ្គង់"

#: .\templates\product\purchase_form.html:55
#, fuzzy
#| msgid "Supplier"
msgid "Select Supplier"
msgstr "អ្នកផ្គត់ផ្គង់"

#: .\templates\product\purchase_form.html:74
msgid "Box Cost (៛)"
msgstr ""

#: .\templates\product\purchase_form.html:77
#: .\templates\product\purchase_form.html:255
msgid "Boxes"
msgstr ""

#: .\templates\product\purchase_form.html:91
#, fuzzy
#| msgid "Add Item"
msgid "Add Another Item"
msgstr "បន្ថែម"

#: .\templates\product\purchase_form.html:95
#, fuzzy
#| msgid "Delete Selected"
msgid "item(s) selected"
msgstr "លុបអ្វីដែលបានជ្រើសរើស"

#: .\templates\product\purchase_form.html:102
#, fuzzy
#| msgid "Cancel Action"
msgid "Cancel"
msgstr "បោះបង់"

#: .\templates\product\purchase_form.html:103
#, fuzzy
#| msgid "Purchase Date"
msgid "Add Purchase"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\templates\product\purchase_form.html:168
#: .\templates\product\purchase_form.html:274
#: .\templates\product\purchase_form.html:282
#, fuzzy
#| msgid "Unit"
msgid "Units"
msgstr "ឯកតា"

#: .\templates\product\purchase_form.html:256
msgid "box"
msgstr ""

#: .\templates\product\purchase_form.html:256
msgid "units"
msgstr ""

#: .\templates\sidebar.html:84
#, fuzzy
#| msgid "Staff Dashboard"
msgid "Staff and User"
msgstr "បុគ្គលិក"

#: .\templates\sidebar.html:96 .\user\models.py:56
msgid "Employee"
msgstr ""

#: .\templates\sidebar.html:105 .\templates\user\admin_action_logs.html:18
#: .\templates\user\user_action_logs.html:120
#: .\templates\user\user_action_logs.html:214 .\user\models.py:331
#, fuzzy
#| msgid "Username"
msgid "User"
msgstr "ឈ្មោះអ្នកប្រើប្រាស់"

#: .\templates\sidebar.html:124
#, fuzzy
#| msgid "Members"
msgid "Membership"
msgstr "សមាជិក"

#: .\templates\sidebar.html:136 .\templates\sidebar.html:475
msgid "Members"
msgstr "សមាជិក"

#: .\templates\sidebar.html:145
#, fuzzy
#| msgid "Packages"
msgid "Packages"
msgstr "កញ្ចប់"

#: .\templates\sidebar.html:166 .\templates\sidebar.html:179
#: .\templates\sidebar.html:493 .\templates\sidebar.html:506
msgid "Products"
msgstr "ផលិតផល"

#: .\templates\sidebar.html:202
#, fuzzy
#| msgid "Products"
msgid "Purchase Products"
msgstr "ផលិតផល"

#: .\templates\sidebar.html:212
#, fuzzy
#| msgid "Supplier"
msgid "Suppliers"
msgstr "អ្នកផ្គត់ផ្គង់"

#: .\templates\sidebar.html:247 .\templates\sidebar.html:548
msgid "Payments"
msgstr "ការទូទាត់"

#: .\templates\sidebar.html:260
#, fuzzy
#| msgid "Mobile Payment"
msgid "Member Payments"
msgstr "ការទូទាត់តាមទូរស័ព្ទ"

#: .\templates\sidebar.html:284
msgid "Payroll"
msgstr ""

#: .\templates\sidebar.html:328
#, fuzzy
#| msgid "Inventory Report"
msgid "Overview Report"
msgstr "របាយការណ៍ទូទៅ"

#: .\templates\sidebar.html:346
#, fuzzy
#| msgid "Member Report"
msgid "Expenses Report"
msgstr "របាយការណ៍សមាជិក"

#: .\templates\sidebar.html:374 .\templates\sidebar.html:576
#, fuzzy
#| msgid "Cancel"
msgid "Finance"
msgstr "បោះបង់"

#: .\templates\sidebar.html:386
msgid "Overview"
msgstr ""

#: .\templates\sidebar.html:518
#, fuzzy
#| msgid "Purchase Date"
msgid "Purchases"
msgstr "កាលបរិច្ឆេទទិញ"

#: .\templates\sidebar.html:530
#, fuzzy
#| msgid "Pay-per-visit Settings"
msgid "Pay-per-visit Transactions"
msgstr "ការកំណត់បង់ប្រាក់តាមការចូល"

#: .\templates\sidebar.html:562
#, fuzzy
#| msgid "Salary"
msgid "My Salary"
msgstr "ប្រាក់ខែ"

#: .\templates\sidebar.html:592
msgid "Logout"
msgstr "ចាកចេញ"

#: .\templates\user\admin_action_logs.html:5
#: .\templates\user\admin_action_logs.html:9
#: .\templates\user\admin_action_logs_fixed.html:5
#: .\templates\user\admin_action_logs_fixed.html:12 .\user\models.py:186
msgid "Admin Action Logs"
msgstr ""

#: .\templates\user\admin_action_logs.html:12
msgid "Log Entries"
msgstr ""

#: .\templates\user\admin_action_logs.html:17
#: .\templates\user\admin_action_logs_fixed.html:118
#: .\templates\user\user_action_logs.html:213
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2520
msgid "Time"
msgstr ""

#: .\templates\user\admin_action_logs.html:19
#: .\templates\user\admin_action_logs_fixed.html:120
#: .\templates\user\user_action_logs.html:215
#, fuzzy
#| msgid "Actions"
msgid "Action"
msgstr "សកម្មភាព"

#: .\templates\user\admin_action_logs.html:33
#: .\templates\user\user_action_logs.html:290
msgid "No logs found"
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:14
#: .\templates\user\user_action_logs.html:17
msgid "Back to Users"
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:20
#, fuzzy
#| msgid "Security"
msgid "Security Notice"
msgstr "សន្តិសុខ"

#: .\templates\user\admin_action_logs_fixed.html:21
msgid ""
"This page displays a log of all critical admin actions. These logs cannot be "
"modified and are used for security auditing."
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:26
#: .\templates\user\user_action_logs.html:107
#, fuzzy
#| msgid "Filter Items"
msgid "Filter Logs"
msgstr "ច្រោះ"

#: .\templates\user\admin_action_logs_fixed.html:29
#: .\templates\user\user_action_logs.html:78
#: .\templates\user\user_action_logs.html:133 .\user\models.py:177
#: .\user\models.py:332
#, fuzzy
#| msgid "Actions"
msgid "Action Type"
msgstr "សកម្មភាព"

#: .\templates\user\admin_action_logs_fixed.html:31
#: .\templates\user\user_action_logs.html:135
#, fuzzy
#| msgid "Bulk Actions"
msgid "All Actions"
msgstr "សកម្មភាពច្រើន"

#: .\templates\user\admin_action_logs_fixed.html:38
#: .\templates\user\admin_action_logs_fixed.html:76
#: .\templates\user\admin_action_logs_fixed.html:119 .\user\models.py:176
#, fuzzy
#| msgid "Admin"
msgid "Admin User"
msgstr "អ្នកគ្រប់គ្រង"

#: .\templates\user\admin_action_logs_fixed.html:40
#, fuzzy
#| msgid "Admin"
msgid "All Admins"
msgstr "អ្នកគ្រប់គ្រង"

#: .\templates\user\admin_action_logs_fixed.html:81
#: .\templates\user\admin_action_logs_fixed.html:135
#: .\venv\Lib\site-packages\django\forms\widgets.py:800
msgid "Unknown"
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:88
#: .\templates\user\admin_action_logs_fixed.html:121 .\user\models.py:181
msgid "Target User"
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:94
#: .\templates\user\admin_action_logs_fixed.html:122
#: .\templates\user\user_action_logs.html:220 .\user\models.py:179
#: .\user\models.py:347
#, fuzzy
#| msgid "Address"
msgid "IP Address"
msgstr "អាសយដ្ឋាន"

#: .\templates\user\admin_action_logs_fixed.html:106
#: .\templates\user\admin_action_logs_fixed.html:161
msgid "No action logs found."
msgstr ""

#: .\templates\user\admin_action_logs_fixed.html:153
msgid "N/A"
msgstr ""

#: .\templates\user\change_password.html:349
msgid "My Profile"
msgstr "ប្រវត្តិរូបរបស់ខ្ញុំ"

#: .\templates\user\change_password.html:353
#: .\templates\user\change_password.html:365
#: .\templates\user\change_password.html:470
#: .\templates\user\change_password.html:803
#, fuzzy
#| msgid "Password"
msgid "Change Password"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\user\change_password.html:366
msgid "Update your account password for better security"
msgstr ""

#: .\templates\user\change_password.html:378
#, fuzzy
#| msgid "Incorrect password"
msgid "Current Password"
msgstr "ពាក្យសម្ងាត់មិនត្រឹមត្រូវ"

#: .\templates\user\change_password.html:388
#, fuzzy
#| msgid "Please enter your password"
msgid "Enter your current password"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:394
msgid "Toggle current password visibility"
msgstr ""

#: .\templates\user\change_password.html:400
msgid "Enter your current password to verify your identity"
msgstr ""

#: .\templates\user\change_password.html:408
#, fuzzy
#| msgid "Password"
msgid "New Password"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\user\change_password.html:418
#, fuzzy
#| msgid "Please enter your password"
msgid "Enter your new password"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:425
msgid "Toggle new password visibility"
msgstr ""

#: .\templates\user\change_password.html:431
msgid ""
"Password must be at least 8 characters long and include a mix of letters, "
"numbers, and special characters"
msgstr ""

#: .\templates\user\change_password.html:439
#, fuzzy
#| msgid "Forgot Password?"
msgid "Confirm New Password"
msgstr "ភ្លេចពាក្យសម្ងាត់?"

#: .\templates\user\change_password.html:449
#, fuzzy
#| msgid "Please enter your password"
msgid "Confirm your new password"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:456
msgid "Toggle confirm password visibility"
msgstr ""

#: .\templates\user\change_password.html:462
#, fuzzy
#| msgid "Please enter your password"
msgid "Re-enter your new password to confirm"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:479
msgid "Password Security Tips"
msgstr ""

#: .\templates\user\change_password.html:482
msgid "Use a mix of uppercase and lowercase letters"
msgstr ""

#: .\templates\user\change_password.html:483
msgid "Include numbers and special characters"
msgstr ""

#: .\templates\user\change_password.html:484
msgid "Avoid using personal information"
msgstr ""

#: .\templates\user\change_password.html:485
msgid "Don't reuse passwords from other sites"
msgstr ""

#: .\templates\user\change_password.html:486
msgid "Consider using a password manager"
msgstr ""

#: .\templates\user\change_password.html:487
#, fuzzy
#| msgid "Please enter your password"
msgid "Change your password regularly"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:681
#, fuzzy
#| msgid "Password"
msgid "Strong password!"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\user\change_password.html:683
msgid "Password must include:"
msgstr ""

#: .\templates\user\change_password.html:684
msgid "8+ characters"
msgstr ""

#: .\templates\user\change_password.html:685
msgid "uppercase"
msgstr ""

#: .\templates\user\change_password.html:686
msgid "lowercase"
msgstr ""

#: .\templates\user\change_password.html:687
#, fuzzy
#| msgid "Members"
msgid "numbers"
msgstr "សមាជិក"

#: .\templates\user\change_password.html:688
msgid "special characters"
msgstr ""

#: .\templates\user\change_password.html:711
#, fuzzy
#| msgid "Password"
msgid "Passwords match!"
msgstr "ពាក្យសម្ងាត់"

#: .\templates\user\change_password.html:713
#: .\templates\user\change_password.html:767
msgid "Passwords do not match"
msgstr ""

#: .\templates\user\change_password.html:748
msgid "Current password is required"
msgstr ""

#: .\templates\user\change_password.html:755
msgid "New password is required"
msgstr ""

#: .\templates\user\change_password.html:758
msgid "Password does not meet security requirements"
msgstr ""

#: .\templates\user\change_password.html:764
#, fuzzy
#| msgid "Please enter your password"
msgid "Please confirm your new password"
msgstr "សូមបញ្ចូលពាក្យសម្ងាត់របស់អ្នក"

#: .\templates\user\change_password.html:773
msgid "New password must be different from current password"
msgstr ""

#: .\templates\user\change_password.html:780
msgid "Changing Password..."
msgstr ""

#: .\templates\user\change_password.html:791
msgid "Validation Error"
msgstr ""

#: .\templates\user\change_password.html:791
msgid "Please fix the errors below and try again"
msgstr ""

#: .\templates\user\change_password.html:803
msgid "Enter your current password and choose a strong new password"
msgstr ""

#: .\templates\user\user_action_logs.html:5
#: .\templates\user\user_action_logs.html:13 .\user\models.py:358
msgid "User Action Logs"
msgstr ""

#: .\templates\user\user_action_logs.html:14
msgid "Comprehensive security audit log of all user actions across the system"
msgstr ""

#: .\templates\user\user_action_logs.html:29
#, fuzzy
#| msgid "Total"
msgid "Total Logs"
msgstr "សរុប"

#: .\templates\user\user_action_logs.html:40
#, fuzzy
#| msgid "Full Name"
msgid "Success Rate"
msgstr "ឈ្មោះពេញ"

#: .\templates\user\user_action_logs.html:51
#, fuzzy
#| msgid "Active"
msgid "Active Users"
msgstr "សកម្ម"

#: .\templates\user\user_action_logs.html:62
#, fuzzy
#| msgid "Bulk Actions"
msgid "Critical Actions"
msgstr "សកម្មភាពច្រើន"

#: .\templates\user\user_action_logs.html:74
#, fuzzy
#| msgid "Active Status"
msgid "Active Filters:"
msgstr "សកម្ម"

#: .\templates\user\user_action_logs.html:96
#: .\templates\user\user_action_logs.html:112
#, fuzzy
#| msgid "Search Items"
msgid "Search"
msgstr "ស្វែងរក"

#: .\templates\user\user_action_logs.html:101
msgid "filtered results"
msgstr ""

#: .\templates\user\user_action_logs.html:114
msgid "Search description, user..."
msgstr ""

#: .\templates\user\user_action_logs.html:122
#, fuzzy
#| msgid "Admin"
msgid "All Users"
msgstr "អ្នកគ្រប់គ្រង"

#: .\templates\user\user_action_logs.html:148
msgid "All Modules"
msgstr ""

#: .\templates\user\user_action_logs.html:174
#, fuzzy
#| msgid "Join Date"
msgid "Date From"
msgstr "ថ្ងៃចូលរួម"

#: .\templates\user\user_action_logs.html:181
#, fuzzy
#| msgid "Join Date"
msgid "Date To"
msgstr "ថ្ងៃចូលរួម"

#: .\templates\user\user_action_logs.html:192
msgid "Clear Filters"
msgstr ""

#: .\templates\user\user_action_logs.html:201
#, fuzzy
#| msgid "Actions"
msgid "Action Log Entries"
msgstr "សកម្មភាព"

#: .\templates\user\user_action_logs.html:218
msgid "Target"
msgstr ""

#: .\templates\user\user_action_logs.html:291
msgid "Try adjusting your filters or search criteria"
msgstr ""

#: .\templates\user\user_action_logs.html:490
msgid "Please enter a valid page number between 1 and"
msgstr ""

#: .\user\models.py:48
msgid "Full Name"
msgstr "ឈ្មោះពេញ"

#: .\user\models.py:53
msgid "Employee ID"
msgstr ""

#: .\user\models.py:54
msgid "NID"
msgstr ""

#: .\user\models.py:55
msgid "Manager"
msgstr "អ្នកគ្រប់គ្រង"

#: .\user\models.py:57
msgid "Salary"
msgstr "ប្រាក់ខែ"

#: .\user\models.py:58
#, fuzzy
#| msgid "Salary"
msgid "Due Salary"
msgstr "ប្រាក់ខែ"

#: .\user\models.py:68
msgid "Join Date"
msgstr "ថ្ងៃចូលរួម"

#: .\user\models.py:69
#, fuzzy
#| msgid "Sale Date"
msgid "Create Date"
msgstr "កាលបរិច្ឆេទលក់"

#: .\user\models.py:70
#, fuzzy
#| msgid "Set Active"
msgid "Last Activity"
msgstr "កំណត់ឱ្យសកម្ម"

#: .\user\models.py:76
msgid "Email Verified"
msgstr ""

#: .\user\models.py:77
msgid "Password Reset Token"
msgstr ""

#: .\user\models.py:78
msgid "Password Reset Expires"
msgstr ""

#: .\user\models.py:81
msgid "Receive Email Notifications"
msgstr ""

#: .\user\models.py:82
msgid "Receive System Notifications"
msgstr ""

#: .\user\models.py:154
msgid "Default number of items to show per page in product list"
msgstr ""

#: .\user\models.py:178 .\user\models.py:334
#, fuzzy
#| msgid "Actions"
msgid "Action Time"
msgstr "សកម្មភាព"

#: .\user\models.py:185
msgid "Admin Action Log"
msgstr ""

#: .\user\models.py:338
msgid "Target Model"
msgstr ""

#: .\user\models.py:339
msgid "Target ID"
msgstr ""

#: .\user\models.py:340
#, fuzzy
#| msgid "Duration"
msgid "Target Description"
msgstr "រយៈពេល"

#: .\user\models.py:343
msgid "Before Values"
msgstr ""

#: .\user\models.py:344
msgid "After Values"
msgstr ""

#: .\user\models.py:348
msgid "User Agent"
msgstr ""

#: .\user\models.py:352
msgid "Additional Data"
msgstr ""

#: .\user\models.py:353
#, fuzzy
#| msgid "Financial Report"
msgid "Financial Impact"
msgstr "របាយការណ៍ហិរញ្ញវត្ថុ"

#: .\user\models.py:357
msgid "User Action Log"
msgstr ""

#: .\venv\Lib\site-packages\click\_termui_impl.py:556
#, python-brace-format
msgid "{editor}: Editing failed"
msgstr ""

#: .\venv\Lib\site-packages\click\_termui_impl.py:560
#, python-brace-format
msgid "{editor}: Editing failed: {e}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1124
msgid "Aborted!"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1349
#: .\venv\Lib\site-packages\click\core.py:1379
#, python-brace-format
msgid "(Deprecated) {text}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1396
#, fuzzy
#| msgid "Actions"
msgid "Options"
msgstr "សកម្មភាព"

#: .\venv\Lib\site-packages\click\core.py:1422
#, python-brace-format
msgid "Got unexpected extra argument ({args})"
msgid_plural "Got unexpected extra arguments ({args})"
msgstr[0] ""

#: .\venv\Lib\site-packages\click\core.py:1438
msgid "DeprecationWarning: The command {name!r} is deprecated."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1645
msgid "Commands"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1677
msgid "Missing command."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:1755
msgid "No such command {name!r}."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2313
msgid "Value must be an iterable."
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2334
#, python-brace-format
msgid "Takes {nargs} values but 1 was given."
msgid_plural "Takes {nargs} values but {len} were given."
msgstr[0] ""

#: .\venv\Lib\site-packages\click\core.py:2783
#, python-brace-format
msgid "env var: {var}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2813
msgid "(dynamic)"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2828
#, python-brace-format
msgid "default: {default}"
msgstr ""

#: .\venv\Lib\site-packages\click\core.py:2841
msgid "required"
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:457
#, python-format
msgid "%(prog)s, version %(version)s"
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:520
msgid "Show the version and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\decorators.py:541
msgid "Show this message and exit."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:49
#: .\venv\Lib\site-packages\click\exceptions.py:88
#, python-brace-format
msgid "Error: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:80
#, python-brace-format
msgid "Try '{command} {option}' for help."
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:129
#, python-brace-format
msgid "Invalid value: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:131
#, python-brace-format
msgid "Invalid value for {param_hint}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:187
msgid "Missing argument"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:189
msgid "Missing option"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:191
msgid "Missing parameter"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:193
#, python-brace-format
msgid "Missing {param_type}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:200
#, python-brace-format
msgid "Missing parameter: {param_name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:220
#, python-brace-format
msgid "No such option: {name}"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:232
#, python-brace-format
msgid "Did you mean {possibility}?"
msgid_plural "(Possible options: {possibilities})"
msgstr[0] ""

#: .\venv\Lib\site-packages\click\exceptions.py:270
msgid "unknown error"
msgstr ""

#: .\venv\Lib\site-packages\click\exceptions.py:277
msgid "Could not open file {filename!r}: {message}"
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:233
msgid "Argument {name!r} takes {nargs} values."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:415
msgid "Option {name!r} does not take a value."
msgstr ""

#: .\venv\Lib\site-packages\click\parser.py:476
msgid "Option {name!r} requires an argument."
msgid_plural "Option {name!r} requires {nargs} arguments."
msgstr[0] ""

#: .\venv\Lib\site-packages\click\shell_completion.py:326
msgid "Shell completion is not supported for Bash versions older than 4.4."
msgstr ""

#: .\venv\Lib\site-packages\click\shell_completion.py:333
msgid "Couldn't detect Bash version, shell completion is not supported."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:158
msgid "Repeat for confirmation"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:174
msgid "Error: The value you entered was invalid."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:176
#, python-brace-format
msgid "Error: {e.message}"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:187
msgid "Error: The two entered values do not match."
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:243
msgid "Error: invalid input"
msgstr ""

#: .\venv\Lib\site-packages\click\termui.py:773
msgid "Press any key to continue..."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:267
#, python-brace-format
msgid ""
"Choose from:\n"
"\t{choices}"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:299
msgid "{value!r} is not {choice}."
msgid_plural "{value!r} is not one of {choices}."
msgstr[0] ""

#: .\venv\Lib\site-packages\click\types.py:393
msgid "{value!r} does not match the format {format}."
msgid_plural "{value!r} does not match the formats {formats}."
msgstr[0] ""

#: .\venv\Lib\site-packages\click\types.py:415
msgid "{value!r} is not a valid {number_type}."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:471
#, python-brace-format
msgid "{value} is not in the range {range}."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:612
msgid "{value!r} is not a valid boolean."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:636
msgid "{value!r} is not a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:826
msgid "file"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:828
msgid "directory"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:830
msgid "path"
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:881
msgid "{name} {filename!r} does not exist."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:890
msgid "{name} {filename!r} is a file."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:898
msgid "{name} {filename!r} is a directory."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:907
msgid "{name} {filename!r} is not readable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:916
msgid "{name} {filename!r} is not writable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:925
msgid "{name} {filename!r} is not executable."
msgstr ""

#: .\venv\Lib\site-packages\click\types.py:992
#, python-brace-format
msgid "{len_type} values are required, but {len_value} was given."
msgid_plural "{len_type} values are required, but {len_value} were given."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\contrib\messages\apps.py:16
msgid "Messages"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\sitemaps\apps.py:8
msgid "Site Maps"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\staticfiles\apps.py:9
msgid "Static Files"
msgstr ""

#: .\venv\Lib\site-packages\django\contrib\syndication\apps.py:7
msgid "Syndication"
msgstr ""

#. Translators: String used to replace omitted page numbers in elided page
#. range generated by paginators, e.g. [1, 2, '…', 5, 6, 7, '…', 9, 10].
#: .\venv\Lib\site-packages\django\core\paginator.py:30
msgid "…"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:32
msgid "That page number is not an integer"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:33
msgid "That page number is less than 1"
msgstr ""

#: .\venv\Lib\site-packages\django\core\paginator.py:34
msgid "That page contains no results"
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:22
msgid "Enter a valid value."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:104
#: .\venv\Lib\site-packages\django\forms\fields.py:760
msgid "Enter a valid URL."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:165
msgid "Enter a valid integer."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:176
msgid "Enter a valid email address."
msgstr ""

#. Translators: "letters" means latin letters: a-z and A-Z.
#: .\venv\Lib\site-packages\django\core\validators.py:259
msgid ""
"Enter a valid “slug” consisting of letters, numbers, underscores or hyphens."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:267
msgid ""
"Enter a valid “slug” consisting of Unicode letters, numbers, underscores, or "
"hyphens."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:279
#: .\venv\Lib\site-packages\django\core\validators.py:306
msgid "Enter a valid IPv4 address."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:286
#: .\venv\Lib\site-packages\django\core\validators.py:307
msgid "Enter a valid IPv6 address."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:298
#: .\venv\Lib\site-packages\django\core\validators.py:305
msgid "Enter a valid IPv4 or IPv6 address."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:341
msgid "Enter only digits separated by commas."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:347
#, python-format
msgid "Ensure this value is %(limit_value)s (it is %(show_value)s)."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:382
#, python-format
msgid "Ensure this value is less than or equal to %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:391
#, python-format
msgid "Ensure this value is greater than or equal to %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:400
#, python-format
msgid "Ensure this value is a multiple of step size %(limit_value)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:407
#, python-format
msgid ""
"Ensure this value is a multiple of step size %(limit_value)s, starting from "
"%(offset)s, e.g. %(offset)s, %(valid_value1)s, %(valid_value2)s, and so on."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:439
#, python-format
msgid ""
"Ensure this value has at least %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at least %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\core\validators.py:457
#, python-format
msgid ""
"Ensure this value has at most %(limit_value)d character (it has "
"%(show_value)d)."
msgid_plural ""
"Ensure this value has at most %(limit_value)d characters (it has "
"%(show_value)d)."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\core\validators.py:480
#: .\venv\Lib\site-packages\django\forms\fields.py:355
#: .\venv\Lib\site-packages\django\forms\fields.py:394
msgid "Enter a number."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:482
#, python-format
msgid "Ensure that there are no more than %(max)s digit in total."
msgid_plural "Ensure that there are no more than %(max)s digits in total."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\core\validators.py:487
#, python-format
msgid "Ensure that there are no more than %(max)s decimal place."
msgid_plural "Ensure that there are no more than %(max)s decimal places."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\core\validators.py:492
#, python-format
msgid ""
"Ensure that there are no more than %(max)s digit before the decimal point."
msgid_plural ""
"Ensure that there are no more than %(max)s digits before the decimal point."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\core\validators.py:563
#, python-format
msgid ""
"File extension “%(extension)s” is not allowed. Allowed extensions are: "
"%(allowed_extensions)s."
msgstr ""

#: .\venv\Lib\site-packages\django\core\validators.py:624
msgid "Null characters are not allowed."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\base.py:1473
#: .\venv\Lib\site-packages\django\forms\models.py:906
msgid "and"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\base.py:1475
#, python-format
msgid "%(model_name)s with this %(field_labels)s already exists."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\constraints.py:20
#, python-format
msgid "Constraint “%(name)s” is violated."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:133
#, python-format
msgid "Value %(value)r is not a valid choice."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:134
#, fuzzy
#| msgid "This action cannot be undone"
msgid "This field cannot be null."
msgstr "សកម្មភាពនេះមិនអាចត្រឡប់ក្រោយវិញបានទេ"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:135
#, fuzzy
#| msgid "This action cannot be undone"
msgid "This field cannot be blank."
msgstr "សកម្មភាពនេះមិនអាចត្រឡប់ក្រោយវិញបានទេ"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:136
#, python-format
msgid "%(model_name)s with this %(field_label)s already exists."
msgstr ""

#. Translators: The 'lookup_type' is one of 'date', 'year' or
#. 'month'. Eg: "Title must be unique for pub_date year"
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:140
#, python-format
msgid ""
"%(field_label)s must be unique for %(date_field_label)s %(lookup_type)s."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:179
#, python-format
msgid "Field of type: %(field_type)s"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1155
#, python-format
msgid "“%(value)s” value must be either True or False."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1156
#, python-format
msgid "“%(value)s” value must be either True, False, or None."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1158
msgid "Boolean (Either True or False)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1208
#, python-format
msgid "String (up to %(max_length)s)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1210
msgid "String (unlimited)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1314
msgid "Comma-separated integers"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1415
#, python-format
msgid ""
"“%(value)s” value has an invalid date format. It must be in YYYY-MM-DD "
"format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1419
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1554
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD) but it is an invalid "
"date."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1423
msgid "Date (without time)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1550
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in YYYY-MM-DD HH:MM[:ss[."
"uuuuuu]][TZ] format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1558
#, python-format
msgid ""
"“%(value)s” value has the correct format (YYYY-MM-DD HH:MM[:ss[.uuuuuu]]"
"[TZ]) but it is an invalid date/time."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1563
msgid "Date (with time)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1690
#, python-format
msgid "“%(value)s” value must be a decimal number."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1692
msgid "Decimal number"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1853
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in [DD] [[HH:]MM:]ss[."
"uuuuuu] format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1857
msgid "Duration"
msgstr "រយៈពេល"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1909
#, fuzzy
#| msgid "Address"
msgid "Email address"
msgstr "អាសយដ្ឋាន"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:1934
msgid "File path"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2012
#, python-format
msgid "“%(value)s” value must be a float."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2014
msgid "Floating point number"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2054
#, python-format
msgid "“%(value)s” value must be an integer."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2056
msgid "Integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2152
msgid "Big (8 byte) integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2169
msgid "Small integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2177
#, fuzzy
#| msgid "Address"
msgid "IPv4 address"
msgstr "អាសយដ្ឋាន"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2208
#, fuzzy
#| msgid "Address"
msgid "IP address"
msgstr "អាសយដ្ឋាន"

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2301
#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2302
#, python-format
msgid "“%(value)s” value must be either None, True or False."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2304
msgid "Boolean (Either True, False or None)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2355
msgid "Positive big integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2370
msgid "Positive integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2385
msgid "Positive small integer"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2401
#, python-format
msgid "Slug (up to %(max_length)s)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2437
msgid "Text"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2512
#, python-format
msgid ""
"“%(value)s” value has an invalid format. It must be in HH:MM[:ss[.uuuuuu]] "
"format."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2516
#, python-format
msgid ""
"“%(value)s” value has the correct format (HH:MM[:ss[.uuuuuu]]) but it is an "
"invalid time."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2628
msgid "URL"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2652
msgid "Raw binary data"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2717
#, python-format
msgid "“%(value)s” is not a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\__init__.py:2719
msgid "Universally unique identifier"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\files.py:232
msgid "File"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:26
msgid "A JSON object"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\json.py:28
msgid "Value must be valid JSON."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:939
#, python-format
msgid "%(model)s instance with %(field)s %(value)r does not exist."
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:941
msgid "Foreign Key (type determined by related field)"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1235
msgid "One-to-one relationship"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1292
#, python-format
msgid "%(from)s-%(to)s relationship"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1294
#, python-format
msgid "%(from)s-%(to)s relationships"
msgstr ""

#: .\venv\Lib\site-packages\django\db\models\fields\related.py:1342
msgid "Many-to-many relationship"
msgstr ""

#. Translators: If found as last label character, these punctuation
#. characters will prevent the default label_suffix to be appended to the label
#: .\venv\Lib\site-packages\django\forms\boundfield.py:185
msgid ":?.!"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:95
msgid "This field is required."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:304
msgid "Enter a whole number."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:475
#: .\venv\Lib\site-packages\django\forms\fields.py:1252
msgid "Enter a valid date."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:498
#: .\venv\Lib\site-packages\django\forms\fields.py:1253
msgid "Enter a valid time."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:525
msgid "Enter a valid date/time."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:559
msgid "Enter a valid duration."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:560
#, python-brace-format
msgid "The number of days must be between {min_days} and {max_days}."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:629
msgid "No file was submitted. Check the encoding type on the form."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:630
msgid "No file was submitted."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:631
msgid "The submitted file is empty."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:633
#, python-format
msgid "Ensure this filename has at most %(max)d character (it has %(length)d)."
msgid_plural ""
"Ensure this filename has at most %(max)d characters (it has %(length)d)."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\forms\fields.py:638
msgid "Please either submit a file or check the clear checkbox, not both."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:702
msgid ""
"Upload a valid image. The file you uploaded was either not an image or a "
"corrupted image."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:874
#: .\venv\Lib\site-packages\django\forms\fields.py:960
#: .\venv\Lib\site-packages\django\forms\models.py:1585
#, python-format
msgid "Select a valid choice. %(value)s is not one of the available choices."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:962
#: .\venv\Lib\site-packages\django\forms\fields.py:1081
#: .\venv\Lib\site-packages\django\forms\models.py:1583
msgid "Enter a list of values."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1082
msgid "Enter a complete value."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1321
msgid "Enter a valid UUID."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\fields.py:1351
msgid "Enter a valid JSON."
msgstr ""

#. Translators: This is the default suffix added to form field labels
#: .\venv\Lib\site-packages\django\forms\forms.py:94
msgid ":"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\forms.py:231
#, python-format
msgid "(Hidden field %(name)s) %(error)s"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:61
#, python-format
msgid ""
"ManagementForm data is missing or has been tampered with. Missing fields: "
"%(field_names)s. You may need to file a bug report if the issue persists."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:65
#, python-format
msgid "Please submit at most %(num)d form."
msgid_plural "Please submit at most %(num)d forms."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:70
#, python-format
msgid "Please submit at least %(num)d form."
msgid_plural "Please submit at least %(num)d forms."
msgstr[0] ""

#: .\venv\Lib\site-packages\django\forms\formsets.py:484
#: .\venv\Lib\site-packages\django\forms\formsets.py:491
msgid "Order"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:899
#, python-format
msgid "Please correct the duplicate data for %(field)s."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:904
#, python-format
msgid "Please correct the duplicate data for %(field)s, which must be unique."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:911
#, python-format
msgid ""
"Please correct the duplicate data for %(field_name)s which must be unique "
"for the %(lookup)s in %(date_field)s."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:920
msgid "Please correct the duplicate values below."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1357
msgid "The inline value did not match the parent instance."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1448
msgid "Select a valid choice. That choice is not one of the available choices."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\models.py:1587
#, python-format
msgid "“%(pk)s” is not a valid value."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\utils.py:227
#, python-format
msgid ""
"%(datetime)s couldn’t be interpreted in time zone %(current_timezone)s; it "
"may be ambiguous or it may not exist."
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:462
msgid "Currently"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:463
msgid "Change"
msgstr ""

#: .\venv\Lib\site-packages\django\forms\widgets.py:801
#, fuzzy
#| msgid "Years"
msgid "Yes"
msgstr "ឆ្នាំ"

#: .\venv\Lib\site-packages\django\forms\widgets.py:802
msgid "No"
msgstr ""

#. Translators: Please do not add spaces around commas.
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:876
msgid "yes,no,maybe"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:906
#: .\venv\Lib\site-packages\django\template\defaultfilters.py:923
#, python-format
msgid "%(size)d byte"
msgid_plural "%(size)d bytes"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:925
#, python-format
msgid "%s KB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:927
#, python-format
msgid "%s MB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:929
#, python-format
msgid "%s GB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:931
#, python-format
msgid "%s TB"
msgstr ""

#: .\venv\Lib\site-packages\django\template\defaultfilters.py:933
#, python-format
msgid "%s PB"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:74
msgid "p.m."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:75
msgid "a.m."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:80
msgid "PM"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:81
msgid "AM"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:153
msgid "midnight"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dateformat.py:155
msgid "noon"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:7
msgid "Monday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:8
msgid "Tuesday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:9
msgid "Wednesday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:10
msgid "Thursday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:11
msgid "Friday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:12
msgid "Saturday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:13
msgid "Sunday"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:16
#, fuzzy
#| msgid "Months"
msgid "Mon"
msgstr "ខែ"

#: .\venv\Lib\site-packages\django\utils\dates.py:17
msgid "Tue"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:18
msgid "Wed"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:19
msgid "Thu"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:20
msgid "Fri"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:21
msgid "Sat"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:22
msgid "Sun"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:25
msgid "January"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:26
msgid "February"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:27
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:28
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:29
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:30
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:31
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:32
msgid "August"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:33
#, fuzzy
#| msgid "Members"
msgid "September"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\dates.py:34
msgid "October"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:35
#, fuzzy
#| msgid "Members"
msgid "November"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\dates.py:36
#, fuzzy
#| msgid "Members"
msgid "December"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\dates.py:39
msgid "jan"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:40
msgid "feb"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:41
msgid "mar"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:42
msgid "apr"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:43
msgid "may"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:44
msgid "jun"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:45
msgid "jul"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:46
msgid "aug"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:47
msgid "sep"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:48
msgid "oct"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:49
msgid "nov"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:50
msgid "dec"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:53
msgctxt "abbrev. month"
msgid "Jan."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:54
msgctxt "abbrev. month"
msgid "Feb."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:55
msgctxt "abbrev. month"
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:56
msgctxt "abbrev. month"
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:57
msgctxt "abbrev. month"
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:58
msgctxt "abbrev. month"
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:59
msgctxt "abbrev. month"
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:60
msgctxt "abbrev. month"
msgid "Aug."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:61
msgctxt "abbrev. month"
msgid "Sept."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:62
msgctxt "abbrev. month"
msgid "Oct."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:63
msgctxt "abbrev. month"
msgid "Nov."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:64
msgctxt "abbrev. month"
msgid "Dec."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:67
msgctxt "alt. month"
msgid "January"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:68
msgctxt "alt. month"
msgid "February"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:69
msgctxt "alt. month"
msgid "March"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:70
msgctxt "alt. month"
msgid "April"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:71
msgctxt "alt. month"
msgid "May"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:72
msgctxt "alt. month"
msgid "June"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:73
msgctxt "alt. month"
msgid "July"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:74
msgctxt "alt. month"
msgid "August"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:75
#, fuzzy
#| msgid "Members"
msgctxt "alt. month"
msgid "September"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\dates.py:76
msgctxt "alt. month"
msgid "October"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\dates.py:77
#, fuzzy
#| msgid "Members"
msgctxt "alt. month"
msgid "November"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\dates.py:78
#, fuzzy
#| msgid "Members"
msgctxt "alt. month"
msgid "December"
msgstr "សមាជិក"

#: .\venv\Lib\site-packages\django\utils\ipv6.py:8
msgid "This is not a valid IPv6 address."
msgstr ""

#: .\venv\Lib\site-packages\django\utils\text.py:70
#, python-format
msgctxt "String to return when truncating text"
msgid "%(truncated_text)s…"
msgstr ""

#: .\venv\Lib\site-packages\django\utils\text.py:270
msgid "or"
msgstr ""

#. Translators: This string is used as a separator between list elements
#: .\venv\Lib\site-packages\django\utils\text.py:289
#: .\venv\Lib\site-packages\django\utils\timesince.py:135
msgid ", "
msgstr ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:8
#, python-format
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:9
#, python-format
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:10
#, python-format
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:11
#, python-format
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:12
#, python-format
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\utils\timesince.py:13
#, python-format
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] ""

#: .\venv\Lib\site-packages\django\views\csrf.py:29
msgid "Forbidden"
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:30
msgid "CSRF verification failed. Request aborted."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:34
msgid ""
"You are seeing this message because this HTTPS site requires a “Referer "
"header” to be sent by your web browser, but none was sent. This header is "
"required for security reasons, to ensure that your browser is not being "
"hijacked by third parties."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:40
msgid ""
"If you have configured your browser to disable “Referer” headers, please re-"
"enable them, at least for this site, or for HTTPS connections, or for “same-"
"origin” requests."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:45
msgid ""
"If you are using the <meta name=\"referrer\" content=\"no-referrer\"> tag or "
"including the “Referrer-Policy: no-referrer” header, please remove them. The "
"CSRF protection requires the “Referer” header to do strict referer checking. "
"If you’re concerned about privacy, use alternatives like <a rel=\"noreferrer"
"\" …> for links to third-party sites."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:54
msgid ""
"You are seeing this message because this site requires a CSRF cookie when "
"submitting forms. This cookie is required for security reasons, to ensure "
"that your browser is not being hijacked by third parties."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:60
msgid ""
"If you have configured your browser to disable cookies, please re-enable "
"them, at least for this site, or for “same-origin” requests."
msgstr ""

#: .\venv\Lib\site-packages\django\views\csrf.py:66
msgid "More information is available with DEBUG=True."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:44
msgid "No year specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:64
#: .\venv\Lib\site-packages\django\views\generic\dates.py:115
#: .\venv\Lib\site-packages\django\views\generic\dates.py:214
#, fuzzy
#| msgid "Date of Birth"
msgid "Date out of range"
msgstr "ថ្ងៃខែឆ្នាំកំណើត"

#: .\venv\Lib\site-packages\django\views\generic\dates.py:94
msgid "No month specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:147
msgid "No day specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:194
msgid "No week specified"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:349
#: .\venv\Lib\site-packages\django\views\generic\dates.py:380
#, python-format
msgid "No %(verbose_name_plural)s available"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:652
#, python-format
msgid ""
"Future %(verbose_name_plural)s not available because %(class_name)s."
"allow_future is False."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\dates.py:692
#, python-format
msgid "Invalid date string “%(datestr)s” given format “%(format)s”"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\detail.py:56
#, python-format
msgid "No %(verbose_name)s found matching the query"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:70
msgid "Page is not “last”, nor can it be converted to an int."
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:77
#, python-format
msgid "Invalid page (%(page_number)s): %(message)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\generic\list.py:169
#, python-format
msgid "Empty list and “%(class_name)s.allow_empty” is False."
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:49
msgid "Directory indexes are not allowed here."
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:51
#, python-format
msgid "“%(path)s” does not exist"
msgstr ""

#: .\venv\Lib\site-packages\django\views\static.py:68
#: .\venv\Lib\site-packages\django\views\templates\directory_index.html:8
#: .\venv\Lib\site-packages\django\views\templates\directory_index.html:11
#, python-format
msgid "Index of %(directory)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:7
#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:220
msgid "The install worked successfully! Congratulations!"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:206
#, python-format
msgid ""
"View <a href=\"https://docs.djangoproject.com/en/%(version)s/releases/\" "
"target=\"_blank\" rel=\"noopener\">release notes</a> for Django %(version)s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:221
#, python-format
msgid ""
"You are seeing this page because <a href=\"https://docs.djangoproject.com/en/"
"%(version)s/ref/settings/#debug\" target=\"_blank\" rel=\"noopener"
"\">DEBUG=True</a> is in your settings file and you have not configured any "
"URLs."
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:229
msgid "Django Documentation"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:230
msgid "Topics, references, &amp; how-to’s"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:238
msgid "Tutorial: A Polling App"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:239
msgid "Get started with Django"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:247
msgid "Django Community"
msgstr ""

#: .\venv\Lib\site-packages\django\views\templates\default_urlconf.html:248
msgid "Connect, get help, or contribute"
msgstr ""

#, fuzzy
#~| msgid "Members"
#~ msgid "Membership Sales"
#~ msgstr "សមាជិក"

# Product-related terms
#, fuzzy
#~| msgid "Product Name"
#~ msgid "Product Sales"
#~ msgstr "ឈ្មោះផលិតផល"

#, fuzzy
#~| msgid "Payment"
#~ msgid "Salary Payment"
#~ msgstr "ការទូទាត់"

#, fuzzy
#~| msgid "Payment"
#~ msgid "Supplier Payment"
#~ msgstr "ការទូទាត់"

#, fuzzy
#~| msgid "Purchase Date"
#~ msgid "Equipment Purchase"
#~ msgstr "កាលបរិច្ឆេទទិញ"

#, fuzzy
#~| msgid "Legend Fitness Club"
#~ msgid "Legend Fitness Club - Login"
#~ msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#, fuzzy
#~| msgid "Gym Management System"
#~ msgid "Staff Management"
#~ msgstr "ប្រព័ន្ធគ្រប់គ្រងក្លឹបហាត់ប្រាណ"

#, fuzzy
#~| msgid "Legend Fitness Club"
#~ msgid "Secure login powered by Legend Fitness Club"
#~ msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#, fuzzy
#~| msgid "Low Stock"
#~ msgid "Low Stock Threshold"
#~ msgstr "ស្តុកទាប"

#~ msgid "Expiry Date"
#~ msgstr "ថ្ងៃផុតកំណត់"

#, fuzzy
#~| msgid "Logout"
#~ msgid "Logo"
#~ msgstr "ចាកចេញ"

#, fuzzy
#~| msgid "Legend Fitness Club"
#~ msgid "Legend Fitness Club. All rights reserved."
#~ msgstr "ក្លឹបហាត់ប្រាណ Legend Fitness"

#~ msgid "Quick View"
#~ msgstr "មើលរហ័ស"

#~ msgid "Estimated Funds"
#~ msgstr "ប្រាក់ប្រមាណ"

#~ msgid "Staff"
#~ msgstr "បុគ្គលិក"

#, fuzzy
#~| msgid "Member Report"
#~ msgid "New Member Form"
#~ msgstr "របាយការណ៍សមាជិក"

#, fuzzy
#~| msgid "Gender"
#~ msgid "Select Gender"
#~ msgstr "ភេទ"

#~ msgid "Male"
#~ msgstr "ប្រុស"

#~ msgid "Female"
#~ msgstr "ស្រី"

#, fuzzy
#~| msgid "Phone Number"
#~ msgid "Phone Number"
#~ msgstr "លេខទូរស័ព្ទ"

#, fuzzy
#~| msgid "Amount"
#~ msgid "Due Amount"
#~ msgstr "ចំនួនទឹកប្រាក់"

#~ msgid "Select CSV File"
#~ msgstr "ជ្រើសរើសឯកសារ CSV"

#~ msgid "CSV should have the following columns:"
#~ msgstr "CSV គួរមានជួរឈរដូចខាងក្រោម៖"

#~ msgid "Import"
#~ msgstr "នាំចូល"

#~ msgid "Set Active"
#~ msgstr "កំណត់ឱ្យសកម្ម"

#~ msgid "Set Inactive"
#~ msgstr "កំណត់ឱ្យអសកម្ម"

#~ msgid "Reactivate Products with Stock"
#~ msgstr "ធ្វើឱ្យផលិតផលដែលមានស្តុកសកម្មឡើងវិញ"

#~ msgid "Export Selected"
#~ msgstr "នាំចេញអ្វីដែលបានជ្រើសរើស"

#~ msgid "Apply"
#~ msgstr "អនុវត្ត"

#~ msgid "Stock Status"
#~ msgstr "ស្ថានភាពស្តុក"

#~ msgid "No image"
#~ msgstr "គ្មានរូបភាព"

#~ msgid "Out of Stock"
#~ msgstr "អស់ស្តុក"

#~ msgid "Low Stock"
#~ msgstr "ស្តុកទាប"

#~ msgid "In Stock"
#~ msgstr "មានស្តុក"

#~ msgid "Auto-deactivated (Out of Stock)"
#~ msgstr "បិទដោយស្វ័យប្រវត្តិ (អស់ស្តុក)"

#, fuzzy
#~| msgid "Inventory Report"
#~ msgid "Inventory"
#~ msgstr "របាយការណ៍ស្តុក"

#~ msgid "Login"
#~ msgstr "ចូល"

#~ msgid "Products Dashboard"
#~ msgstr "ផលិតផល"

#~ msgid "Members Dashboard"
#~ msgstr "សមាជិក"

#~ msgid "Products List"
#~ msgstr "បញ្ជីផលិតផល"

#~ msgid "Inactive Status"
#~ msgstr "អសកម្ម"

# Common UI elements
#~ msgid "Save"
#~ msgstr "រក្សាទុក"

#~ msgid "Edit Item"
#~ msgstr "កែប្រែ"

#~ msgid "Print Document"
#~ msgstr "បោះពុម្ព"

#~ msgid "Export Data"
#~ msgstr "នាំចេញ"

#~ msgid "Import Data"
#~ msgstr "នាំចូល"

#~ msgid "Email"
#~ msgstr "អ៊ីមែល"

# Package-related terms
#~ msgid "Package Name"
#~ msgstr "ឈ្មោះកញ្ចប់"

#~ msgid "Card"
#~ msgstr "កាត"

#~ msgid "Stock"
#~ msgstr "ស្តុក"

#~ msgid "Product Quantity"
#~ msgstr "បរិមាណ"

#~ msgid "Yesterday"
#~ msgstr "ម្សិលមិញ"

#~ msgid "Last Month"
#~ msgstr "ខែមុន"

#~ msgid "This Year"
#~ msgstr "ឆ្នាំនេះ"

# Report-related terms
#~ msgid "Income"
#~ msgstr "ចំណូល"

#~ msgid "Expense"
#~ msgstr "ចំណាយ"

#~ msgid "Profit"
#~ msgstr "ប្រាក់ចំណេញ"

#~ msgid "Loss"
#~ msgstr "ការខាតបង់"

#~ msgid "Balance"
#~ msgstr "សមតុល្យ"

# Notification messages
#~ msgid "Successfully saved"
#~ msgstr "រក្សាទុកដោយជោគជ័យ"

#~ msgid "Successfully deleted"
#~ msgstr "លុបដោយជោគជ័យ"

#~ msgid "Successfully updated"
#~ msgstr "ធ្វើបច្ចុប្បន្នភាពដោយជោគជ័យ"

#~ msgid "An error occurred"
#~ msgstr "មានកំហុសមួយបានកើតឡើង"

#~ msgid "Are you sure?"
#~ msgstr "តើអ្នកប្រាកដទេ?"
