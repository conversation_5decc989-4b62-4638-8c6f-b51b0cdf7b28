from django.db import models
from django.utils.translation import gettext_lazy as _

class ReportTemplate(models.Model):
    """
    Model for storing report templates
    """
    TEMPLATE_TYPES = (
        ('income', 'Income Report'),
        ('expense', 'Expense Report'),
        ('balance', 'Balance Report'),
    )

    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    is_default = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Styling options
    header_color = models.Char<PERSON>ield(max_length=20, default='#2563eb')
    text_color = models.CharField(max_length=20, default='#333333')
    accent_color = models.Char<PERSON>ield(max_length=20, default='#2563eb')
    background_color = models.CharField(max_length=20, default='#ffffff')
    table_header_color = models.Char<PERSON><PERSON>(max_length=20, default='#f8f9fa')

    # Content options
    show_logo = models.<PERSON><PERSON>anField(default=True)
    show_footer = models.BooleanField(default=True)
    footer_text = models.CharField(max_length=255, default='Legend Fitness Club')

    class Meta:
        verbose_name = _('Report Template')
        verbose_name_plural = _('Report Templates')
        unique_together = ('is_default', 'template_type')

    def __str__(self):
        return f"{self.name} ({self.get_template_type_display()})"

    def save(self, *args, **kwargs):
        # If this template is being set as default, unset default for other templates of the same type
        if self.is_default:
            ReportTemplate.objects.filter(
                template_type=self.template_type,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)

        # If no default template exists for this type, set this one as default
        if not ReportTemplate.objects.filter(template_type=self.template_type, is_default=True).exists():
            self.is_default = True

        super().save(*args, **kwargs)
