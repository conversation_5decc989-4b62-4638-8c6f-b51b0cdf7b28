# Generated by Django 5.0.2 on 2025-05-10 15:00

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('template_type', models.CharField(choices=[('income', 'Income Report'), ('expense', 'Expense Report'), ('balance', 'Balance Report')], max_length=20)),
                ('is_default', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('header_color', models.CharField(default='#2563eb', max_length=20)),
                ('text_color', models.Char<PERSON><PERSON>(default='#333333', max_length=20)),
                ('accent_color', models.Char<PERSON>ield(default='#2563eb', max_length=20)),
                ('background_color', models.CharField(default='#ffffff', max_length=20)),
                ('table_header_color', models.CharField(default='#f8f9fa', max_length=20)),
                ('show_logo', models.BooleanField(default=True)),
                ('show_footer', models.BooleanField(default=True)),
                ('footer_text', models.CharField(default='Legend Fitness Club', max_length=255)),
            ],
            options={
                'verbose_name': 'Report Template',
                'verbose_name_plural': 'Report Templates',
                'unique_together': {('is_default', 'template_type')},
            },
        ),
    ]
