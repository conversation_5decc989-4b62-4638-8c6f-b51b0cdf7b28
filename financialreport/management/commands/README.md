# Financial Report Templates

This directory contains management commands for the financial report system.

## Default Templates

The `create_default_templates.py` command creates three default templates for the financial report system:

1. **Standard Income Report** - A blue-themed template for income reports
2. **Standard Expense Report** - A red-themed template for expense reports
3. **Standard Balance Report** - A green-themed template for balance reports

These templates are designed with a clean, professional look that matches the updated design of the financial report system.

## Usage

To create the default templates, run:

```bash
python manage.py create_default_templates
```

This command will create the templates if they don't already exist. If default templates already exist for any report type, the command will skip creating them.

## Template Properties

Each template has the following properties:

- **Name**: A descriptive name (e.g., "Standard Income Report")
- **Type**: The report type (income, expense, or balance)
- **Default**: Set to true to make it the default template for its type
- **Colors**: Appropriate color scheme for each report type
  - Income: Blue (#2563eb)
  - Expense: Red (#e11d48)
  - Balance: Green (#16a34a)
- **Content Options**: Logo and footer settings

## Customization

Users can customize these templates or create new ones through the web interface at:

```
/financialreport/templates/
```

## Design Principles

The templates follow these design principles:

1. **Clean and Professional**: Minimal design with clear information hierarchy
2. **Consistent Typography**: Using Open Sans font family for better readability
3. **Appropriate Color Coding**: Blue for income, red for expenses, green for balance
4. **Proper Spacing**: Adequate spacing between elements for better readability
5. **Universal Design**: No region-specific elements, suitable for any business context
