channels-4.0.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
channels-4.0.0.dist-info/LICENSE,sha256=uEZBXRtRTpwd_xSiLeuQbXlLxUbKYSn5UKGM0JHipmk,1552
channels-4.0.0.dist-info/METADATA,sha256=YMIcmdUYPo-MK6XY_r7LIaRB3QZlhiQg6dvpnJHfa9Q,1530
channels-4.0.0.dist-info/RECORD,,
channels-4.0.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
channels-4.0.0.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
channels-4.0.0.dist-info/top_level.txt,sha256=5-YaD2ZIFwhfgn0xikDTCwaofGY9Tg_HVyZkw2ocUnA,9
channels/__init__.py,sha256=m4IWu2y-B3ioVoHqxlXa-AeV2tXiYLj-EAG5t1uMsG0,58
channels/__pycache__/__init__.cpython-312.pyc,,
channels/__pycache__/apps.cpython-312.pyc,,
channels/__pycache__/auth.cpython-312.pyc,,
channels/__pycache__/consumer.cpython-312.pyc,,
channels/__pycache__/db.cpython-312.pyc,,
channels/__pycache__/exceptions.cpython-312.pyc,,
channels/__pycache__/layers.cpython-312.pyc,,
channels/__pycache__/middleware.cpython-312.pyc,,
channels/__pycache__/routing.cpython-312.pyc,,
channels/__pycache__/sessions.cpython-312.pyc,,
channels/__pycache__/utils.cpython-312.pyc,,
channels/__pycache__/worker.cpython-312.pyc,,
channels/apps.py,sha256=ghfLPMxM-Y7KnbVeHqe53bdYSRNuvcBEbC5XHTQcCvE,122
channels/auth.py,sha256=1PdA1_FP9fOx1WLcJrIJKDhZA9J2wAXMYDmO3bJUlxM,6547
channels/consumer.py,sha256=_I7_mson7Ncyv-NGa1VOi8X95uIp9vgWbMpHbG8Pf_Y,4316
channels/db.py,sha256=ydC3ph-l7urfVdpatHqljRtA9kfECs1e9duKz8aATE8,562
channels/exceptions.py,sha256=-AJy2YuE4geaucjGDo59ZA78a-m5ZMJNOUS_QVzSASo,1119
channels/generic/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
channels/generic/__pycache__/__init__.cpython-312.pyc,,
channels/generic/__pycache__/http.cpython-312.pyc,,
channels/generic/__pycache__/websocket.cpython-312.pyc,,
channels/generic/http.py,sha256=5pCyfkFellIiLDLeSoH3qCZ28NG3NLLjZYMlrN9dn7I,3089
channels/generic/websocket.py,sha256=1hB_6be6twz-8N1rL8aOpASFKYNfH7fh_6qBj1e_qjI,8563
channels/layers.py,sha256=mf5gwmJRGC4eTA_BO3YqFKQHYu4VustjcCO9k8d2REM,11953
channels/management/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
channels/management/__pycache__/__init__.cpython-312.pyc,,
channels/management/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
channels/management/commands/__pycache__/__init__.cpython-312.pyc,,
channels/management/commands/__pycache__/runworker.cpython-312.pyc,,
channels/management/commands/runworker.py,sha256=jxrR0_x0mdG7rlRV4cNtuWB6ZylzSeBkyiEGZt72lV4,1594
channels/middleware.py,sha256=6PQuE_-vzH7QF2bHcbgyBBJYnsyZBhhMFwRUrj6hz7o,756
channels/routing.py,sha256=DtiMoDCO3V_KH59_ngcu2is-hw2b0DM9BZFQ4aKE1aI,5772
channels/security/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
channels/security/__pycache__/__init__.cpython-312.pyc,,
channels/security/__pycache__/websocket.cpython-312.pyc,,
channels/security/websocket.py,sha256=zZ3nfhrXqYK5WbPRaIesA431_5JW-NwbKezQo_AvMnI,5800
channels/sessions.py,sha256=RQ9XFmYZb7Sm8SEG2zmf9Asyuml85VxCRJajc5TW5_g,10062
channels/testing/__init__.py,sha256=4Gv--eDMF5__J4ESnf4JLPxeYn_1I9tPV1x0GJUL_98,343
channels/testing/__pycache__/__init__.cpython-312.pyc,,
channels/testing/__pycache__/http.cpython-312.pyc,,
channels/testing/__pycache__/live.cpython-312.pyc,,
channels/testing/__pycache__/websocket.cpython-312.pyc,,
channels/testing/http.py,sha256=9XR-hXcWvx_BmXFwetEp7jez9yf7daS1LbWWtSP_r5s,2043
channels/testing/live.py,sha256=FbS_CyhiwxEfuphccYWjOWlzaGaEUnS-aUDIPmGOILM,2600
channels/testing/websocket.py,sha256=d6YAJhLCwEL6n-u_LEEFzwjZl_xA5PaQd8G0tydCTfk,3901
channels/utils.py,sha256=ZXkTASx-Qf9fcJjCHQFJkkH_oTDrcHerjcyyyHU8iyE,2177
channels/worker.py,sha256=6z_R8hUqlD9bz2vNHi1NCgZyxLMNzU_0t6tF-APk7yI,1687
