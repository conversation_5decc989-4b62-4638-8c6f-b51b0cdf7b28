# automatically generated by the FlatBuffers compiler, do not modify

# namespace: proto

import flatbuffers
from flatbuffers.compat import import_numpy
np = import_numpy()

class HelloNew(object):
    __slots__ = ['_tab']

    @classmethod
    def GetRootAs(cls, buf, offset=0):
        n = flatbuffers.encode.Get(flatbuffers.packer.uoffset, buf, offset)
        x = HelloNew()
        x.Init(buf, n + offset)
        return x

    @classmethod
    def GetRootAsHelloNew(cls, buf, offset=0):
        """This method is deprecated. Please switch to GetRootAs."""
        return cls.GetRootAs(buf, offset)
    # HelloNew
    def Init(self, buf, pos):
        self._tab = flatbuffers.table.Table(buf, pos)

    # HelloNew
    def Roles(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(4))
        if o != 0:
            x = self._tab.Indirect(o + self._tab.Pos)
            from wamp.proto.ClientRoles import ClientRoles
            obj = ClientRoles()
            obj.Init(self._tab.Bytes, x)
            return obj
        return None

    # HelloNew
    def Realm(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(6))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # HelloNew
    def Authid(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(8))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # HelloNew
    def Authrole(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(10))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

    # HelloNew
    def Authmode(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(12))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # HelloNew
    def Authfactor1Type(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(14))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # HelloNew
    def Authfactor1(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(16))
        if o != 0:
            from flatbuffers.table import Table
            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None

    # HelloNew
    def Authfactor2Type(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(18))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # HelloNew
    def Authfactor2(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(20))
        if o != 0:
            from flatbuffers.table import Table
            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None

    # HelloNew
    def Authfactor3Type(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(22))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint8Flags, o + self._tab.Pos)
        return 0

    # HelloNew
    def Authfactor3(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(24))
        if o != 0:
            from flatbuffers.table import Table
            obj = Table(bytearray(), 0)
            self._tab.Union(obj, o)
            return obj
        return None

    # HelloNew
    def Resumable(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(26))
        if o != 0:
            return bool(self._tab.Get(flatbuffers.number_types.BoolFlags, o + self._tab.Pos))
        return False

    # HelloNew
    def ResumeSession(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(28))
        if o != 0:
            return self._tab.Get(flatbuffers.number_types.Uint64Flags, o + self._tab.Pos)
        return 0

    # HelloNew
    def ResumeToken(self):
        o = flatbuffers.number_types.UOffsetTFlags.py_type(self._tab.Offset(30))
        if o != 0:
            return self._tab.String(o + self._tab.Pos)
        return None

def HelloNewStart(builder): builder.StartObject(14)
def Start(builder):
    return HelloNewStart(builder)
def HelloNewAddRoles(builder, roles): builder.PrependUOffsetTRelativeSlot(0, flatbuffers.number_types.UOffsetTFlags.py_type(roles), 0)
def AddRoles(builder, roles):
    return HelloNewAddRoles(builder, roles)
def HelloNewAddRealm(builder, realm): builder.PrependUOffsetTRelativeSlot(1, flatbuffers.number_types.UOffsetTFlags.py_type(realm), 0)
def AddRealm(builder, realm):
    return HelloNewAddRealm(builder, realm)
def HelloNewAddAuthid(builder, authid): builder.PrependUOffsetTRelativeSlot(2, flatbuffers.number_types.UOffsetTFlags.py_type(authid), 0)
def AddAuthid(builder, authid):
    return HelloNewAddAuthid(builder, authid)
def HelloNewAddAuthrole(builder, authrole): builder.PrependUOffsetTRelativeSlot(3, flatbuffers.number_types.UOffsetTFlags.py_type(authrole), 0)
def AddAuthrole(builder, authrole):
    return HelloNewAddAuthrole(builder, authrole)
def HelloNewAddAuthmode(builder, authmode): builder.PrependUint8Slot(4, authmode, 0)
def AddAuthmode(builder, authmode):
    return HelloNewAddAuthmode(builder, authmode)
def HelloNewAddAuthfactor1Type(builder, authfactor1Type): builder.PrependUint8Slot(5, authfactor1Type, 0)
def AddAuthfactor1Type(builder, authfactor1Type):
    return HelloNewAddAuthfactor1Type(builder, authfactor1Type)
def HelloNewAddAuthfactor1(builder, authfactor1): builder.PrependUOffsetTRelativeSlot(6, flatbuffers.number_types.UOffsetTFlags.py_type(authfactor1), 0)
def AddAuthfactor1(builder, authfactor1):
    return HelloNewAddAuthfactor1(builder, authfactor1)
def HelloNewAddAuthfactor2Type(builder, authfactor2Type): builder.PrependUint8Slot(7, authfactor2Type, 0)
def AddAuthfactor2Type(builder, authfactor2Type):
    return HelloNewAddAuthfactor2Type(builder, authfactor2Type)
def HelloNewAddAuthfactor2(builder, authfactor2): builder.PrependUOffsetTRelativeSlot(8, flatbuffers.number_types.UOffsetTFlags.py_type(authfactor2), 0)
def AddAuthfactor2(builder, authfactor2):
    return HelloNewAddAuthfactor2(builder, authfactor2)
def HelloNewAddAuthfactor3Type(builder, authfactor3Type): builder.PrependUint8Slot(9, authfactor3Type, 0)
def AddAuthfactor3Type(builder, authfactor3Type):
    return HelloNewAddAuthfactor3Type(builder, authfactor3Type)
def HelloNewAddAuthfactor3(builder, authfactor3): builder.PrependUOffsetTRelativeSlot(10, flatbuffers.number_types.UOffsetTFlags.py_type(authfactor3), 0)
def AddAuthfactor3(builder, authfactor3):
    return HelloNewAddAuthfactor3(builder, authfactor3)
def HelloNewAddResumable(builder, resumable): builder.PrependBoolSlot(11, resumable, 0)
def AddResumable(builder, resumable):
    return HelloNewAddResumable(builder, resumable)
def HelloNewAddResumeSession(builder, resumeSession): builder.PrependUint64Slot(12, resumeSession, 0)
def AddResumeSession(builder, resumeSession):
    return HelloNewAddResumeSession(builder, resumeSession)
def HelloNewAddResumeToken(builder, resumeToken): builder.PrependUOffsetTRelativeSlot(13, flatbuffers.number_types.UOffsetTFlags.py_type(resumeToken), 0)
def AddResumeToken(builder, resumeToken):
    return HelloNewAddResumeToken(builder, resumeToken)
def HelloNewEnd(builder): return builder.EndObject()
def End(builder):
    return HelloNewEnd(builder)