Metadata-Version: 2.1
Name: django-decorator-include
Version: 3.0
Summary: Include Django URL patterns with decorators
Home-page: https://github.com/twidi/django-decorator-include
Author: <PERSON>
Author-email: <EMAIL>
License: BSD
Keywords: django,urls
Platform: UNKNOWN
Classifier: Development Status :: 5 - Production/Stable
Classifier: Framework :: Django
Classifier: Framework :: Django :: 2.0
Classifier: Framework :: Django :: 2.1
Classifier: Framework :: Django :: 2.2
Classifier: Framework :: Django :: 3.0
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.4
Classifier: Programming Language :: Python :: 3.5
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Topic :: Internet :: WWW/HTTP
Requires-Python: >=3.4
Requires-Dist: Django (>=2.0)
Provides-Extra: dev
Requires-Dist: flake8 ; extra == 'dev'
Requires-Dist: isort ; extra == 'dev'

django-decorator-include
========================

Include Django URL patterns with decorators.

Maintained by Stéphane "Twidi" Angel, and Jon Dufresne on
https://github.com/twidi/django-decorator-include
based on the original work from Jeff Kistler on
https://github.com/jeffkistler/django-decorator-include.

.. image:: https://badge.fury.io/py/django-decorator-include.svg
    :target: https://badge.fury.io/py/django-decorator-include

.. image:: https://travis-ci.org/twidi/django-decorator-include.svg?branch=develop
    :target: https://travis-ci.org/twidi/django-decorator-include

Installation
------------

Assuming you have pip installed, run the following command to install from
PyPI::

    pip install django-decorator-include


Usage
-----

``decorator_include`` is intended for use in URL confs as a replacement for the
``django.conf.urls.include`` function. It works in almost the same way as
``include`` however the first argument should be either a decorator or an
iterable of decorators to apply to all included views (if an iterable, the order of the
decorators is the order in which the functions will be applied on the views).
Herei s an example URL conf::

    from django.contrib import admin
    from django.core.exceptions import PermissionDenied
    from django.urls import path
    from django.contrib.auth.decorators import login_required, user_passes_test

    from decorator_include import decorator_include

    from mysite.views import index

    def only_user(username):
        def check(user):
            if user.is_authenticated and user.username == username:
                return True
            raise PermissionDenied
        return user_passes_test(check)

    urlpatterns = [
        path('', views.index, name='index'),
        # will redirect to login page if not authenticated
        path('secret/', decorator_include(login_required, 'mysite.secret.urls')),
        # will redirect to login page if not authenticated
        # will return a 403 http error if the user does not have the "god" username
        path('admin/', decorator_include([login_required, only_user('god')], admin.site.urls),
    ]


Supported versions
------------------

=============== ==================
Django versions Python versions
=============== ==================
2.0             3.4, 3.5, 3.6, 3.7
2.1             3.5, 3.6, 3.7
2.2             3.5, 3.6, 3.7, 3.8
3.0             3.6, 3.7, 3.8
=============== ==================

All library versions to use for old Django/Python support
---------------------------------------------------------

=============== ======================= ==================
Django versions Python versions         Library versions
=============== ======================= ==================
1.4, 1.5        2.6, 2.7                1.2
1.6             2.6, 2.7, 3.2, 3.3      1.2
1.7             2.7, 3.2, 3.3, 3.4      1.2
1.8             2.7, 3.2, 3.3, 3.4, 3.5 1.3
1.9, 1.10       2.7, 3.4, 3.5           1.3
1.11            2.7, 3.4, 3.5, 3.6      1.4.x (>=1.4.1,<2)
2.0             3.4, 3.5, 3.6, 3.7      3.0
2.1             3.5, 3.6, 3.7           3.0
2.2             3.5, 3.6, 3.7, 3.8      3.0
3.0             3.6, 3.7, 3.8           3.0
=============== ======================= ==================


Development
-----------

Make sure you are in a virtualenv on a valid python version.

Grab the sources from Github::

    git clone -b develop https://github.com/twidi/django-decorator-include.git


Then go into the newly created ``django-decorator-include`` directory and install
the few needed libraries::

    pip install -r requirements.txt


To run the tests, this library provides a test project, so you can launch
them this way::

    django-admin test --settings=tests.settings tests

Or simply launch the ``runtests.sh`` script (it will run this exact command)::

    ./runtests.sh

Base your work on the ``develop`` branch. Iit should be the default branch on
git assuming you used the ``-b develop`` argument on the ``git clone``
command as shown above.

When creating the pull request, ensure you are using the correct base
(twidi/django-decorator-include on develop).


