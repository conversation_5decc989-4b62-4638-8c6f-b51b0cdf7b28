Metadata-Version: 2.3
Name: Twisted
Version: 24.11.0
Summary: An asynchronous networking framework written in Python
Project-URL: Changelog, https://github.com/twisted/twisted/blob/HEAD/NEWS.rst
Project-URL: Documentation, https://docs.twisted.org/
Project-URL: Homepage, https://twisted.org/
Project-URL: Issues, https://github.com/twisted/twisted/issues
Project-URL: Source, https://github.com/twisted/twisted
Project-URL: Funding-PSF, https://psfmember.org/civicrm/contribute/transact/?reset=1&id=44
Project-URL: Funding-GitHub, https://github.com/sponsors/twisted
Author-email: Twisted Matrix Community <<EMAIL>>
License: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Requires-Python: >=3.8.0
Requires-Dist: attrs>=22.2.0
Requires-Dist: automat>=24.8.0
Requires-Dist: constantly>=15.1
Requires-Dist: hyperlink>=17.1.1
Requires-Dist: incremental>=24.7.0
Requires-Dist: typing-extensions>=4.2.0
Requires-Dist: zope-interface>=5
Provides-Extra: all-non-platform
Requires-Dist: appdirs>=1.4.0; extra == 'all-non-platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'all-non-platform'
Requires-Dist: cryptography>=3.3; extra == 'all-non-platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'all-non-platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'all-non-platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'all-non-platform'
Requires-Dist: hypothesis>=6.56; extra == 'all-non-platform'
Requires-Dist: idna>=2.4; extra == 'all-non-platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'all-non-platform'
Requires-Dist: pyhamcrest>=2; extra == 'all-non-platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'all-non-platform'
Requires-Dist: pyserial>=3.0; extra == 'all-non-platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'all-non-platform'
Requires-Dist: service-identity>=18.1.0; extra == 'all-non-platform'
Provides-Extra: all_non_platform
Requires-Dist: appdirs>=1.4.0; extra == 'all_non_platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'all_non_platform'
Requires-Dist: cryptography>=3.3; extra == 'all_non_platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'all_non_platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'all_non_platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'all_non_platform'
Requires-Dist: hypothesis>=6.56; extra == 'all_non_platform'
Requires-Dist: idna>=2.4; extra == 'all_non_platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'all_non_platform'
Requires-Dist: pyhamcrest>=2; extra == 'all_non_platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'all_non_platform'
Requires-Dist: pyserial>=3.0; extra == 'all_non_platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'all_non_platform'
Requires-Dist: service-identity>=18.1.0; extra == 'all_non_platform'
Provides-Extra: conch
Requires-Dist: appdirs>=1.4.0; extra == 'conch'
Requires-Dist: bcrypt>=3.1.3; extra == 'conch'
Requires-Dist: cryptography>=3.3; extra == 'conch'
Provides-Extra: dev
Requires-Dist: coverage~=7.5; extra == 'dev'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'dev'
Requires-Dist: httpx[http2]>=0.27; extra == 'dev'
Requires-Dist: hypothesis>=6.56; extra == 'dev'
Requires-Dist: pydoctor~=23.9.0; extra == 'dev'
Requires-Dist: pyflakes~=2.2; extra == 'dev'
Requires-Dist: pyhamcrest>=2; extra == 'dev'
Requires-Dist: python-subunit~=1.4; extra == 'dev'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'dev'
Requires-Dist: sphinx<7,>=6; extra == 'dev'
Requires-Dist: towncrier~=23.6; extra == 'dev'
Requires-Dist: twistedchecker~=0.7; extra == 'dev'
Provides-Extra: dev-release
Requires-Dist: pydoctor~=23.9.0; extra == 'dev-release'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'dev-release'
Requires-Dist: sphinx<7,>=6; extra == 'dev-release'
Requires-Dist: towncrier~=23.6; extra == 'dev-release'
Provides-Extra: dev_release
Requires-Dist: pydoctor~=23.9.0; extra == 'dev_release'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'dev_release'
Requires-Dist: sphinx<7,>=6; extra == 'dev_release'
Requires-Dist: towncrier~=23.6; extra == 'dev_release'
Provides-Extra: gtk-platform
Requires-Dist: appdirs>=1.4.0; extra == 'gtk-platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'gtk-platform'
Requires-Dist: cryptography>=3.3; extra == 'gtk-platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'gtk-platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'gtk-platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'gtk-platform'
Requires-Dist: hypothesis>=6.56; extra == 'gtk-platform'
Requires-Dist: idna>=2.4; extra == 'gtk-platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'gtk-platform'
Requires-Dist: pygobject; extra == 'gtk-platform'
Requires-Dist: pyhamcrest>=2; extra == 'gtk-platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'gtk-platform'
Requires-Dist: pyserial>=3.0; extra == 'gtk-platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'gtk-platform'
Requires-Dist: service-identity>=18.1.0; extra == 'gtk-platform'
Provides-Extra: gtk_platform
Requires-Dist: appdirs>=1.4.0; extra == 'gtk_platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'gtk_platform'
Requires-Dist: cryptography>=3.3; extra == 'gtk_platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'gtk_platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'gtk_platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'gtk_platform'
Requires-Dist: hypothesis>=6.56; extra == 'gtk_platform'
Requires-Dist: idna>=2.4; extra == 'gtk_platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'gtk_platform'
Requires-Dist: pygobject; extra == 'gtk_platform'
Requires-Dist: pyhamcrest>=2; extra == 'gtk_platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'gtk_platform'
Requires-Dist: pyserial>=3.0; extra == 'gtk_platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'gtk_platform'
Requires-Dist: service-identity>=18.1.0; extra == 'gtk_platform'
Provides-Extra: http2
Requires-Dist: h2<5.0,>=3.2; extra == 'http2'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'http2'
Provides-Extra: macos-platform
Requires-Dist: appdirs>=1.4.0; extra == 'macos-platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'macos-platform'
Requires-Dist: cryptography>=3.3; extra == 'macos-platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'macos-platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'macos-platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'macos-platform'
Requires-Dist: hypothesis>=6.56; extra == 'macos-platform'
Requires-Dist: idna>=2.4; extra == 'macos-platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'macos-platform'
Requires-Dist: pyhamcrest>=2; extra == 'macos-platform'
Requires-Dist: pyobjc-core; extra == 'macos-platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'macos-platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'macos-platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'macos-platform'
Requires-Dist: pyserial>=3.0; extra == 'macos-platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'macos-platform'
Requires-Dist: service-identity>=18.1.0; extra == 'macos-platform'
Provides-Extra: macos_platform
Requires-Dist: appdirs>=1.4.0; extra == 'macos_platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'macos_platform'
Requires-Dist: cryptography>=3.3; extra == 'macos_platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'macos_platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'macos_platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'macos_platform'
Requires-Dist: hypothesis>=6.56; extra == 'macos_platform'
Requires-Dist: idna>=2.4; extra == 'macos_platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'macos_platform'
Requires-Dist: pyhamcrest>=2; extra == 'macos_platform'
Requires-Dist: pyobjc-core; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'macos_platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'macos_platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'macos_platform'
Requires-Dist: pyserial>=3.0; extra == 'macos_platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'macos_platform'
Requires-Dist: service-identity>=18.1.0; extra == 'macos_platform'
Provides-Extra: mypy
Requires-Dist: appdirs>=1.4.0; extra == 'mypy'
Requires-Dist: bcrypt>=3.1.3; extra == 'mypy'
Requires-Dist: coverage~=7.5; extra == 'mypy'
Requires-Dist: cryptography>=3.3; extra == 'mypy'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'mypy'
Requires-Dist: h2<5.0,>=3.2; extra == 'mypy'
Requires-Dist: httpx[http2]>=0.27; extra == 'mypy'
Requires-Dist: hypothesis>=6.56; extra == 'mypy'
Requires-Dist: idna>=2.4; extra == 'mypy'
Requires-Dist: mypy-zope==1.0.6; extra == 'mypy'
Requires-Dist: mypy==1.10.1; extra == 'mypy'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'mypy'
Requires-Dist: pydoctor~=23.9.0; extra == 'mypy'
Requires-Dist: pyflakes~=2.2; extra == 'mypy'
Requires-Dist: pyhamcrest>=2; extra == 'mypy'
Requires-Dist: pyopenssl>=21.0.0; extra == 'mypy'
Requires-Dist: pyserial>=3.0; extra == 'mypy'
Requires-Dist: python-subunit~=1.4; extra == 'mypy'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'mypy'
Requires-Dist: service-identity>=18.1.0; extra == 'mypy'
Requires-Dist: sphinx-rtd-theme~=1.3; extra == 'mypy'
Requires-Dist: sphinx<7,>=6; extra == 'mypy'
Requires-Dist: towncrier~=23.6; extra == 'mypy'
Requires-Dist: twistedchecker~=0.7; extra == 'mypy'
Requires-Dist: types-pyopenssl; extra == 'mypy'
Requires-Dist: types-setuptools; extra == 'mypy'
Provides-Extra: osx-platform
Requires-Dist: appdirs>=1.4.0; extra == 'osx-platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'osx-platform'
Requires-Dist: cryptography>=3.3; extra == 'osx-platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'osx-platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'osx-platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'osx-platform'
Requires-Dist: hypothesis>=6.56; extra == 'osx-platform'
Requires-Dist: idna>=2.4; extra == 'osx-platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'osx-platform'
Requires-Dist: pyhamcrest>=2; extra == 'osx-platform'
Requires-Dist: pyobjc-core; extra == 'osx-platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'osx-platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'osx-platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'osx-platform'
Requires-Dist: pyserial>=3.0; extra == 'osx-platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'osx-platform'
Requires-Dist: service-identity>=18.1.0; extra == 'osx-platform'
Provides-Extra: osx_platform
Requires-Dist: appdirs>=1.4.0; extra == 'osx_platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'osx_platform'
Requires-Dist: cryptography>=3.3; extra == 'osx_platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'osx_platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'osx_platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'osx_platform'
Requires-Dist: hypothesis>=6.56; extra == 'osx_platform'
Requires-Dist: idna>=2.4; extra == 'osx_platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'osx_platform'
Requires-Dist: pyhamcrest>=2; extra == 'osx_platform'
Requires-Dist: pyobjc-core; extra == 'osx_platform'
Requires-Dist: pyobjc-framework-cfnetwork; extra == 'osx_platform'
Requires-Dist: pyobjc-framework-cocoa; extra == 'osx_platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'osx_platform'
Requires-Dist: pyserial>=3.0; extra == 'osx_platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'osx_platform'
Requires-Dist: service-identity>=18.1.0; extra == 'osx_platform'
Provides-Extra: serial
Requires-Dist: pyserial>=3.0; extra == 'serial'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'serial'
Provides-Extra: test
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'test'
Requires-Dist: httpx[http2]>=0.27; extra == 'test'
Requires-Dist: hypothesis>=6.56; extra == 'test'
Requires-Dist: pyhamcrest>=2; extra == 'test'
Provides-Extra: tls
Requires-Dist: idna>=2.4; extra == 'tls'
Requires-Dist: pyopenssl>=21.0.0; extra == 'tls'
Requires-Dist: service-identity>=18.1.0; extra == 'tls'
Provides-Extra: windows-platform
Requires-Dist: appdirs>=1.4.0; extra == 'windows-platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'windows-platform'
Requires-Dist: cryptography>=3.3; extra == 'windows-platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'windows-platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'windows-platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'windows-platform'
Requires-Dist: hypothesis>=6.56; extra == 'windows-platform'
Requires-Dist: idna>=2.4; extra == 'windows-platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'windows-platform'
Requires-Dist: pyhamcrest>=2; extra == 'windows-platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'windows-platform'
Requires-Dist: pyserial>=3.0; extra == 'windows-platform'
Requires-Dist: pywin32!=226; extra == 'windows-platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'windows-platform'
Requires-Dist: service-identity>=18.1.0; extra == 'windows-platform'
Requires-Dist: twisted-iocpsupport>=1.0.2; extra == 'windows-platform'
Provides-Extra: windows_platform
Requires-Dist: appdirs>=1.4.0; extra == 'windows_platform'
Requires-Dist: bcrypt>=3.1.3; extra == 'windows_platform'
Requires-Dist: cryptography>=3.3; extra == 'windows_platform'
Requires-Dist: cython-test-exception-raiser<2,>=1.0.2; extra == 'windows_platform'
Requires-Dist: h2<5.0,>=3.2; extra == 'windows_platform'
Requires-Dist: httpx[http2]>=0.27; extra == 'windows_platform'
Requires-Dist: hypothesis>=6.56; extra == 'windows_platform'
Requires-Dist: idna>=2.4; extra == 'windows_platform'
Requires-Dist: priority<2.0,>=1.1.0; extra == 'windows_platform'
Requires-Dist: pyhamcrest>=2; extra == 'windows_platform'
Requires-Dist: pyopenssl>=21.0.0; extra == 'windows_platform'
Requires-Dist: pyserial>=3.0; extra == 'windows_platform'
Requires-Dist: pywin32!=226; extra == 'windows_platform'
Requires-Dist: pywin32!=226; (platform_system == 'Windows') and extra == 'windows_platform'
Requires-Dist: service-identity>=18.1.0; extra == 'windows_platform'
Requires-Dist: twisted-iocpsupport>=1.0.2; extra == 'windows_platform'
Description-Content-Type: text/x-rst

Twisted
#######

|gitter|_
|rtd|_
|pypi|_
|ci|_

For information on changes in this release, see the `NEWS <https://github.com/twisted/twisted/blob/trunk/NEWS.rst>`_ file.


Sponsors
--------

Twisted is an MIT-licensed open source project with its ongoing development made possible entirely by the support of community and these awesome sponsors.
If you'd like to join them, please consider `sponsoring Twisted's <https://docs.twisted.org/en/latest/development/sponsorship.html>`_ development.

|thinkst|_

|sftpplus|_


What is this?
-------------

Twisted is a Python event-based framework for internet applications.
It includes modules for many different purposes, including the following:

- ``twisted.web``: HTTP clients and servers, HTML templating, and a WSGI server
- ``twisted.conch``: SSHv2 and Telnet clients and servers and terminal emulators
- ``twisted.words``: Clients and servers for IRC, XMPP, and other IM protocols
- ``twisted.mail``: IMAPv4, POP3, SMTP clients and servers
- ``twisted.positioning``: Tools for communicating with NMEA-compatible GPS receivers
- ``twisted.names``: DNS client and tools for making your own DNS servers
- ``twisted.trial``: A unit testing framework that integrates well with Twisted-based code.

Twisted supports all major system event loops -- ``select`` (all platforms), ``poll`` (most POSIX platforms), ``epoll`` (Linux), ``kqueue`` (FreeBSD, macOS), IOCP (Windows), and various GUI event loops (GTK+2/3, Qt, wxWidgets).
Third-party reactors can plug into Twisted, and provide support for additional event loops.


Installing
----------

To install the latest version of Twisted using pip::

  $ pip install twisted

Additional instructions for installing this software are in `the installation instructions <https://docs.twisted.org/en/latest/installation.html>`_.


Documentation and Support
-------------------------

Twisted's documentation is available from the `Twisted Matrix Read The Docs website <https://docs.twisted.org/>`_.
This documentation contains how-tos, code examples, and an API reference.

Help is also available on the `Twisted mailing list <https://mail.python.org/mailman3/lists/twisted.python.org/>`_.

There is also an IRC channel, ``#twisted``,
on the `Libera.Chat <https://libera.chat/>`_ network.
A web client is available at `web.libera.chat <https://web.libera.chat/>`_.


Unit Tests
----------

Twisted has a comprehensive test suite, which can be run by ``tox``::

  $ tox -l                       # to view all test environments
  $ tox -e nocov                 # to run all the tests without coverage
  $ tox -e withcov               # to run all the tests with coverage
  $ tox -e alldeps-withcov-posix # install all dependencies, run tests with coverage on POSIX platform


You can test running the test suite under the different reactors with the ``TWISTED_REACTOR`` environment variable::

  $ env TWISTED_REACTOR=epoll tox -e alldeps-withcov-posix

Some of these tests may fail if you:

* don't have the dependencies required for a particular subsystem installed,
* have a firewall blocking some ports (or things like Multicast, which Linux NAT has shown itself to do), or
* run them as root.


Static Code Checkers
--------------------

You can ensure that code complies to Twisted `coding standards <https://docs.twisted.org/en/latest/development/coding-standard.html>`_::

  $ tox -e lint   # run pre-commit to check coding stanards
  $ tox -e mypy   # run MyPy static type checker to check for type errors

Or, for speed, use pre-commit directly::

  $ pipx run pre-commit run


Copyright
---------

All of the code in this distribution is Copyright (c) 2001-2024 Twisted Matrix Laboratories.

Twisted is made available under the MIT license.
The included `LICENSE <https://github.com/twisted/twisted/blob/trunk/LICENSE>`_ file describes this in detail.


Warranty
--------

  THIS SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EITHER
  EXPRESSED OR IMPLIED, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
  OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.  THE ENTIRE RISK AS
  TO THE USE OF THIS SOFTWARE IS WITH YOU.

  IN NO EVENT WILL ANY COPYRIGHT HOLDER, OR ANY OTHER PARTY WHO MAY MODIFY
  AND/OR REDISTRIBUTE THE LIBRARY, BE LIABLE TO YOU FOR ANY DAMAGES, EVEN IF
  SUCH HOLDER OR OTHER PARTY HAS BEEN ADVISED OF THE POSSIBILITY OF SUCH
  DAMAGES.

Again, see the included `LICENSE <https://github.com/twisted/twisted/blob/trunk/LICENSE>`_ file for specific legal details.


.. |pypi| image:: https://img.shields.io/pypi/v/twisted.svg
.. _pypi: https://pypi.python.org/pypi/twisted

.. |gitter| image:: https://img.shields.io/gitter/room/twisted/twisted.svg
.. _gitter: https://gitter.im/twisted/twisted

.. |ci| image:: https://github.com/twisted/twisted/actions/workflows/test.yaml/badge.svg
.. _ci: https://github.com/twisted/twisted

.. |rtd| image:: https://readthedocs.org/projects/twisted/badge/?version=latest&style=flat
.. _rtd: https://docs.twistedmatrix.com

.. |thinkst| image:: https://github.com/user-attachments/assets/a5b52432-2d18-4d91-a3c9-772fb2e02781
    :alt: Thinkst Canary
.. _thinkst: https://thinkst.com/

.. |sftpplus| image:: https://github.com/user-attachments/assets/5f585316-c7e8-4ef1-8fbb-923f0756ceed
    :alt: SFTPPlus
.. _sftpplus: https://www.sftpplus.com/
