from django.contrib import admin
from .models import PayPerVisit, PayPerVisitSettings

@admin.register(PayPerVisit)
class PayPerVisitAdmin(admin.ModelAdmin):
    list_display = ('trxId', 'amount', 'num_people', 'date', 'received_by')
    search_fields = ('trxId',)
    list_filter = ('date',)

@admin.register(PayPerVisitSettings)
class PayPerVisitSettingsAdmin(admin.ModelAdmin):
    list_display = ('price_per_person', 'last_updated')

    def has_add_permission(self, request):
        # Only allow one settings instance
        return not PayPerVisitSettings.objects.exists()

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the settings
        return False
