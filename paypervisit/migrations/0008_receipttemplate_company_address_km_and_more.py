# Generated by Django 5.0.2 on 2025-05-18 00:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0007_receipttemplate_company_address_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='receipttemplate',
            name='company_address_km',
            field=models.CharField(blank=True, default='ផ្លូវ ១២៣, ភ្នំពេញ, កម្ពុជា', max_length=200, verbose_name='Company Address (Khmer)'),
        ),
        migrations.AddField(
            model_name='receipttemplate',
            name='company_phone_km',
            field=models.CharField(blank=True, default='+855 12 345 678', max_length=50, verbose_name='Company Phone (Khmer)'),
        ),
        migrations.AddField(
            model_name='receipttemplate',
            name='footer_text_km',
            field=models.CharField(blank=True, default='សូមអរគុណសម្រាប់ការទស្សនាលេហ្គិន ហ្វីតនេស!', max_length=200, verbose_name='Footer Text (Khmer)'),
        ),
        migrations.AddField(
            model_name='receipttemplate',
            name='header_text_km',
            field=models.CharField(blank=True, default='លេហ្គិន ហ្វីតនេស', max_length=200, verbose_name='Header Text (Khmer)'),
        ),
        migrations.AddField(
            model_name='receipttemplate',
            name='subheader_text_km',
            field=models.CharField(blank=True, default='បង្កាន់ដៃបង់ប្រាក់', max_length=200, verbose_name='Subheader Text (Khmer)'),
        ),
        migrations.AlterField(
            model_name='receipttemplate',
            name='company_address',
            field=models.CharField(default='123 Main Street, Phnom Penh, Cambodia', max_length=200, verbose_name='Company Address (English)'),
        ),
        migrations.AlterField(
            model_name='receipttemplate',
            name='footer_text',
            field=models.CharField(default='Thank you for visiting Legend Fitness!', max_length=200, verbose_name='Footer Text (English)'),
        ),
        migrations.AlterField(
            model_name='receipttemplate',
            name='header_text',
            field=models.CharField(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text (English)'),
        ),
        migrations.AlterField(
            model_name='receipttemplate',
            name='subheader_text',
            field=models.CharField(default='Pay-per-visit Receipt', max_length=200, verbose_name='Subheader Text (English)'),
        ),
    ]
