# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PayPerVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('amount', models.IntegerField(verbose_name='Amount')),
                ('num_people', models.IntegerField(verbose_name='Number of People')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='Date')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='PayPerVisitSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price_per_person', models.IntegerField(default=5000, verbose_name='Price Per Person')),
                ('quick_select_1', models.IntegerField(default=2, verbose_name='Quick Selection 1')),
                ('quick_select_2', models.IntegerField(default=5, verbose_name='Quick Selection 2')),
                ('quick_select_3', models.IntegerField(default=10, verbose_name='Quick Selection 3')),
                ('price_for_2', models.IntegerField(default=10000, verbose_name='Price For 2 People')),
                ('price_for_5', models.IntegerField(default=25000, verbose_name='Price For 5 People')),
                ('price_for_10', models.IntegerField(default=50000, verbose_name='Price For 10 People')),
                ('custom_price_1', models.IntegerField(default=10000, verbose_name='Custom Price 1')),
                ('custom_price_2', models.IntegerField(default=25000, verbose_name='Custom Price 2')),
                ('custom_price_3', models.IntegerField(default=50000, verbose_name='Custom Price 3')),
                ('last_updated', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
            ],
        ),
    ]
