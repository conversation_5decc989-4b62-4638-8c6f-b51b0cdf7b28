# Generated by Django 5.0.2 on 2025-05-09 02:42

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0004_paypervisitsettings_price_for_10_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='paypervisitsettings',
            name='custom_price_1',
            field=models.IntegerField(default=8000, verbose_name='Custom Price 1'),
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='custom_price_2',
            field=models.IntegerField(default=20000, verbose_name='Custom Price 2'),
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='custom_price_3',
            field=models.IntegerField(default=40000, verbose_name='Custom Price 3'),
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='quick_select_1',
            field=models.IntegerField(default=2, verbose_name='Quick Selection 1'),
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='quick_select_2',
            field=models.IntegerField(default=5, verbose_name='Quick Selection 2'),
        ),
        migrations.AddField(
            model_name='paypervisitsettings',
            name='quick_select_3',
            field=models.IntegerField(default=10, verbose_name='Quick Selection 3'),
        ),
    ]
