# Generated by Django 5.0.2 on 2025-05-18 01:38

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('paypervisit', '0009_remove_receipttemplate'),
    ]

    operations = [
        migrations.CreateModel(
            name='ReceiptTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.Char<PERSON>ield(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text (English)')),
                ('subheader_text', models.CharField(default='Pay-per-visit Receipt', max_length=200, verbose_name='Subheader Text (English)')),
                ('footer_text', models.CharField(default='Thank you for visiting Legend Fitness!', max_length=200, verbose_name='Footer Text (English)')),
                ('company_address', models.CharField(default='123 Main Street, Phnom Penh, Cambodia', max_length=200, verbose_name='Company Address (English)')),
                ('company_phone', models.CharField(default='+855 12 345 678', max_length=50, verbose_name='Company Phone')),
                ('header_text_km', models.CharField(blank=True, default='លេហ្គិន ហ្វីតនេស', max_length=200, verbose_name='Header Text (Khmer)')),
                ('subheader_text_km', models.CharField(blank=True, default='បង្កាន់ដៃបង់ប្រាក់', max_length=200, verbose_name='Subheader Text (Khmer)')),
                ('footer_text_km', models.CharField(blank=True, default='សូមអរគុណសម្រាប់ការទស្សនាលេហ្គិន ហ្វីតនេស!', max_length=200, verbose_name='Footer Text (Khmer)')),
                ('company_address_km', models.CharField(blank=True, default='ផ្លូវ ១២៣, ភ្នំពេញ, កម្ពុជា', max_length=200, verbose_name='Company Address (Khmer)')),
                ('company_phone_km', models.CharField(blank=True, default='+855 12 345 678', max_length=50, verbose_name='Company Phone (Khmer)')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='paypervisit_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#333333', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#1e40af', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=False, verbose_name='Show Signatures')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
        ),
        migrations.AlterField(
            model_name='paypervisitsettings',
            name='default_receipt_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='settings', to='paypervisit.receipttemplate'),
        ),
    ]
