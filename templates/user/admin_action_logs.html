                                             {% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Admin Action Logs" %} | Legend Fitness Club{% endblock %}

{% block body %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">{% trans "Admin Action Logs" %}</h1>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-lg font-semibold mb-4">{% trans "Log Entries" %}</h2>

        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{% trans "Time" %}</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{% trans "User" %}</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{% trans "Action" %}</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">{% trans "Description" %}</th>
                </tr>
            </thead>
            <tbody>
                {% for log in logs %}
                <tr class="border-b">
                    <td class="px-6 py-4">{{ log.action_time|date:"Y-m-d H:i:s" }}</td>
                    <td class="px-6 py-4">{{ log.user.username }}</td>
                    <td class="px-6 py-4">{{ log.get_action_type_display }}</td>
                    <td class="px-6 py-4">{{ log.description }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="px-6 py-4 text-center">{% trans "No logs found" %}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock body %}

{% block js %}
<script>
    console.log("Admin action logs page loaded");
</script>
{% endblock js %}
