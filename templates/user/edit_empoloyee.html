{% extends "../base.html" %}
{% load custom_filters %}


{% load user_filters %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit Employee</h3>
            <div class="flex space-x-2">
                <a href="{% url 'user:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Employee List
                </a>
            </div>
        </div>

        <!-- Form starts  -->
        <div class="formSection bg-white px-10 py-8 rounded shadow-md mb-4">
            <form class="grid grid-cols-3 gap-4" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="grid gap-2">
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300" name="username" type="text" placeholder="Username" value={{employee.username}} disabled />
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Name" value="{{employee.name}}" required />
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="salary" type="number" placeholder="Salary" required value="{{employee.salary|floatformat:'0'}}" />
                    <label for="photo" class="block text-sm font-medium text-gray-700">Photo (Optional)</label>
                    <input class="border w-full p-2 leading-tight bg-slate-100" name="photo" type="file" accept="image/*" />
                    {% if employee.photo %}
                    <div class="mt-2">
                        <img src="/media/{{ employee.photo }}" alt="{{ employee.name }}" class="w-20 h-20 rounded-full object-cover">
                    </div>
                    {% endif %}
                </div>
                <div class="grid gap-2">
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300" name="empId" type="text" placeholder="Employee ID" disabled value={{employee.emp_id}} />
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="phone" type="tel" placeholder="Phone" required value={{employee.phone}} />

                    <!-- Join Date -->
                    <label for="join_date" class="block text-sm font-medium text-gray-700">Join Date</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="join_date" type="date" value="{% if employee.join_date %}{{employee.join_date|date:'Y-m-d'}}{% endif %}" required />
                </div>
                <div class="grid gap-2">
                    <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role" id="role" class="border w-full p-4 leading-tight bg-slate-100" required>
                        <option value="" hidden>Select Role</option>
                        <option value="cashier" {% if employee.role == 'cashier' %}selected{% endif %}>Cashier</option>
                        <option value="coach" {% if employee.role == 'coach' %}selected{% endif %}>Coach</option>
                        <option value="cleaner" {% if employee.role == 'cleaner' %}selected{% endif %}>Cleaner</option>
                        <option value="security" {% if employee.role == 'security' %}selected{% endif %}>Security Guard</option>
                    </select>
                </div>
                <div class="col-span-3 grid grid-cols-2 gap-4">
                    <div>
                        <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100" name="address" placeholder="Address" rows="4">{{employee.address|split_address_schedule:"address"}}</textarea>
                    </div>
                    <div>
                        <label for="schedule" class="block text-sm font-medium text-gray-700">Work Schedule</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100" name="schedule" placeholder="Work Schedule Details" rows="4">{{employee.address|split_address_schedule:"schedule"}}</textarea>
                    </div>
                </div>
                <div>
                    <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Update</button>
                </div>
                <div>
                    {% if employee.is_active %}
                    <a href="{% url "user:deactivate" employee.id %}" class="bg-yellow-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Deactivate Account</a>
                    {% else %}
                    <a href="{% url "user:active" employee.id %}" class="bg-green-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Activate Account</a>
                    {% endif %}
                </div>
                <div>
                    <a href="{% url "user:delete" employee.id %}" class="bg-red-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Delete Account</a>
                </div>
            </form>
        </div>
        <!-- Salary history section removed -->
    </div>
</div>
{% endblock body %}
