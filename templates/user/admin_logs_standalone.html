<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Action Logs</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-2xl font-bold text-gray-800 mb-6">Admin Action Logs</h1>
        
        <div class="bg-white shadow-md rounded-lg p-6">
            <h2 class="text-lg font-semibold mb-4">Log Entries</h2>
            
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Description</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in logs %}
                    <tr class="border-b">
                        <td class="px-6 py-4">{{ log.action_time|date:"Y-m-d H:i:s" }}</td>
                        <td class="px-6 py-4">{{ log.user.username }}</td>
                        <td class="px-6 py-4">{{ log.get_action_type_display }}</td>
                        <td class="px-6 py-4">{{ log.description }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="4" class="px-6 py-4 text-center">No logs found</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</body>
</html>
