{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
  <div class="componentWrapper">
    <!-- Header with navigation -->
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-2xl font-bold">My Profile</h3>
      <div class="flex space-x-2">
        <a href="{% url 'user:profile' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
          <i class="fa-solid fa-user mr-2"></i>Profile
        </a>
        <a href="{% url 'user:change_password' %}" class="bg-green-900 text-white px-4 py-2 rounded">
          <i class="fa-solid fa-key mr-2"></i>Change Password
        </a>
      </div>
    </div>

    <!-- Profile Information -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- Left Column - Profile Photo and Basic Info -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="flex flex-col items-center">
          {% if user.photo %}
          <div class="w-32 h-32 mb-4">
            <img src="/media/{{ user.photo }}" alt="{{ user.name }}" class="w-full h-full rounded-full object-cover">
          </div>
          {% else %}
          <div class="w-32 h-32 mb-4 bg-gray-200 rounded-full flex items-center justify-center">
            <i class="fa-solid fa-user text-gray-500 text-5xl"></i>
          </div>
          {% endif %}
          
          <h2 class="text-xl font-semibold">{{ user.name|default:user.username }}</h2>
          <p class="text-gray-600 mb-2">{{ user.get_role_display_name }}</p>
          
          {% if user.is_active %}
          <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Active</span>
          {% else %}
          <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Inactive</span>
          {% endif %}
          
          <div class="mt-4 w-full">
            <div class="border-t border-gray-200 pt-4">
              <p class="text-sm text-gray-500">Employee ID</p>
              <p class="font-medium">{{ user.emp_id|default:"Not assigned" }}</p>
            </div>
            
            <div class="border-t border-gray-200 pt-4 mt-4">
              <p class="text-sm text-gray-500">Username</p>
              <p class="font-medium">{{ user.username }}</p>
            </div>
            
            <div class="border-t border-gray-200 pt-4 mt-4">
              <p class="text-sm text-gray-500">Join Date</p>
              <p class="font-medium">{{ user.join_date|default:"Not recorded" }}</p>
            </div>
            
            <div class="border-t border-gray-200 pt-4 mt-4">
              <p class="text-sm text-gray-500">Last Login</p>
              <p class="font-medium">{{ user.last_login|date:"Y-m-d H:i"|default:"Never" }}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Middle Column - Personal Information -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold mb-4 border-b pb-2">Personal Information</h3>
        
        <form method="post" enctype="multipart/form-data" class="space-y-4">
          {% csrf_token %}
          <input type="hidden" name="form_type" value="personal_info">
          
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
            <input type="text" id="name" name="name" value="{{ user.name|default:'' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
            <input type="email" id="email" name="email" value="{{ user.email|default:'' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          
          <div>
            <label for="phone" class="block text-sm font-medium text-gray-700">Phone Number</label>
            <input type="tel" id="phone" name="phone" value="{{ user.phone|default:'' }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          
          <div>
            <label for="dob" class="block text-sm font-medium text-gray-700">Date of Birth</label>
            <input type="date" id="dob" name="dob" value="{% if user.dob %}{{ user.dob|date:'Y-m-d' }}{% endif %}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
          </div>
          
          <div>
            <label for="gender" class="block text-sm font-medium text-gray-700">Gender</label>
            <select id="gender" name="gender" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
              <option value="" {% if not user.gender %}selected{% endif %}>Select Gender</option>
              <option value="male" {% if user.gender == 'male' %}selected{% endif %}>Male</option>
              <option value="female" {% if user.gender == 'female' %}selected{% endif %}>Female</option>
            </select>
          </div>
          
          <div>
            <label for="photo" class="block text-sm font-medium text-gray-700">Profile Photo</label>
            <input type="file" id="photo" name="photo" accept="image/*" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
          </div>
          
          <div>
            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Update Personal Information</button>
          </div>
        </form>
      </div>
      
      <!-- Right Column - Additional Information and Preferences -->
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-semibold mb-4 border-b pb-2">Additional Information</h3>
        
        <form method="post" class="space-y-4">
          {% csrf_token %}
          <input type="hidden" name="form_type" value="additional_info">
          
          <div>
            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
            <textarea id="address" name="address" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ user.address|split_address_schedule:'address'|default:'' }}</textarea>
          </div>
          
          <div>
            <label for="schedule" class="block text-sm font-medium text-gray-700">Work Schedule</label>
            <textarea id="schedule" name="schedule" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">{{ user.address|split_address_schedule:'schedule'|default:'' }}</textarea>
          </div>
          
          <h3 class="text-lg font-semibold mt-6 mb-4 border-b pb-2">Notification Preferences</h3>
          
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="email_notifications" name="email_notifications" type="checkbox" {% if user.receive_email_notifications %}checked{% endif %} class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="email_notifications" class="font-medium text-gray-700">Email Notifications</label>
              <p class="text-gray-500">Receive notifications via email</p>
            </div>
          </div>
          
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="system_notifications" name="system_notifications" type="checkbox" {% if user.receive_system_notifications %}checked{% endif %} class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="system_notifications" class="font-medium text-gray-700">System Notifications</label>
              <p class="text-gray-500">Receive notifications in the system</p>
            </div>
          </div>
          
          <div>
            <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-900 hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">Update Additional Information</button>
          </div>
        </form>
      </div>
    </div>
    
    <!-- Recent Activity Section -->
    <div class="bg-white p-6 rounded-lg shadow-md mt-6">
      <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
      
      {% if activities %}
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50"><tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {% for activity in activities %}
            <tr>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ activity.timestamp|date:"Y-m-d H:i" }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ activity.action }}</td>
              <td class="px-6 py-4 text-sm text-gray-500">{{ activity.details }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% else %}
      <div class="text-center py-4 text-gray-500">
        <p>No recent activity to display.</p>
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock body %}
