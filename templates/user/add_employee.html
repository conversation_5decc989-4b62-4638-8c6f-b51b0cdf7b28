{% extends "../base.html" %}
{% load custom_filters %}


{% load static %}

{% block extra_css %}
<style>
    /* Custom styles for datepicker */
    .datepicker-container {
        position: relative;
    }

    .datepicker-container .fa-calendar-alt {
        cursor: pointer;
    }

    .datepicker-dropdown {
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        padding: 0.5rem;
    }

    .datepicker table tr td.active,
    .datepicker table tr td.active:hover,
    .datepicker table tr td.active.disabled,
    .datepicker table tr td.active.disabled:hover {
        background-color: #1d4ed8 !important;
        background-image: none;
    }

    .datepicker table tr td.today {
        background-color: #e5edff !important;
        background-image: none;
        color: #1d4ed8 !important;
    }
</style>
{% endblock %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Page header with breadcrumbs -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Add New Employee</h1>
            <div class="flex items-center text-sm mt-2">
                <a href="{% url 'adminDashboard' %}" class="text-gray-500 hover:text-blue-600 transition-colors">
                    <i class="fas fa-home mr-1"></i>Dashboard
                </a>
                <span class="mx-2 text-gray-400">/</span>
                <a href="{% url 'user:index' %}" class="text-gray-500 hover:text-blue-600 transition-colors">
                    <i class="fas fa-users mr-1"></i>User Management
                </a>
                <span class="mx-2 text-gray-400">/</span>
                <span class="text-blue-600 font-medium">Add Employee</span>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="flex justify-end mb-6">
            <a href="{% url 'user:index' %}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded flex items-center justify-center transition-colors">
                <i class="fa-solid fa-arrow-left mr-2"></i>Back to User Management
            </a>
        </div>

        <!-- Form card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="border-b border-gray-200 px-6 py-4">
                <h2 class="text-xl font-semibold text-gray-800">Employee Information</h2>
                <p class="text-gray-600 text-sm mt-1">Register new employees with their personal and work details. For system access, use the User Registration form.</p>
            </div>

            <form class="p-6" method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <!-- Personal Information Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Personal Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Photo Upload -->
                        <div class="mb-4">
                            <label for="photo" class="block text-sm font-medium text-gray-700 mb-1">Photo</label>
                            <input class="border border-gray-300 rounded-md w-full p-2 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="photo" type="file" accept="image/*" />
                        </div>

                        <!-- Full Name -->
                        <div class="mb-4">
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="name" type="text" placeholder="Full Name" required />
                        </div>

                        <!-- Gender -->
                        <div class="mb-4">
                            <label for="gender" class="block text-sm font-medium text-gray-700 mb-1">Gender</label>
                            <select name="gender" class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" required>
                                <option value="" hidden>Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Date of Birth -->
                        <div class="mb-4">
                            <label for="dob" class="block text-sm font-medium text-gray-700 mb-1">Date of Birth</label>
                            <div class="datepicker-container position-relative">
                                <input class="datepicker border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="dob" type="text" placeholder="mm/dd/yyyy" required />
                                <div class="position-absolute top-0 end-0 h-full px-3 d-flex align-items-center">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Email (Optional) -->
                        <div class="mb-4">
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email (Optional)</label>
                            <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="email" type="email" placeholder="Email Address" />
                        </div>

                        <!-- Phone Number -->
                        <div class="mb-4">
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="phone" type="tel" placeholder="Phone Number" required />
                        </div>
                    </div>
                </div>

                <!-- Employment Details Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Employment Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Role -->
                        <div class="mb-4">
                            <label for="role" class="block text-sm font-medium text-gray-700 mb-1">Role</label>
                            <select name="role" id="role" class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" required>
                                <option value="" hidden>Select Role</option>
                                <option value="manager">Manager</option>
                                <option value="cashier">Cashier</option>
                                <option value="coach">Coach</option>
                                <option value="cleaner">Cleaner</option>
                                <option value="security">Security Guard</option>
                            </select>
                        </div>

                        <!-- Join Date -->
                        <div class="mb-4">
                            <label for="join_date" class="block text-sm font-medium text-gray-700 mb-1">Join Date</label>
                            <div class="datepicker-container position-relative">
                                <input class="datepicker border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="join_date" type="text" placeholder="mm/dd/yyyy" required />
                                <div class="position-absolute top-0 end-0 h-full px-3 d-flex align-items-center">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Salary -->
                        <div class="mb-4">
                            <label for="salary" class="block text-sm font-medium text-gray-700 mb-1">Salary (KHR)</label>
                            <input class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="salary" type="number" placeholder="Salary in KHR" required />
                        </div>

                        <!-- Work Schedule -->
                        <div class="mb-4 md:col-span-3">
                            <label for="schedule" class="block text-sm font-medium text-gray-700 mb-1">Work Schedule</label>
                            <textarea class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="schedule" placeholder="Work Schedule Details" rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Address Section -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b border-gray-200">Address Information</h3>
                    <div class="mb-4">
                        <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                        <textarea class="border border-gray-300 rounded-md w-full p-3 bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all" name="address" placeholder="Address" rows="3"></textarea>
                    </div>
                </div>

                <!-- Submit button -->
                <div class="mt-8">
                    <button type="submit" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 px-4 w-full rounded flex items-center justify-center transition-colors text-lg"><i class="fa-solid fa-user-plus mr-2"></i> Add New Employee</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock body %}

{% block js %}
<script>
    $(document).ready(function() {
        // Initialize datepicker for Date of Birth field
        $('.datepicker').datepicker({
            format: 'mm/dd/yyyy',
            autoclose: true,
            todayHighlight: true,
            clearBtn: true,
            orientation: "bottom auto"
        });

        // Add custom styling to the datepicker
        $('.datepicker-container .fa-calendar-alt').on('click', function() {
            $(this).closest('.datepicker-container').find('.datepicker').datepicker('show');
        });
    });
</script>
{% endblock js %}
