{% extends "../base.html" %}
{% load custom_filters %}


{% load user_filters %}

{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Edit Admin User</h3>
            <div class="flex space-x-2">
                <a href="{% url 'user:admin_users' %}" class="bg-purple-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Admin Users
                </a>
            </div>
        </div>

        <!-- Form starts  -->
        <div class="formSection bg-white px-10 py-8 rounded shadow-md mb-4">
            <!-- Password change note -->
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fa-solid fa-circle-info text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            To change a user's password, please use the dedicated "Change Password" functionality available in the header dropdown menu.
                        </p>
                    </div>
                </div>
            </div>
            <form class="grid grid-cols-2 gap-4" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="grid gap-2">
                    <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300" name="username" type="text" placeholder="Username" value={{employee.username}} disabled />
                    <label for="name" class="block text-sm font-medium text-gray-700">Name</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Name" value="{{employee.name}}" required />
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="email" type="email" placeholder="Email" value="{{employee.email}}" />
                </div>
                <div class="grid gap-2">
                    <label for="role" class="block text-sm font-medium text-gray-700">Role</label>
                    <select name="role" id="role" class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300" disabled>
                        <option value="admin" selected>Admin</option>
                    </select>
                    <input type="hidden" name="role" value="admin" />
                    <label for="create_day" class="block text-sm font-medium text-gray-700">Create Date</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300" name="create_day" type="date" value="{% if employee.create_day %}{{employee.create_day|date:'Y-m-d'}}{% endif %}" disabled />
                </div>
                <input type="hidden" name="salary" value="{{employee.salary|default:0}}" />
                <input type="hidden" name="phone" value="{{employee.phone|default:''}}" />
                <input type="hidden" name="address" value="{{employee.address|split_address_schedule:'address'|default:''}}" />
                <input type="hidden" name="schedule" value="{{employee.address|split_address_schedule:'schedule'|default:''}}" />
                <div class="col-span-2 grid grid-cols-3 gap-4 mt-4">
                    <div>
                        <button class="bg-purple-900 text-white font-bold py-2 px-4 w-full" type="submit">Update</button>
                    </div>
                    <div>
                        {% if employee.is_active %}
                        <a href="{% url "user:deactivate" employee.id %}" class="bg-yellow-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Deactivate Account</a>
                        {% else %}
                        <a href="{% url "user:active" employee.id %}" class="bg-green-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Activate Account</a>
                        {% endif %}
                    </div>
                    <div>
                        <a href="{% url "user:delete" employee.id %}" class="bg-red-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Delete Account</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock body %}