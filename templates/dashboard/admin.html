{% extends "../base.html" %}
{% load custom_filters %}
{% load i18n %}
{% load static %}
{% load permission_tags %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">

        <!-- Quick Actions Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">

            <!-- Quick Payment Section -->
            {% has_permission request.user 'paypervisit' 'view' as can_view_paypervisit %}
            {% has_permission request.user 'pos' 'view' as can_view_pos %}
            {% has_permission request.user 'payment' 'view' as can_view_payments %}
            {% if can_view_paypervisit or can_view_pos or can_view_payments %}
            <div class="componentWrapper">
                <div class="bg-gray-50 w-full p-4 shadow-xl rounded-md">
                    <h3 class="text-center text-2xl font-bold my-4">
                        <i class="fas fa-money-bill-wave mr-2 text-blue-600"></i>
                        {% trans "Quick Payment" %}
                    </h3>

                <div class="quick-actions-container">

                    <!-- Pay-Per-Visit Button -->
                    {% if can_view_paypervisit %}
                    <a href="{% url 'paypervisit:index' %}" class="quick-action-btn paypervisit">
                        <div class="quick-action-icon paypervisit">
                            <i class="fas fa-walking"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4 class="quick-action-title">{% trans "Pay-Per-Visit" %}</h4>
                            <p class="quick-action-subtitle">{% trans "Process visitor payments" %}</p>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    {% endif %}

                    <!-- POS Button -->
                    {% if can_view_pos %}
                    <a href="{% url 'product:pos' %}" class="quick-action-btn pos">
                        <div class="quick-action-icon pos">
                            <i class="fas fa-cash-register"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4 class="quick-action-title">{% trans "POS" %}</h4>
                            <p class="quick-action-subtitle">{% trans "Point of Sale system" %}</p>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    {% endif %}

                    <!-- Payment Member Button -->
                    {% if can_view_payments %}
                    <a href="{% url 'payment:create_payment' %}" class="quick-action-btn payment">
                        <div class="quick-action-icon payment">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4 class="quick-action-title">{% trans "Payment Member" %}</h4>
                            <p class="quick-action-subtitle">{% trans "Process membership payments" %}</p>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>
                    {% endif %}

                </div>
                </div>
            </div>
            {% endif %}

            <!-- Quick Finance Section -->
            {% has_permission request.user 'finance' 'view' as can_view_finance %}
            {% if can_view_finance %}
            <div class="componentWrapper">
                <div class="bg-gray-50 w-full p-4 shadow-xl rounded-md">
                    <h3 class="text-center text-2xl font-bold my-4">
                        <i class="fas fa-chart-line mr-2 text-green-600"></i>
                        {% trans "Quick Finance" %}
                    </h3>

                <div class="quick-actions-container">

                    <!-- Deposit Button -->
                    <a href="{% url 'finance:deposit' %}" class="quick-action-btn deposit">
                        <div class="quick-action-icon deposit">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4 class="quick-action-title">{% trans "Deposit" %}</h4>
                            <p class="quick-action-subtitle">{% trans "Add income transaction" %}</p>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                    <!-- Withdraw Button -->
                    <a href="{% url 'finance:withdraw' %}" class="quick-action-btn withdraw">
                        <div class="quick-action-icon withdraw">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="quick-action-content">
                            <h4 class="quick-action-title">{% trans "Withdraw" %}</h4>
                            <p class="quick-action-subtitle">{% trans "Add expense transaction" %}</p>
                        </div>
                        <div class="quick-action-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </a>

                </div>
                </div>
            </div>
            {% endif %}

        </div>


    <!-- Quick Reports -->
    <div class="componentWrapper mt-8">
        <div class="bg-gray-50 w-full p-4 shadow-xl rounded-md">
            <h3 class="text-center text-2xl font-bold my-4">{% trans "Quick Reports" %}</h3>

            <!-- Report Cards Container -->
            <div class="report-cards-container">

                <!-- Pay-Per-Visit Report Card -->
                <div class="report-card paypervisit" data-report-type="paypervisit" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Pay-Per-Visit Report" %}</h4>
                        <div class="report-card-icon paypervisit">
                            <i class="fas fa-walking"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                    </div>
                </div>

                <!-- Product Sales Report Card -->
                <div class="report-card product" data-report-type="product" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Product Sales Report" %}</h4>
                        <div class="report-card-icon product">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                    </div>
                </div>

                <!-- Overview Report Card -->
                <div class="report-card overview" data-report-type="overview" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Member Report" %}</h4>
                        <div class="report-card-icon overview">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                    </div>
                </div>

                <!-- Income Report Card -->
                <div class="report-card income" data-report-type="income" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Income Report" %}</h4>
                        <div class="report-card-icon income">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                    </div>
                </div>

                <!-- Expense Report Card -->
                <div class="report-card expense" data-report-type="expense" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Expense Report" %}</h4>
                        <div class="report-card-icon expense">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                    </div>
                </div>

                <!-- Balance Report Card -->
                <div class="report-card balance" data-report-type="balance" tabindex="0">
                    <div class="report-card-header">
                        <h4 class="report-card-title">{% trans "Balance Report" %}</h4>
                        <div class="report-card-icon balance">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                    </div>
                    <div class="report-card-content">
                        <div class="loading-state">
                            <div class="loading-spinner"></div>
                            {% trans "Loading..." %}
                        </div>
                    </div>
                    <div class="report-card-footer">
                        <div class="period-selector">
                            <button class="period-btn active" data-period="day">{% trans "Day" %}</button>
                            <button class="period-btn" data-period="week">{% trans "Week" %}</button>
                            <button class="period-btn" data-period="month">{% trans "Month" %}</button>
                        </div>
                        <div class="trend-indicator neutral">
                            <i class="fas fa-minus"></i> {% trans "Neutral Trend" %}
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>

<!-- component section ends -->
{% endblock body %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/dashboard-cards.css' %}">
{% endblock extra_css %}

{% block js %}
<script src="{% static 'js/dashboard-reports.js' %}?v=20250523-final"></script>
{% endblock js %}
