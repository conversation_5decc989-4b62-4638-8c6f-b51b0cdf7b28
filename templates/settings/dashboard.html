{% extends 'base.html' %}
{% load static %}



{% block extra_css %}
<style>
    .settings-list {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .settings-item {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #e5e7eb;
        transition: all 0.2s ease;
    }

    .settings-item:hover {
        background-color: #f9fafb;
    }

    .settings-item:last-child {
        border-bottom: none;
    }

    .settings-link {
        display: flex;
        align-items: center;
        color: #1f2937;
        font-weight: 500;
    }

    .settings-link:hover {
        text-decoration: none;
        color: #1e40af;
    }

    .settings-icon {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.375rem;
        margin-right: 1rem;
        color: white;
    }

    .settings-description {
        color: #6b7280;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .clean-data-button {
        background-color: #ef4444;
        color: white;
        border: none;
        border-radius: 0.375rem;
        padding: 0.75rem 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .clean-data-button:hover {
        background-color: #dc2626;
    }

    .warning-text {
        color: #b91c1c;
        font-size: 0.875rem;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-2xl font-bold text-gray-800">Settings</h2>
            <p class="text-gray-600">Manage all system settings from one place</p>
        </div>

        <!-- Settings List -->
        <div class="settings-list mb-6">
            <!-- Settings Dashboard -->
            <div class="settings-item">
                <a href="{% url 'settings:dashboard' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-th-large"></i>
                    </div>
                    <div>
                        <div>Settings Dashboard</div>
                        <div class="settings-description">Overview of all settings</div>
                    </div>
                </a>
            </div>

            <!-- General Settings -->
            <div class="settings-item">
                <a href="{% url 'settings:general' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-building"></i>
                    </div>
                    <div>
                        <div>General Settings</div>
                        <div class="settings-description">Configure basic information about your gym</div>
                    </div>
                </a>
            </div>

            <!-- Product Settings -->
            <div class="settings-item">
                <a href="{% url 'settings:product' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-box"></i>
                    </div>
                    <div>
                        <div>Product Settings</div>
                        <div class="settings-description">Configure product behavior and display options</div>
                    </div>
                </a>
            </div>



            <!-- Currency Settings -->
            <div class="settings-item">
                <a href="{% url 'settings:currency' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div>
                        <div>Currency Settings</div>
                        <div class="settings-description">Configure currency exchange rates</div>
                    </div>
                </a>
            </div>

            <!-- UI Settings -->
            <div class="settings-item">
                <a href="{% url 'settings:ui' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div>
                        <div>UI Settings</div>
                        <div class="settings-description">Customize the user interface appearance</div>
                    </div>
                </a>
            </div>

            {% if request.user.role == 'admin' %}
            <!-- Permissions Management -->
            <div class="settings-item">
                <a href="{% url 'settings:permissions' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div>
                        <div>Permissions Management</div>
                        <div class="settings-description">Configure role-based access permissions</div>
                    </div>
                </a>
            </div>

            <!-- System Settings -->
            <div class="settings-item">
                <a href="{% url 'settings:system' %}" class="settings-link">
                    <div class="settings-icon bg-blue-900">
                        <i class="fas fa-server"></i>
                    </div>
                    <div>
                        <div>System Settings</div>
                        <div class="settings-description">Manage system-level settings and maintenance</div>
                    </div>
                </a>
            </div>
            {% endif %}

        </div>

        <!-- System Information -->
        <div class="bg-white rounded-lg shadow-md p-4">
            <div class="flex items-center mb-3">
                <div class="settings-icon bg-gray-100 text-blue-900">
                    <i class="fas fa-info-circle"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800">System Information</h3>
            </div>

            <ul class="text-sm text-gray-700 pl-12">
                <li class="flex items-center mb-2">
                    <i class="fas fa-clock text-blue-900 mr-2"></i>
                    <span class="font-medium">Last Updated:</span>
                    <span class="ml-2">{{ settings.last_updated|date:"F j, Y H:i" }}</span>
                </li>
                <li class="flex items-center mb-2">
                    <i class="fas fa-calendar-check text-blue-900 mr-2"></i>
                    <span class="font-medium">Last Data Cleanup:</span>
                    <span class="ml-2">
                        {% if settings.last_data_cleanup %}
                            {{ settings.last_data_cleanup|date:"F j, Y H:i" }}
                        {% else %}
                            Never
                        {% endif %}
                    </span>
                </li>
                <li class="flex items-center mb-2">
                    <i class="fas fa-database text-blue-900 mr-2"></i>
                    <span class="font-medium">Last Backup:</span>
                    <span class="ml-2">
                        {% if settings.last_backup_date %}
                            {{ settings.last_backup_date|date:"F j, Y H:i" }}
                        {% else %}
                            Never
                        {% endif %}
                    </span>
                </li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
