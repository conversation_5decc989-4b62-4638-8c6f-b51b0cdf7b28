{% extends 'base.html' %}
{% load static %}



{% block extra_css %}
<style>
    .color-preview {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 50%; /* Make it a perfect circle */
        display: inline-block;
        border: 1px solid #e5e7eb;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
    }

    .color-preview:hover {
        border-color: #3b82f6;
    }

    .notification-preview {
        padding: 0.75rem 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        position: relative;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .notification-preview-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .notification-preview-message {
        font-size: 0.875rem;
    }

    .notification-preview-icon {
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Back Button -->
        <div class="flex items-center mb-4">
            <a href="{% url 'settings:dashboard' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="text-2xl font-bold">UI Settings</h2>
        </div>

        <!-- Settings Card -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- Card Header -->
            <div class="bg-blue-900 text-white p-4">
                <div class="flex items-center">
                    <i class="fas fa-palette text-2xl mr-3"></i>
                    <h3 class="text-xl font-bold">Notification Colors</h3>
                </div>
            </div>

            <!-- Card Body -->
            <div class="p-6">
                <form method="post">
                    {% csrf_token %}

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Notification Preview</h4>

                        <div id="success-preview" class="notification-preview" style="background-color: {{ settings.notification_success_color }}; color: {{ settings.notification_text_color }};">
                            <div class="notification-preview-title">
                                <i class="fas fa-check-circle notification-preview-icon"></i>
                                Success
                            </div>
                            <div class="notification-preview-message">Operation completed successfully</div>
                        </div>

                        <div id="error-preview" class="notification-preview" style="background-color: {{ settings.notification_error_color }}; color: {{ settings.notification_text_color }};">
                            <div class="notification-preview-title">
                                <i class="fas fa-exclamation-circle notification-preview-icon"></i>
                                Error
                            </div>
                            <div class="notification-preview-message">An error occurred while processing your request</div>
                        </div>

                        <div id="warning-preview" class="notification-preview" style="background-color: {{ settings.notification_warning_color }}; color: {{ settings.notification_text_color }};">
                            <div class="notification-preview-title">
                                <i class="fas fa-exclamation-triangle notification-preview-icon"></i>
                                Warning
                            </div>
                            <div class="notification-preview-message">Please review the information before proceeding</div>
                        </div>

                        <div id="info-preview" class="notification-preview" style="background-color: {{ settings.notification_info_color }}; color: {{ settings.notification_text_color }};">
                            <div class="notification-preview-title">
                                <i class="fas fa-info-circle notification-preview-icon"></i>
                                Information
                            </div>
                            <div class="notification-preview-message">Here's some information you might find useful</div>
                        </div>
                    </div>

                    <div class="mb-6">
                        <h4 class="text-lg font-semibold mb-3">Color Settings</h4>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Success Notification Color</label>
                                <div class="flex items-center">
                                    <div class="relative">
                                        <span class="color-preview mr-2" id="success-color-preview" style="background-color: {{ settings.notification_success_color }};"></span>
                                        <input type="color"
                                               id="notification_success_color_picker"
                                               class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
                                               value="{{ settings.notification_success_color }}"
                                               oninput="updateColorFromPicker('notification_success_color')"
                                               onchange="updateColorFromPicker('notification_success_color')" />
                                    </div>
                                    <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                           id="notification_success_color"
                                           name="notification_success_color"
                                           type="text"
                                           placeholder="Enter color hex code"
                                           value="{{ settings.notification_success_color }}"
                                           required />
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Error Notification Color</label>
                                <div class="flex items-center">
                                    <div class="relative">
                                        <span class="color-preview mr-2" id="error-color-preview" style="background-color: {{ settings.notification_error_color }};"></span>
                                        <input type="color"
                                               id="notification_error_color_picker"
                                               class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
                                               value="{{ settings.notification_error_color }}"
                                               oninput="updateColorFromPicker('notification_error_color')"
                                               onchange="updateColorFromPicker('notification_error_color')" />
                                    </div>
                                    <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                           id="notification_error_color"
                                           name="notification_error_color"
                                           type="text"
                                           placeholder="Enter color hex code"
                                           value="{{ settings.notification_error_color }}"
                                           required />
                                </div>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">Warning Notification Color</label>
                                <div class="flex items-center">
                                    <div class="relative">
                                        <span class="color-preview mr-2" id="warning-color-preview" style="background-color: {{ settings.notification_warning_color }};"></span>
                                        <input type="color"
                                               id="notification_warning_color_picker"
                                               class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
                                               value="{{ settings.notification_warning_color }}"
                                               oninput="updateColorFromPicker('notification_warning_color')"
                                               onchange="updateColorFromPicker('notification_warning_color')" />
                                    </div>
                                    <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                           id="notification_warning_color"
                                           name="notification_warning_color"
                                           type="text"
                                           placeholder="Enter color hex code"
                                           value="{{ settings.notification_warning_color }}"
                                           required />
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Info Notification Color</label>
                                <div class="flex items-center">
                                    <div class="relative">
                                        <span class="color-preview mr-2" id="info-color-preview" style="background-color: {{ settings.notification_info_color }};"></span>
                                        <input type="color"
                                               id="notification_info_color_picker"
                                               class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
                                               value="{{ settings.notification_info_color }}"
                                               oninput="updateColorFromPicker('notification_info_color')"
                                               onchange="updateColorFromPicker('notification_info_color')" />
                                    </div>
                                    <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                           id="notification_info_color"
                                           name="notification_info_color"
                                           type="text"
                                           placeholder="Enter color hex code"
                                           value="{{ settings.notification_info_color }}"
                                           required />
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-sm font-medium mb-2">Notification Text Color</label>
                            <div class="flex items-center">
                                <div class="relative">
                                    <span class="color-preview mr-2" id="text-color-preview" style="background-color: {{ settings.notification_text_color }};"></span>
                                    <input type="color"
                                           id="notification_text_color_picker"
                                           class="opacity-0 absolute top-0 left-0 w-full h-full cursor-pointer"
                                           value="{{ settings.notification_text_color }}"
                                           oninput="updateColorFromPicker('notification_text_color')"
                                           onchange="updateColorFromPicker('notification_text_color')" />
                                </div>
                                <input class="border w-full p-4 leading-tight bg-slate-100 rounded"
                                       id="notification_text_color"
                                       name="notification_text_color"
                                       type="text"
                                       placeholder="Enter color hex code"
                                       value="{{ settings.notification_text_color }}"
                                       required />
                            </div>
                        </div>

                        <!-- Info Box -->
                        <div class="mt-3 p-3 bg-white rounded border border-blue-100">
                            <p class="text-sm text-gray-700"><i class="fas fa-info-circle text-blue-600 mr-1"></i> These settings control the appearance of notifications throughout the system. Use hex color codes (e.g., #4CAF50) for best results.</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <a href="{% url 'settings:dashboard' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-3 px-6 rounded mr-2">Cancel</a>
                            <button type="button" id="reset-defaults" class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded"><i class="fas fa-undo mr-2"></i> Reset to Defaults</button>
                        </div>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded"
                                type="submit"><i class="fas fa-save mr-2"></i> Save Changes</button>
                    </div>
                </form>
            </div>

            <!-- Last Updated Info -->
            <div class="bg-gray-100 p-4 border-t">
                <p class="text-sm text-gray-600">
                    <i class="fas fa-clock mr-1"></i> Last updated: {{ settings.last_updated|date:"F j, Y H:i" }}
                </p>
            </div>
        </div>
    </div>
</div>

<script>
    // Function to open the color picker when clicking on a color preview
    function openColorPicker(inputId) {
        const colorPicker = document.getElementById(inputId + '_picker');
        colorPicker.click();
    }

    // Function to update the text input and preview when a color is selected from the picker
    function updateColorFromPicker(inputId) {
        const colorPicker = document.getElementById(inputId + '_picker');
        const textInput = document.getElementById(inputId);
        const colorPreview = document.getElementById(inputId.replace('notification_', '') + '-color-preview');

        // Update the text input with the selected color
        textInput.value = colorPicker.value;

        // Update the color preview
        colorPreview.style.backgroundColor = colorPicker.value;
        // For white colors, add a light gray border to make it visible
        if (colorPicker.value.toLowerCase() === '#ffffff' || colorPicker.value.toLowerCase() === '#fff') {
            colorPreview.style.borderColor = '#999';
        } else {
            colorPreview.style.borderColor = '#e5e7eb';
        }

        // Update the notification preview if applicable
        const previewId = inputId.replace('notification_', '').replace('_color', '') + '-preview';
        const previewElement = document.getElementById(previewId);

        if (previewElement) {
            if (inputId.includes('text_color')) {
                // If this is the text color, update all notification previews
                document.getElementById('success-preview').style.color = colorPicker.value;
                document.getElementById('error-preview').style.color = colorPicker.value;
                document.getElementById('warning-preview').style.color = colorPicker.value;
                document.getElementById('info-preview').style.color = colorPicker.value;
            } else {
                // Otherwise, update the background color of the specific preview
                previewElement.style.backgroundColor = colorPicker.value;
            }
        }

        // Trigger an input event on the text input to ensure all listeners are notified
        // This ensures consistency between typing in the text field and using the color picker
        const event = new Event('input', { bubbles: true });
        textInput.dispatchEvent(event);
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Default color values
        const defaultColors = {
            'notification_success_color': '#4CAF50',
            'notification_error_color': '#F44336',
            'notification_warning_color': '#FF9800',
            'notification_info_color': '#2196F3',
            'notification_text_color': '#FFFFFF'
        };

        // Live preview for color changes when typing in the text input
        const colorInputs = {
            'notification_success_color': ['success-color-preview', 'success-preview'],
            'notification_error_color': ['error-color-preview', 'error-preview'],
            'notification_warning_color': ['warning-color-preview', 'warning-preview'],
            'notification_info_color': ['info-color-preview', 'info-preview'],
            'notification_text_color': ['text-color-preview', null]
        };

        // Initialize color previews to handle white colors correctly
        for (const [inputId, previewIds] of Object.entries(colorInputs)) {
            const input = document.getElementById(inputId);
            const colorPreview = document.getElementById(previewIds[0]);

            // For white colors, add a light gray border to make it visible
            if (input.value.toLowerCase() === '#ffffff' || input.value.toLowerCase() === '#fff') {
                colorPreview.style.borderColor = '#999';
            } else {
                colorPreview.style.borderColor = '#e5e7eb';
            }
        }

        for (const [inputId, previewIds] of Object.entries(colorInputs)) {
            const input = document.getElementById(inputId);
            const colorPreview = document.getElementById(previewIds[0]);
            const colorPicker = document.getElementById(inputId + '_picker');

            input.addEventListener('input', function() {
                // Update color preview
                colorPreview.style.backgroundColor = this.value;

                // For white colors, add a light gray border to make it visible
                if (this.value.toLowerCase() === '#ffffff' || this.value.toLowerCase() === '#fff') {
                    colorPreview.style.borderColor = '#999';
                } else {
                    colorPreview.style.borderColor = '#e5e7eb';
                }

                // Update color picker
                colorPicker.value = this.value;

                // Update notification preview if applicable
                if (previewIds[1]) {
                    document.getElementById(previewIds[1]).style.backgroundColor = this.value;
                }

                // If this is the text color, update all notification previews
                if (inputId === 'notification_text_color') {
                    document.getElementById('success-preview').style.color = this.value;
                    document.getElementById('error-preview').style.color = this.value;
                    document.getElementById('warning-preview').style.color = this.value;
                    document.getElementById('info-preview').style.color = this.value;
                }
            });
        }

        // Add event listener for Reset to Default button
        document.getElementById('reset-defaults').addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all colors to their default values?')) {
                // Reset each color input to its default value
                for (const [inputId, defaultValue] of Object.entries(defaultColors)) {
                    const input = document.getElementById(inputId);
                    const colorPicker = document.getElementById(inputId + '_picker');

                    // Set the input value
                    input.value = defaultValue;

                    // Set the color picker value
                    colorPicker.value = defaultValue;

                    // Trigger the input event to update previews
                    const event = new Event('input', { bubbles: true });
                    input.dispatchEvent(event);
                }

                // Show a success message
                alert('Colors have been reset to default values. Click Save Changes to apply.');
            }
        });
    });
</script>
{% endblock %}
