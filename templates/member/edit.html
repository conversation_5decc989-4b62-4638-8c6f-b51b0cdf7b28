{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
   <!-- component sections starts  -->
   <div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Entry Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <div class="flex justify-between">
                <h3 class="text-2xl font-bold mb-4">Member Details</h3>
            </div>
            <form class="grid grid-cols-4 gap-4" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <!-- Col 1 -->
                <div class="grid gap-2">
                    <!-- Member ID -->
                    <label class="block text-sm font-medium mb-1">Member ID</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:cursor-not-allowed disabled:bg-slate-300"
                           name="member_id" type="text" placeholder="Member ID" disabled value="{{member.member_id}}" />

                    <!-- Full Name -->
                    <label class="block text-sm font-medium mb-1">Full Name*</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100"
                           name="name" type="text" placeholder="Full Name" required value="{{member.name}}" />

                    <!-- Gender -->
                    <label class="block text-sm font-medium mb-1">Gender*</label>
                    <select name="gender" class="border w-full p-4 leading-tight bg-slate-100" required>
                        <option value="" hidden>Select Gender</option>
                        <option value="male" {% if member.gender == 'male' %}selected{% endif %}>Male</option>
                        <option value="female" {% if member.gender == 'female' %}selected{% endif %}>Female</option>
                        <option value="other" {% if member.gender == 'other' %}selected{% endif %}>Other</option>
                    </select>
                </div>

                <!-- Col 2 -->
                <div class="grid gap-2">
                    <!-- Photo Upload -->
                    <label class="block text-sm font-medium mb-1">Photo</label>
                    <input class="border w-full p-3 leading-tight bg-slate-100"
                           name="photo" type="file" accept="image/*" />
                    {% if member.photo %}
                    <div class="mt-1">
                        <img src="{{ member.photo.url }}" alt="{{ member.name }}" class="h-20 w-auto object-cover rounded">
                    </div>
                    {% endif %}

                    <!-- Date of Birth -->
                    <label class="block text-sm font-medium mb-1">Date of Birth</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100"
                           name="dob" type="date" value="{% if member.dob %}{{member.dob|date:'Y-m-d'}}{% endif %}" />

                    <!-- Phone Number -->
                    <label class="block text-sm font-medium mb-1">Phone Number*</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100"
                           name="contact" type="tel" placeholder="Phone Number" required value="{{member.contact}}" />
                </div>

                <!-- Col 3 -->
                <div class="grid gap-2">
                    <!-- Telegram/Messenger -->
                    <label class="block text-sm font-medium mb-1">Telegram/Messenger</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100"
                           name="telegram" type="text" placeholder="Telegram/Messenger" value="{{member.telegram}}" />

                    <!-- Address -->
                    <label class="block text-sm font-medium mb-1">Address</label>
                    <textarea class="border w-full p-4 leading-tight bg-slate-100"
                              name="address" placeholder="Address" rows="3">{{member.address}}</textarea>

                    <!-- Package -->
                    <label class="block text-sm font-medium mb-1">Package*</label>
                    <select name="package" id="package" class="border w-full p-4 leading-tight bg-slate-100" required>
                        <option value="" hidden>Select a package</option>
                        {% for package in packages %}
                        <option {% if package.id == member.package.id %}selected{% endif %}
                                value="{{package.id}}"
                                data-price="{{package.price_khr}}"
                                data-duration="{{package.duration}}">{{package.name}} ({{package.duration}} months) - {{package.price_khr|format_khr}}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Col 4 -->
                <div class="grid gap-2">
                    <!-- Total Amount -->
                    <label class="block text-sm font-medium mb-1">Total Amount</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:bg-slate-400 disabled:cursor-not-allowed"
                           name="total_amount" id="total_amount" type="number"
                           value="{% if member.package %}{{member.package.price_khr}}{% else %}0{% endif %}" disabled />

                    <!-- Discount Amount -->
                    <label class="block text-sm font-medium mb-1">Discount Amount</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100"
                           name="discount" id="discount" type="number" value="{{member.discount}}" min="0" />

                    <!-- Due Amount -->
                    <label class="block text-sm font-medium mb-1">Due Amount</label>
                    <input class="border w-full p-4 leading-tight bg-slate-100 disabled:bg-slate-400 disabled:cursor-not-allowed due_amount"
                           type="number" value="{{member.due_payment}}" disabled />
                    <input class="due_amount" name="due_payment" value="{{member.due_payment}}" hidden />
                </div>
                <div class="col-span-2">
                    <button
                      class="bg-blue-900 text-white font-bold py-2 px-4 w-full"
                      type="submit">Update</button>
                </div>
                <div>
                    {% if member.status %}
                        <a href="{% url "member:deactivate"  member.id %}" class="bg-yellow-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Deactivate Member</a>
                        {% else %}
                        <a href="{% url "member:activate"  member.id %}" class="bg-green-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer">Activate Member</a>
                    {% endif %}
                </div>
                <div>
                    <a href="{% url "member:delete" member.id %}" class="bg-red-900 text-white font-bold py-2 px-4 w-full inline-block text-center cursor-pointer" onclick="return confirm('Are you sure you want to delete this member? This action cannot be undone.')">Delete Member</a>
                </div>
            </form>
        </div>

          <!-- Membership Details -->
          <section class="bg-gray-50 w-full p-4 shadow-xl rounded-md mt-4">
            <h3 class="text-2xl font-bold mb-4">Membership Details</h3>
            <div class="grid grid-cols-3 gap-4">
              <div class="bg-white p-4 rounded shadow">
                <h4 class="font-semibold text-lg mb-2">Package Information</h4>
                <p><span class="font-medium">Package:</span> {{member.package.name}}</p>
                <p><span class="font-medium">Duration:</span> {{member.package.duration}} months</p>
                <p><span class="font-medium">Price:</span> {{member.package.price_khr|format_khr}}</p>
                <p><span class="font-medium">Access Type:</span>
                  {% if member.package.access_type == 'all_hours' %}
                    All Hours
                  {% else %}
                    Peak Hours Only
                  {% endif %}
                </p>
              </div>

              <div class="bg-white p-4 rounded shadow">
                <h4 class="font-semibold text-lg mb-2">Membership Period</h4>
                <p><span class="font-medium">Start Date:</span> {{member.start_date}}</p>
                <p><span class="font-medium">End Date:</span> {{member.end_date}}</p>

                <!-- Expiration Status -->
                <p class="mt-2">
                  <span class="font-medium">Expiration:</span>
                  {% if member.expiration_status == "expired" %}
                    <span class="text-red-600 font-medium">Expired</span>
                  {% elif member.expiration_status == "critical" %}
                    <span class="text-red-600 font-medium">{{member.days_remaining}} days left</span>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                      <div class="bg-red-600 h-2.5 rounded-full" style="width: {{member.days_remaining|progress_width}}%"></div>
                    </div>
                  {% elif member.expiration_status == "warning" %}
                    <span class="text-yellow-600 font-medium">{{member.days_remaining}} days left</span>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                      <div class="bg-yellow-400 h-2.5 rounded-full" style="width: {{member.days_remaining|progress_width}}%"></div>
                    </div>
                  {% else %}
                    <span class="text-green-600 font-medium">{{member.days_remaining}} days left</span>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                      <div class="bg-green-600 h-2.5 rounded-full" style="width: 100%"></div>
                    </div>
                  {% endif %}
                </p>

                <p class="mt-2"><span class="font-medium">Status:</span>
                  {% if member.status %}
                    <span class="text-green-600 font-medium">Active</span>
                  {% else %}
                    <span class="text-red-600 font-medium">Inactive</span>
                  {% endif %}
                </p>
                <p><span class="font-medium">Payment Status:</span> {{member.payment_status|title}}</p>
              </div>

              <div class="bg-white p-4 rounded shadow">
                <h4 class="font-semibold text-lg mb-2">Financial Summary</h4>
                <p>
                  <span class="font-medium">
                    {% if member.due_payment < 0 %}
                      Advanced Payment:
                    {% else %}
                      Due Payment:
                    {% endif %}
                  </span>
                  {% if member.due_payment < 0 %}
                    <span class="text-green-600 font-medium">{{ member.due_payment|abs_val|format_khr }}</span>
                  {% else %}
                    <span class="text-red-600 font-medium">{{ member.due_payment|format_khr }}</span>
                  {% endif %}
                </p>
              </div>
            </div>
          </section>



          <!-- Transaction History -->
          <section class="bg-gray-50 w-full p-4 shadow-xl rounded-md mt-4">
            <div class="flex justify-between">
                <h3 class="text-2xl font-bold">Payment & Membership History</h3>
                <span>
                    {% if member.end_date >= now.date %}
                        <!-- Member has an active package -->
                        {% if member.due_payment > 0 %}
                            <!-- Member has a partial payment -->
                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded">
                                Package Active Until: {{ member.end_date }} | Due: {{ member.due_payment|format_khr }}
                            </span>
                        {% else %}
                            <!-- Member has paid in full -->
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded">
                                Package Active Until: {{ member.end_date }}
                            </span>
                        {% endif %}
                    {% elif member.due_payment < 0 %}
                        Total Advanced:
                        {{ member.due_payment|abs_val|format_khr }}
                    {% else %}
                        Total Due:
                        {{member.due_payment|format_khr }}
                    {% endif %}
                </span>
            </div>
            <div class="relative overflow-x-auto mt-4">
                <table class="w-full">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50 text-center"><tr>
                            <th scope="col" class="px-4 py-3">Period</th>
                            <th scope="col" class="px-4 py-3">Transaction Type</th>
                            <th scope="col" class="px-4 py-3">Invoice No.</th>
                            <th scope="col" class="px-4 py-3">Amount</th>
                            <th scope="col" class="px-4 py-3">Due Amount</th>
                            <th scope="col" class="px-4 py-3">Date</th>
                            <th scope="col" class="px-4 py-3">Method</th>
                            <th scope="col" class="px-4 py-3">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bill in bills %}
                        <tr class="{% if bill.is_summary %}bg-gray-100 font-medium{% else %}bg-white{% endif %} border text-sm text-center">
                            <td class="px-4 py-3">{{bill.month}} {{bill.year}}</td>
                            <td class="px-4 py-3">
                                {% if bill.is_summary %}
                                    {% if bill.status == 'Covered by Package' %}
                                        <span class="font-semibold">Package Coverage</span>
                                    {% else %}
                                        <span class="font-semibold">Monthly Summary</span>
                                    {% endif %}
                                {% else %}
                                    {{bill.reason}}
                                {% endif %}
                            </td>
                            <td class="px-4 py-3">
                                {% if bill.invoice_no %}
                                    {{bill.invoice_no}}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="px-4 py-3">
                                {% if bill.is_summary %}
                                    <span class="font-semibold">{{bill.payed_amount|format_khr}} / {{bill.total_amount|format_khr}}</span>
                                {% else %}
                                    {{bill.payed_amount|format_khr}}
                                {% endif %}
                            </td>
                            <td class="px-4 py-3">{{bill.due_amount|format_khr}}</td>
                            <td class="px-4 py-3">{{bill.payment_date}}</td>
                            <td class="px-4 py-3">{{bill.payment_method}}</td>
                            <td class="px-4 py-3">
                                {% if bill.status == 'Completed' %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                    {{bill.status}}
                                </span>
                                {% elif bill.status == 'Partial' %}
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                    {{bill.status}}
                                </span>
                                {% elif bill.status == 'Extension' %}
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">Membership Extended</span>
                                {% elif bill.status == 'Covered by Package' %}
                                <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded">Covered by Package</span>
                                {% elif bill.status == 'Payment Required' %}
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">Payment Required</span>
                                {% else %}
                                <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                    {{bill.status}}
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Legend for payment types -->
            <div class="mt-4 bg-white p-3 rounded shadow">
                <h4 class="font-semibold mb-2">Payment Types Explained:</h4>
                <ul class="text-sm space-y-2">
                    <li class="flex items-center">
                        <span class="bg-gray-100 font-medium px-2 py-1 rounded mr-2">Monthly Summary</span>
                        <span>Shows the overall payment status for each month</span>
                    </li>
                    <li class="flex items-center">
                        <span class="bg-gray-100 font-medium px-2 py-1 rounded mr-2">Package Coverage</span>
                        <span>Shows months that are covered by your multi-month package</span>
                    </li>
                    <li class="flex items-center">
                        <span class="px-2 py-1 rounded mr-2">Membership Fee</span>
                        <span>Regular payment toward monthly membership</span>
                    </li>
                    <li class="flex items-center">
                        <span class="px-2 py-1 rounded mr-2">Membership Extension</span>
                        <span>Additional payment that extends membership duration</span>
                    </li>
                    <li class="flex items-center">
                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded mr-2">Membership Extended</span>
                        <span>Payment has extended the membership end date</span>
                    </li>
                    <li class="flex items-center">
                        <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded mr-2">Covered by Package</span>
                        <span>Month is already paid for by the member's package</span>
                    </li>
                    <li class="flex items-center">
                        <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded mr-2">Payment Required</span>
                        <span>Payment is required for the package</span>
                    </li>
                </ul>
            </div>
        </section>
    </div>
  </div>
{% endblock body %}

{% block js %}
<script>
  $("document").ready(() => {
    // Package Select
    $("#package").change(e => {
      const selectedOption = e.target.options[e.target.selectedIndex];
      const price = selectedOption.getAttribute('data-price');

      // Set total amount
      $("#total_amount").val(price);

      // Recalculate in case there's a discount
      calculateAmounts();
    });

    // Discount Change
    $("#discount").on('input', () => {
      calculateAmounts();
    });

    // Function to calculate all amounts
    function calculateAmounts() {
      const totalAmount = Number($("#total_amount").val());
      const discount = Number($("#discount").val());

      // Calculate due amount (total - discount)
      const dueAmount = Math.max(0, totalAmount - discount);

      // Update due amount fields
      document.querySelectorAll(".due_amount").forEach(item => {
        item.value = dueAmount;
      });
    }

    // Initialize calculations on page load
    calculateAmounts();
  });
</script>
{% endblock js %}