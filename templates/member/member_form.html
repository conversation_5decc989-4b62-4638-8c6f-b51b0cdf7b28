{% extends 'base.html' %}
{% load custom_filters %}


{% load humanize %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">
                {% if member %}
                    Edit Member: {{ member.name }}
                {% else %}
                    Create New Member
                {% endif %}
            </h3>
        </div>

        <!-- Member Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Basic Information</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <!-- Member ID (auto-generated, hidden) -->
                        <input type="hidden" name="member_id" value="auto" />

                        <!-- Full Name -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Full Name <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="name"
                                   type="text"
                                   placeholder="Full Name"
                                   value="{{ member.name|default:'' }}"
                                   required />
                        </div>

                        <!-- Gender -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Gender <span class="text-red-500">*</span></label>
                            <select class="border w-full p-4 leading-tight bg-slate-100" name="gender" required>
                                <option value="" disabled {% if not member %}selected{% endif %}>Select Gender</option>
                                <option value="male" {% if member.gender == 'male' %}selected{% endif %}>Male</option>
                                <option value="female" {% if member.gender == 'female' %}selected{% endif %}>Female</option>
                                <option value="other" {% if member.gender == 'other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>

                        <!-- Date of Birth -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Date of Birth</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="dob"
                                   type="date"
                                   value="{{ member.dob|date:'Y-m-d'|default:'' }}" />
                        </div>

                        <!-- Photo Upload -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Photo</label>
                            <input class="border w-full p-3 leading-tight bg-slate-100"
                                   name="photo"
                                   type="file"
                                   accept="image/*" />
                        </div>

                        <!-- Phone Number -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Phone Number <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="contact"
                                   type="tel"
                                   placeholder="Phone Number"
                                   value="{{ member.contact|default:'' }}"
                                   required />
                        </div>

                        <!-- Telegram/Messenger -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Telegram/Messenger</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="telegram"
                                   type="text"
                                   placeholder="Telegram/Messenger"
                                   value="{{ member.telegram|default:'' }}" />
                        </div>
                    </div>
                </div>

                <!-- Address Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Address</h4>
                    <div class="grid grid-cols-1 gap-4">
                        <!-- Address -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Address</label>
                            <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                      name="address"
                                      rows="2"
                                      placeholder="Address">{{ member.address|default:'' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Membership Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Membership Details</h4>
                    <div class="grid grid-cols-3 gap-4">
                        <!-- Package Selection -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Package <span class="text-red-500">*</span></label>
                            <select class="border w-full p-4 leading-tight bg-slate-100" name="package" required>
                                <option value="" disabled {% if not member %}selected{% endif %}>Select Package</option>
                                {% for package in packages %}
                                <option value="{{ package.id }}"
                                        data-price="{{ package.price_khr }}"
                                        data-duration="{{ package.duration }}"
                                        {% if member.package.id == package.id %}selected{% endif %}>
                                    {{ package.name }} ({{ package.duration }} months) - {{ package.price_khr|intcomma }} ៛
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Start Date -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Start Date <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="start_date"
                                   type="date"
                                   value="{{ member.start_date|date:'Y-m-d'|default:'' }}"
                                   required />
                        </div>

                        <!-- Total Amount -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Total Amount (៛)</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100 disabled:bg-slate-400 disabled:cursor-not-allowed"
                                   id="total_amount"
                                   type="number"
                                   value="0"
                                   disabled />
                        </div>

                        <!-- Discount -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Discount (៛)</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="discount"
                                   type="number"
                                   min="0"
                                   placeholder="0"
                                   value="{{ member.discount|default:'0' }}" />
                        </div>

                        <!-- Due Amount -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Due Amount (៛)</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100 disabled:bg-slate-400 disabled:cursor-not-allowed due_amount"
                                   type="number"
                                   value="0"
                                   disabled />
                            <input class="due_amount" name="due_amount" value="0" hidden />
                        </div>
                    </div>
                </div>



                <!-- Submit Button -->
                <div class="flex justify-between mt-6">
                    <a href="{% url 'member:index' %}" class="bg-gray-500 text-white font-bold py-2 px-4 rounded">
                        Cancel
                    </a>
                    <button class="bg-blue-900 text-white font-bold py-2 px-4 rounded" type="submit">
                        {% if member %}Update{% else %}Create{% endif %} Member
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
  $("document").ready(() => {
    // Package Select
    $("select[name='package']").change(e => {
      const selectedOption = e.target.options[e.target.selectedIndex];
      const price = selectedOption.getAttribute('data-price');

      if (price) {
        // Set total amount
        $("#total_amount").val(price);

        // Reset discount
        $("input[name='discount']").val(0);

        // Set due amount to total amount initially
        document.querySelectorAll(".due_amount").forEach(item => {
          item.value = price;
        });

        // Recalculate in case there's a discount
        calculateAmounts();
      }
    });

    // Discount Change
    $("input[name='discount']").on('input', () => {
      calculateAmounts();
    });

    // Function to calculate all amounts
    function calculateAmounts() {
      const totalAmount = Number($("#total_amount").val());
      const discount = Number($("input[name='discount']").val());

      // Calculate due amount (total - discount)
      const dueAmount = Math.max(0, totalAmount - discount);

      // Update due amount fields
      document.querySelectorAll(".due_amount").forEach(item => {
        item.value = dueAmount;
      });
    }
  });
</script>
{% endblock js %}
