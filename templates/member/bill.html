{% extends "../base.html" %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Entry Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Search</h3>
            <form method="post">
                {% csrf_token %}
                <div class="grid gap-4">
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="search" type="text"
                        placeholder="Search For Member" />
                    <div>
                        <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Search</button>
                    </div>
                </div>
            </form>
        </div>
        <div class="bg-gray-50 p-4 shadow-xl rounded-md mt-4">
            <h3 class="text-2xl font-semibold mb-4">Payment Due Member List</h3>
            <div>
                <div class="relative overflow-x-auto">
                    <table class="w-full">
                        <thead class="text-sm uppercase bg-blue-900 text-gray-50 text-center"><tr>
                                <th scope="col" class="px-6 py-3">Member Id</th>
                                <th scope="col" class="px-6 py-3">Name</th>
                                <th scope="col" class="px-6 py-3">Phone</th>
                                <th scope="col" class="px-6 py-3">Package</th>
                                <th scope="col" class="px-6 py-3">Due</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for member in members %}
                            <tr class="bg-white border text-sm text-center">
                                <td class="px-6 py-4">{{member.member_id}}</td>
                                <td class="px-6 py-4">{{member.name}}</td>
                                <td class="px-6 py-4">{{member.contact}}</td>
                                <td class="px-6 py-4">{{member.package.name}}</td>
                                <td class="px-6 py-4">{{member.due_payment|format_khr}}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="bg-gray-50 p-4 shadow-xl rounded-md mt-4">

            <h3 class="text-2xl font-semibold mb-4">Pay Package Bill</h3>
            <form class="grid grid-cols-2 gap-4" method="post" action="/member/pay/" autocomplete="off">
                {% csrf_token %}
                <!-- Member Id -->
                <input class="border w-full p-4 leading-tight bg-slate-100" name="memberId" type="text"
                    placeholder="Member Id" required list="memberList" />
                <datalist id="memberList">
                    {% for member in members %}
                        <option value="{{member.member_id}}">{{member.name}} | {{member.package.name}} | {{member.status}}</option>
                    {% endfor %}
                </datalist>
                <!-- Amount -->
                <input class="border w-full p-4 leading-tight bg-slate-100" name="amount" type="number"
                    placeholder="Amount" required />
                <div class="col-span-2">
                    <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Pay</button>
                </div>
        </div>
    </div>
</div>
</div>

{% endblock body %}