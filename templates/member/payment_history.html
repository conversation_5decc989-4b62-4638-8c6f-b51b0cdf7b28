{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
   <!-- component sections starts  -->
   <div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
          <!-- Transaction History -->
          <section class="bg-gray-50 w-full p-4 shadow-xl rounded-md mt-4">
            <div class="flex justify-between">
                <h3 class="text-2xl font-bold">{{month}} Transaction History for {{member.name}}</h3>
            </div>
            <div class="relative overflow-x-auto mt-4">
                <table class="w-full">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50 text-center"><tr>
                            <th scope="col" class="px-6 py-3">Date</th>
                            <th scope="col" class="px-6 py-3">Reason</th>
                            <th scope="col" class="px-6 py-3">Total Amount</th>
                            <th scope="col" class="px-6 py-3">Received Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for pay in payments %}
                        <tr class="bg-white border text-sm text-center">
                            <td class="px-6 py-4"><a class="underline" href="#">{{pay.date}}</a></td>
                            <td class="px-6 py-4">{{pay.reason}}</td>
                            <td class="px-6 py-4">{{pay.amount}}</td>
                            <td class="px-6 py-4">{{pay.received_by.name}} | {{pay.received_by.emp_id}} </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </section>
    </div>
  </div>
{% endblock body %}