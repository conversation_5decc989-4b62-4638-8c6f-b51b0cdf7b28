{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">View Salary Payment</h3>
            <div class="flex space-x-2">
                <a href="{% url 'salary:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Salary Payments
                </a>
            </div>
        </div>

        <!-- Payment Details -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="grid grid-cols-2 gap-6">
                <!-- Left Column -->
                <div>
                    <h4 class="text-xl font-semibold mb-4">Employee Information</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-gray-600 text-sm">Employee Name</p>
                            <p class="font-medium">{{ payment.employee.name }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Employee ID</p>
                            <p class="font-medium">{{ payment.employee.emp_id }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Role</p>
                            <p class="font-medium">{{ payment.employee.role|title }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Employment Type</p>
                            <p class="font-medium">{{ payment.get_employment_type_display }}</p>
                        </div>
                    </div>

                    <h4 class="text-xl font-semibold mt-6 mb-4">Payment Information</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p class="text-gray-600 text-sm">Payroll ID</p>
                            <p class="font-medium">{{ payment.payroll_id }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Month & Year</p>
                            <p class="font-medium">{{ payment.month|date:"F Y" }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Payment Status</p>
                            <p class="font-medium">
                                {% if payment.payment_status == 'paid' %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Paid</span>
                                {% else %}
                                <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded">Pending</span>
                                {% endif %}
                            </p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Payment Method</p>
                            <p class="font-medium">{{ payment.get_payment_method_display }}</p>
                        </div>
                        {% if payment.payment_date %}
                        <div>
                            <p class="text-gray-600 text-sm">Payment Date</p>
                            <p class="font-medium">{{ payment.payment_date|date:"d-M-Y H:i" }}</p>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <h4 class="text-xl font-semibold mb-4">Salary Breakdown</h4>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between py-2 border-b">
                            <span>Base Salary</span>
                            <span class="font-medium">{{ payment.base_salary|format_khr }}</span>
                        </div>
                        {% if payment.bonus > 0 %}
                        <div class="flex justify-between py-2 border-b">
                            <span>Bonus</span>
                            <span class="font-medium text-green-600">+ {{ payment.bonus|format_khr }}</span>
                        </div>
                        {% endif %}
                        {% if payment.overtime_hours > 0 %}
                        <div class="flex justify-between py-2 border-b">
                            <span>Overtime ({{ payment.overtime_hours }} hours)</span>
                            <span class="font-medium text-green-600">+ 0៛</span>
                        </div>
                        {% endif %}
                        {% if payment.deduction > 0 %}
                        <div class="flex justify-between py-2 border-b">
                            <span>Deductions</span>
                            <span class="font-medium text-red-600">- {{ payment.deduction|format_khr }}</span>
                        </div>
                        {% endif %}
                        <div class="flex justify-between py-3 mt-2 border-t-2 border-gray-400">
                            <span class="font-bold">Final Pay</span>
                            <span class="font-bold text-lg">{{ payment.final_pay|format_khr }}</span>
                        </div>
                    </div>

                    {% if payment.notes %}
                    <div class="mt-6">
                        <h4 class="text-xl font-semibold mb-2">Notes</h4>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <p>{{ payment.notes }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Action Buttons -->
                    <div class="mt-8 flex space-x-3">
                        {% if payment.payment_status == 'pending' %}
                        <a href="{% url 'salary:edit' payment.id %}" class="bg-green-600 text-white px-4 py-2 rounded flex-1 text-center">Edit Payment</a>
                        <a href="{% url 'salary:process' payment.id %}" class="bg-blue-600 text-white px-4 py-2 rounded flex-1 text-center"
                           onclick="return confirm('Are you sure you want to process this payment?')">Process Payment</a>
                        {% else %}
                        <a href="{% url 'salary:print' payment.id %}" class="bg-purple-600 text-white px-4 py-2 rounded flex-1 text-center">Print Salary Slip</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
