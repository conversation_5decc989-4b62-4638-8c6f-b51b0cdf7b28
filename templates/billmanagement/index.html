{% extends 'base.html' %}
{% load custom_filters %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h3 class="text-2xl font-bold mb-3 sm:mb-0">Bill Management</h3>
            <div class="flex flex-wrap gap-2">
                <a href="{% url 'billmanagement:create_bill' %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-plus mr-2"></i>Add New Bill
                </a>
                <a href="{% url 'billmanagement:bill_list' %}" class="bg-blue-900 hover:bg-blue-950 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-history mr-2"></i>Payment History
                </a>
                <a href="{% url 'billmanagement:template_list' %}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-file-invoice mr-2"></i>Receipt Templates
                </a>
            </div>
        </div>

        <!-- Summary Statistics Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <!-- Total Pending -->
            <div class="bg-white p-5 rounded-lg shadow-md border-l-4 border-red-600 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-700">Total Pending</h4>
                        <p class="text-3xl font-bold text-red-600 mt-2">{{ total_pending|format_khr }}</p>
                        <p class="text-sm text-gray-500 mt-1">Amount due for unpaid bills</p>
                    </div>
                    <div class="text-red-200">
                        <i class="fa-solid fa-clock text-3xl"></i>
                    </div>
                </div>
            </div>

            <!-- Total Paid -->
            <div class="bg-white p-5 rounded-lg shadow-md border-l-4 border-green-600 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-700">Total Paid</h4>
                        <p class="text-3xl font-bold text-green-600 mt-2">{{ total_paid|format_khr }}</p>
                        <p class="text-sm text-gray-500 mt-1">Total payments made</p>
                    </div>
                    <div class="text-green-200">
                        <i class="fa-solid fa-check-circle text-3xl"></i>
                    </div>
                </div>
            </div>

            <!-- Upcoming Bills -->
            <div class="bg-white p-5 rounded-lg shadow-md border-l-4 border-blue-600 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-700">Upcoming Bills</h4>
                        <p class="text-3xl font-bold text-blue-600 mt-2">{{ upcoming_bills.count }}</p>
                        <p class="text-sm text-gray-500 mt-1">Bills pending payment</p>
                    </div>
                    <div class="text-blue-200">
                        <i class="fa-solid fa-calendar-alt text-3xl"></i>
                    </div>
                </div>
            </div>

            <!-- Recent Payments -->
            <div class="bg-white p-5 rounded-lg shadow-md border-l-4 border-purple-600 hover:shadow-lg transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-700">Recent Payments</h4>
                        <p class="text-3xl font-bold text-purple-600 mt-2">{{ recent_payments.count }}</p>
                        <p class="text-sm text-gray-500 mt-1">Bills paid recently</p>
                    </div>
                    <div class="text-purple-200">
                        <i class="fa-solid fa-receipt text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Filter Section -->
        <div class="bg-white p-5 rounded-lg shadow-md mb-6">
            <h4 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                <i class="fa-solid fa-filter mr-2 text-blue-900"></i>Filter Bills
            </h4>
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 items-end">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select id="category-filter" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Categories</option>
                        {% for category_value, category_name in bill_categories %}
                        <option value="{{ category_value }}" {% if category_filter == category_value %}selected{% endif %}>{{ category_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Month & Year</label>
                    <input type="month" id="month-filter" class="border w-full p-3 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="{{ month_filter }}">
                </div>

                <div class="flex space-x-2">
                    <button id="apply-filters" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md transition-colors flex-1 flex justify-center items-center">
                        <i class="fa-solid fa-filter mr-2"></i> Apply Filters
                    </button>
                    <a href="{% url 'billmanagement:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-md transition-colors flex items-center justify-center">
                        <i class="fa-solid fa-undo mr-2"></i>Reset
                    </a>
                </div>
            </div>
        </div>

        <!-- Improved Bills Grid -->
        <h4 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
            <i class="fa-solid fa-file-invoice-dollar mr-2 text-blue-900"></i>Pending Bills
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-8">
            {% for bill in bills %}
            {% if bill.payment_status == 'pending' or status_filter == 'paid' %}
            <div class="bg-white rounded-lg shadow-md overflow-hidden bill-card hover:shadow-lg transition-shadow"
                 data-category="{{ bill.category }}"
                 data-month="{{ bill.month_year|date:'Y-m' }}">
                <div class="p-4 border-b bg-gray-50 flex justify-between items-center">
                    <h3 class="font-bold text-gray-800 truncate">{{ bill.provider }}</h3>
                    <span class="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full">{{ bill.get_category_display }}</span>
                </div>

                <div class="p-4">
                    <div class="grid grid-cols-2 gap-4 mb-3">
                        <div>
                            <p class="text-xs text-gray-500 uppercase">Bill ID</p>
                            <p class="text-sm font-medium">{{ bill.bill_id }}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500 uppercase">Period</p>
                            <p class="text-sm font-medium">{{ bill.get_payment_period_display }}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500 uppercase">Month & Year</p>
                            <p class="text-sm font-medium">{{ bill.month_year|date:"M Y" }}</p>
                        </div>
                        <div>
                            <p class="text-xs text-gray-500 uppercase">Amount</p>
                            <p class="text-sm font-bold text-blue-600">{{ bill.amount_khr|format_khr }}</p>
                        </div>
                    </div>

                    <div class="flex space-x-2 mt-4">
                        <a href="{% url 'billmanagement:view_bill' bill.id %}" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 rounded transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-eye mr-1"></i> View
                        </a>
                        {% if bill.payment_status == 'pending' %}
                        <a href="{% url 'billmanagement:process_payment' bill.id %}" class="flex-1 bg-green-600 hover:bg-green-700 text-white text-center py-2 rounded transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-credit-card mr-1"></i> Pay
                        </a>
                        {% else %}
                        <a href="{% url 'billmanagement:print_receipt' bill.id %}" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white text-center py-2 rounded transition-colors flex items-center justify-center">
                            <i class="fa-solid fa-print mr-1"></i> Print
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
            {% empty %}
            <div class="col-span-1 md:col-span-2 lg:col-span-3 bg-white p-8 rounded-lg shadow-md text-center">
                <div class="py-6">
                    <i class="fa-solid fa-file-invoice text-gray-300 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-700 mb-2">No Pending Bills</h3>
                    <p class="text-gray-500 mb-4">There are no pending bills matching your filter criteria.</p>
                    <a href="{% url 'billmanagement:create_bill' %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded inline-block transition-colors">
                        <i class="fa-solid fa-plus mr-2"></i>Add New Bill
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Improved Bulk Payment Section -->
        <div class="bg-white p-5 rounded-lg shadow-md">
            <h3 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fa-solid fa-money-bill-wave mr-2 text-green-600"></i>Bulk Payment
            </h3>

            <form method="post" action="{% url 'billmanagement:bulk_actions' %}" id="bulk-actions-form">
                {% csrf_token %}
                <input type="hidden" name="bulk_action" value="process">

                <div class="mb-4">
                    <div class="flex justify-between items-center mb-3">
                        <p class="text-sm text-gray-600">Select multiple bills to pay them at once</p>
                        <div class="flex items-center">
                            <input type="checkbox" id="select-all-bills" class="w-4 h-4 mr-2">
                            <label for="select-all-bills" class="text-sm font-medium">Select All</label>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {% for bill in bills %}
                        {% if bill.payment_status == 'pending' %}
                        <div class="flex items-center p-3 border rounded-md hover:bg-gray-50 transition-colors">
                            <input type="checkbox" name="selected_bills" value="{{ bill.id }}" class="bill-checkbox w-4 h-4 mr-3">
                            <div class="flex-1">
                                <p class="font-medium">{{ bill.provider }}</p>
                                <div class="flex justify-between mt-1">
                                    <span class="text-sm text-gray-600">{{ bill.month_year|date:"M Y" }}</span>
                                    <span class="text-sm font-semibold text-blue-600">{{ bill.amount_khr|format_khr }}</span>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>

                <div class="flex justify-end mt-4">
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors flex items-center">
                        <i class="fa-solid fa-credit-card mr-2"></i> Pay Selected Bills
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced filter functionality
        const applyFiltersBtn = document.getElementById('apply-filters');
        const categoryFilter = document.getElementById('category-filter');
        const monthFilter = document.getElementById('month-filter');
        const billCards = document.querySelectorAll('.bill-card');

        applyFiltersBtn.addEventListener('click', function() {
            const categoryValue = categoryFilter.value;
            const monthValue = monthFilter.value;

            billCards.forEach(card => {
                let showCard = true;

                if (categoryValue && card.dataset.category !== categoryValue) {
                    showCard = false;
                }

                if (monthValue && card.dataset.month !== monthValue) {
                    showCard = false;
                }

                card.style.display = showCard ? 'block' : 'none';
            });
        });

        // Select all bills functionality
        const selectAllCheckbox = document.getElementById('select-all-bills');
        const billCheckboxes = document.querySelectorAll('.bill-checkbox');

        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                billCheckboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }

        // Bulk payment form validation
        const bulkActionsForm = document.getElementById('bulk-actions-form');
        if (bulkActionsForm) {
            bulkActionsForm.addEventListener('submit', function(e) {
                const checkedBoxes = document.querySelectorAll('.bill-checkbox:checked');

                if (checkedBoxes.length === 0) {
                    e.preventDefault();
                    alert('Please select at least one bill to pay.');
                    return false;
                }

                if (!confirm('Are you sure you want to process payment for the selected bills?')) {
                    e.preventDefault();
                    return false;
                }

                return true;
            });
        }
    });
</script>
{% endblock %}
