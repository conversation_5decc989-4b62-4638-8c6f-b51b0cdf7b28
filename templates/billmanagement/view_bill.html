{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <div class="flex items-center mb-3 sm:mb-0">
                <h3 class="text-2xl font-bold">Bill Details</h3>
                <span class="ml-3">
                    {% if bill.payment_status == 'paid' %}
                    <span class="bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full border border-green-200">
                        <i class="fa-solid fa-check-circle mr-1"></i>Paid
                    </span>
                    {% else %}
                    <span class="bg-yellow-100 text-yellow-800 text-sm font-medium px-3 py-1 rounded-full border border-yellow-200">
                        <i class="fa-solid fa-clock mr-1"></i>Pending
                    </span>
                    {% endif %}
                </span>
            </div>
            <div class="flex flex-wrap gap-2">
                <a href="{% url 'billmanagement:index' %}" class="bg-blue-900 hover:bg-blue-950 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Bills
                </a>
                {% if bill.payment_status == 'pending' %}
                <a href="{% url 'billmanagement:edit_bill' bill.id %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-edit mr-2"></i>Edit Bill
                </a>
                {% endif %}
                <a href="{% url 'billmanagement:delete_bill' bill.id %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-trash mr-2"></i>Delete Bill
                </a>
            </div>
        </div>

        <!-- Bill Details Card -->
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <!-- Bill Header with ID and Status -->
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center border-b pb-4 mb-6">
                <div>
                    <h4 class="text-xl font-bold text-gray-800 flex items-center">
                        <i class="fa-solid fa-file-invoice-dollar mr-2 text-blue-900"></i>{{ bill.provider }}
                    </h4>
                    <p class="text-sm text-gray-600 mt-1">{{ bill.bill_id }} - {{ bill.get_category_display }}</p>
                </div>
                <div class="mt-3 sm:mt-0">
                    {% if bill.payment_status == 'paid' %}
                    <div class="flex items-center bg-green-50 text-green-800 px-4 py-2 rounded-md border border-green-200">
                        <i class="fa-solid fa-check-circle mr-2 text-green-600"></i>
                        <div>
                            <div class="font-semibold">Paid on {{ bill.payment_date|date:"d M Y" }}</div>
                            <div class="text-xs">by {{ bill.paid_by.name }}</div>
                        </div>
                    </div>
                    {% else %}
                    <div class="flex items-center bg-yellow-50 text-yellow-800 px-4 py-2 rounded-md border border-yellow-200">
                        <i class="fa-solid fa-clock mr-2 text-yellow-600"></i>
                        <div class="font-semibold">Payment Pending</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Bill Information Column -->
                <div class="lg:col-span-2">
                    <h4 class="text-md font-semibold text-gray-700 mb-4 border-l-4 border-blue-900 pl-3">Bill Information</h4>

                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5 mb-6">
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Category</p>
                            <p class="font-medium">{{ bill.get_category_display }}</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Provider/Vendor</p>
                            <p class="font-medium">{{ bill.provider }}</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Month & Year</p>
                            <p class="font-medium">{{ bill.month_year|date:"F Y" }}</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Payment Period</p>
                            <p class="font-medium">{{ bill.get_payment_period_display }}</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Payment Method</p>
                            <p class="font-medium">{{ bill.get_payment_method_display }}</p>
                        </div>
                        <div class="bg-gray-50 p-3 rounded-md">
                            <p class="text-xs text-gray-500 uppercase">Recurring Bill</p>
                            <p class="font-medium">
                                {% if bill.is_recurring %}
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded">Yes</span>
                                {% else %}
                                <span class="bg-gray-200 text-gray-800 text-xs font-medium px-2 py-0.5 rounded">No</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>

                    {% if bill.description or bill.notes %}
                    <div class="mb-6">
                        {% if bill.description %}
                        <div class="mb-4">
                            <h5 class="text-sm font-semibold text-gray-700 mb-2">Description</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p>{{ bill.description }}</p>
                            </div>
                        </div>
                        {% endif %}

                        {% if bill.notes %}
                        <div>
                            <h5 class="text-sm font-semibold text-gray-700 mb-2">Notes</h5>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <p>{{ bill.notes }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>

                <!-- Payment Details Column -->
                <div class="lg:col-span-1">
                    <h4 class="text-md font-semibold text-gray-700 mb-4 border-l-4 border-green-600 pl-3">Payment Details</h4>

                    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
                        <div class="flex justify-between py-3 border-b">
                            <span class="text-gray-600">Amount (KHR)</span>
                            <span class="font-bold text-blue-900">{{ bill.amount_khr|format_khr }}</span>
                        </div>
                        {% if bill.amount_usd %}
                        <div class="flex justify-between py-3 border-b">
                            <span class="text-gray-600">Amount (USD)</span>
                            <span class="font-bold text-blue-900">{{ bill.amount_usd|format_usd }}</span>
                        </div>
                        {% endif %}
                        <div class="flex justify-between py-3 border-b">
                            <span class="text-gray-600">Payment Period</span>
                            <span class="font-medium">{{ bill.get_payment_period_display }}</span>
                        </div>
                        <div class="flex justify-between py-3">
                            <span class="text-gray-600">Status</span>
                            <span class="font-medium">
                                {% if bill.payment_status == 'paid' %}
                                <span class="text-green-600 font-semibold">Paid</span>
                                {% else %}
                                <span class="text-yellow-600 font-semibold">Pending</span>
                                {% endif %}
                            </span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        {% if bill.payment_status == 'pending' %}
                        <a href="{% url 'billmanagement:process_payment' bill.id %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-md flex-1 text-center block transition-colors">
                            <i class="fa-solid fa-credit-card mr-2"></i>Process Payment
                        </a>
                        <a href="{% url 'billmanagement:edit_bill' bill.id %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-md flex-1 text-center block transition-colors">
                            <i class="fa-solid fa-edit mr-2"></i>Edit Bill
                        </a>
                        {% else %}
                        <a href="{% url 'billmanagement:print_receipt' bill.id %}" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-md flex-1 text-center block transition-colors">
                            <i class="fa-solid fa-print mr-2"></i>Print Receipt
                        </a>
                        {% endif %}
                        <a href="{% url 'billmanagement:delete_bill' bill.id %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-md flex-1 text-center block transition-colors">
                            <i class="fa-solid fa-trash mr-2"></i>Delete Bill
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
