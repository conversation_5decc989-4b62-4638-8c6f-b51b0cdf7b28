{% extends 'base.html' %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Process Payment</h3>
            <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-blue-900 text-white px-4 py-2 rounded">Back to Bill</a>
        </div>

        <div class="bg-white p-6 rounded shadow-md">
            <h2 class="text-2xl font-bold mb-6">Process Payment for {{ bill.bill_id }}</h2>

            <div class="mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                        <p class="text-gray-600 text-sm">Provider</p>
                        <p class="font-medium">{{ bill.provider }}</p>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">Category</p>
                        <p class="font-medium">{{ bill.get_category_display }}</p>
                    </div>
                </div>

                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6">
                    <p class="text-sm text-yellow-700">You can adjust the payment amount below if needed. This is useful for bills like electricity where the amount may vary each month.</p>
                </div>

                <form method="POST" action="{% url 'billmanagement:process_payment' bill.id %}">
                    {% csrf_token %}

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- Amount (KHR) -->
                        <div>
                            <label for="amount_khr" class="block text-sm font-medium text-gray-700 mb-1">Amount (KHR)*</label>
                            <input type="number" id="amount_khr" name="amount_khr" value="{{ bill.amount_khr }}" class="border border-gray-300 rounded-md w-full p-3" required>
                        </div>

                        <!-- Amount (USD) -->
                        <div>
                            <label for="amount_usd" class="block text-sm font-medium text-gray-700 mb-1">Amount (USD)</label>
                            <input type="number" id="amount_usd" name="amount_usd" value="{{ bill.amount_usd|default:'' }}" step="0.01" class="border border-gray-300 rounded-md w-full p-3">
                        </div>
                    </div>

                    <div class="flex space-x-3">
                        <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded font-semibold">Process Payment</button>
                        <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-gray-500 text-white px-6 py-3 rounded font-semibold">Cancel</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}