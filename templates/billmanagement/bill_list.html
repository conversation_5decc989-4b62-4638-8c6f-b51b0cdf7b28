{% extends 'base.html' %}
{% load custom_filters %}
{% load permission_tags %}

{% block body %}
<!-- Check permission for delete functionality -->
{% has_permission user 'bill' 'full' as can_delete_bill %}

<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-6">
            <h3 class="text-2xl font-bold mb-3 sm:mb-0">Payment History</h3>
            <div class="flex flex-wrap gap-2">
                <a href="{% url 'billmanagement:index' %}" class="bg-blue-900 hover:bg-blue-950 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <a href="{% url 'billmanagement:create_bill' %}" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded transition-colors flex items-center">
                    <i class="fa-solid fa-plus mr-2"></i>Add New Bill
                </a>
            </div>
        </div>

        <!-- Bills List starts -->
        <div class="listSection bg-white p-5 rounded-lg shadow-md">
            <!-- Enhanced Filter Section -->
            <div class="border-b pb-4 mb-5">
                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                    <i class="fa-solid fa-receipt mr-2 text-blue-900"></i>Payment Records
                </h3>

                <form method="get" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-3 items-end">
                    <input type="hidden" name="status" value="paid">

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" class="border w-full p-2.5 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="">All Categories</option>
                            {% for category_value, category_name in bill_categories %}
                            <option value="{{ category_value }}" {% if category_filter == category_value %}selected{% endif %}>{{ category_name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Month & Year</label>
                        <input type="month" name="month" class="border w-full p-2.5 rounded-md bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500" value="{{ month_filter }}">
                    </div>

                    <div class="flex space-x-2 col-span-1 sm:col-span-2 lg:col-span-3">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2.5 rounded-md transition-colors flex items-center">
                            <i class="fa-solid fa-filter mr-2"></i>Apply Filters
                        </button>
                        <a href="{% url 'billmanagement:bill_list' %}?status=paid" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2.5 rounded-md transition-colors flex items-center">
                            <i class="fa-solid fa-undo mr-2"></i>Reset
                        </a>
                    </div>
                </form>
            </div>

            <!-- Responsive Table -->
            <div class="relative overflow-x-auto rounded-lg border border-gray-200">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs uppercase bg-blue-900 text-white">
                        <tr>
                            <th scope="col" class="px-4 py-3">Bill ID</th>
                            <th scope="col" class="px-4 py-3">Category</th>
                            <th scope="col" class="px-4 py-3 hidden md:table-cell">Provider</th>
                            <th scope="col" class="px-4 py-3">Month & Year</th>
                            <th scope="col" class="px-4 py-3 hidden sm:table-cell">Period</th>
                            <th scope="col" class="px-4 py-3">Amount</th>
                            <th scope="col" class="px-4 py-3 hidden md:table-cell">Payment Date</th>
                            <th scope="col" class="px-4 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bill in bills %}
                        {% if bill.payment_status == 'paid' %}
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-4 py-3 font-medium">{{ bill.bill_id }}</td>
                            <td class="px-4 py-3">
                                <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                                    {{ bill.get_category_display }}
                                </span>
                            </td>
                            <td class="px-4 py-3 hidden md:table-cell">{{ bill.provider }}</td>
                            <td class="px-4 py-3">{{ bill.month_year|date:"M Y" }}</td>
                            <td class="px-4 py-3 hidden sm:table-cell">{{ bill.get_payment_period_display }}</td>
                            <td class="px-4 py-3 font-semibold text-blue-900">{{ bill.amount_khr|format_khr }}</td>
                            <td class="px-4 py-3 hidden md:table-cell">{{ bill.payment_date|date:"d M Y" }}</td>
                            <td class="px-4 py-3">
                                <div class="flex flex-col sm:flex-row gap-2">
                                    <a href="{% url 'billmanagement:view_bill' bill.id %}" class="text-blue-600 hover:text-blue-900 transition-colors text-sm">
                                        <i class="fa-solid fa-eye"></i>
                                        <span class="hidden sm:inline ml-1">View</span>
                                    </a>
                                    <a href="{% url 'billmanagement:print_receipt' bill.id %}" class="text-purple-600 hover:text-purple-900 transition-colors text-sm">
                                        <i class="fa-solid fa-print"></i>
                                        <span class="hidden sm:inline ml-1">Print</span>
                                    </a>
                                    {% if can_delete_bill %}
                                    <button type="button"
                                            class="delete-bill-btn text-red-600 hover:text-red-800 text-sm font-medium transition duration-200"
                                            data-bill-id="{{ bill.id }}"
                                            data-bill-reference="{{ bill.bill_id }}"
                                            data-provider="{{ bill.provider }}"
                                            data-amount="{{ bill.amount_khr|format_khr }}"
                                            data-category="{{ bill.get_category_display }}"
                                            data-status="{{ bill.get_payment_status_display }}">
                                        <i class="fas fa-trash mr-1"></i><span class="hidden sm:inline">Delete</span>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endif %}
                        {% empty %}
                        <tr class="bg-white border-b">
                            <td colspan="8" class="px-4 py-6 text-center">
                                <div class="py-4">
                                    <i class="fa-solid fa-receipt text-gray-300 text-4xl mb-3"></i>
                                    <p class="text-gray-500">No payment records found matching the filter criteria.</p>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Mobile View Info -->
            <div class="mt-4 text-xs text-gray-500 md:hidden">
                <p><i class="fa-solid fa-info-circle mr-1"></i> Swipe horizontally to see more details</p>
            </div>
        </div>
        <!-- Bills List ends -->
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Delete bill functionality with confirmation
        document.querySelectorAll('.delete-bill-btn').forEach(button => {
            button.addEventListener('click', function() {
                const billId = this.getAttribute('data-bill-id');
                const billReference = this.getAttribute('data-bill-reference');
                const provider = this.getAttribute('data-provider');
                const amount = this.getAttribute('data-amount');
                const category = this.getAttribute('data-category');
                const status = this.getAttribute('data-status');

                // Create confirmation dialog
                const confirmDialog = confirm(
                    `⚠️ DELETE BILL CONFIRMATION ⚠️\n\n` +
                    `Bill Details:\n` +
                    `• Reference: ${billReference}\n` +
                    `• Provider: ${provider}\n` +
                    `• Category: ${category}\n` +
                    `• Amount: ${amount}\n` +
                    `• Status: ${status}\n\n` +
                    `⚠️ FINANCIAL WARNING ⚠️\n` +
                    `${status === 'Paid' ?
                        `This will add ${amount} back to gym funds.` :
                        `This will remove the pending bill record.`}\n\n` +
                    `This action cannot be undone!\n\n` +
                    `Are you sure you want to delete this bill?`
                );

                if (confirmDialog) {
                    // Create a form and submit it
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/bill/delete/${billId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Add to body and submit
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });
</script>
{% endblock %}
