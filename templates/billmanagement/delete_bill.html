{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Delete Bill</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Bill Details
                </a>
            </div>
        </div>

        <!-- Delete Confirmation -->
        <div class="bg-white p-6 rounded shadow-md">
            <div class="text-center">
                <div class="mb-4">
                    <i class="fa-solid fa-triangle-exclamation text-red-600 text-5xl"></i>
                </div>
                <h4 class="text-xl font-semibold mb-2">Confirm Deletion</h4>
                <p class="text-gray-600 mb-6">Are you sure you want to delete this bill? This action cannot be undone.</p>
                
                <div class="bg-gray-100 p-4 rounded-lg mb-6 max-w-md mx-auto text-left">
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <p class="text-gray-600 text-sm">Bill ID:</p>
                            <p class="font-medium">{{ bill.bill_id }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Provider:</p>
                            <p class="font-medium">{{ bill.provider }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Category:</p>
                            <p class="font-medium">{{ bill.get_category_display }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Month & Year:</p>
                            <p class="font-medium">{{ bill.month_year|date:"F Y" }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Amount:</p>
                            <p class="font-medium">{{ bill.amount_khr|format_khr }}</p>
                        </div>
                        <div>
                            <p class="text-gray-600 text-sm">Status:</p>
                            <p class="font-medium">{{ bill.get_payment_status_display }}</p>
                        </div>
                    </div>
                </div>
                
                <form method="post" class="flex justify-center space-x-4">
                    {% csrf_token %}
                    <button type="submit" class="bg-red-600 text-white px-6 py-3 rounded font-semibold">Delete Bill</button>
                    <a href="{% url 'billmanagement:view_bill' bill.id %}" class="bg-gray-500 text-white px-6 py-3 rounded font-semibold">Cancel</a>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
