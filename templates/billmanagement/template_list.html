{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Bill Receipt Templates</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Bills
                </a>
                <a href="{% url 'billmanagement:create_template' %}" class="bg-green-600 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-plus mr-2"></i>Create New Template
                </a>
            </div>
        </div>

        <!-- Templates List -->
        <div class="bg-white p-4 rounded shadow-md">
            <h3 class="text-xl font-bold mb-4">Available Templates</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for template in templates %}
                <div class="border rounded-lg overflow-hidden">
                    <div class="p-4" style="background-color: {{ template.background_color }}; color: {{ template.text_color }};">
                        <h4 class="text-lg font-semibold mb-2">{{ template.name }}</h4>
                        <div class="text-center py-4">
                            {% if template.company_logo %}
                            <img src="{{ template.company_logo.url }}" alt="Logo" class="h-12 mx-auto mb-2">
                            {% endif %}
                            <p class="text-xl font-bold" style="color: {{ template.text_color }};">{{ template.header_text }}</p>
                            <p class="text-md" style="color: {{ template.accent_color }};">{{ template.subheader_text }}</p>
                        </div>
                    </div>
                    <div class="p-4 bg-gray-50">
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Language:</span>
                            <span class="font-medium">
                                {% if template.language == 'en' %}English
                                {% elif template.language == 'km' %}Khmer
                                {% else %}Bilingual
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Default:</span>
                            <span class="font-medium">
                                {% if template.is_default %}
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded">Yes</span>
                                {% else %}
                                <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded">No</span>
                                {% endif %}
                            </span>
                        </div>
                        <div class="flex justify-between mb-2">
                            <span class="text-gray-600">Created:</span>
                            <span class="font-medium">{{ template.created_at|date:"d-M-Y" }}</span>
                        </div>
                        <div class="mt-4 flex justify-between">
                            <a href="{% url 'billmanagement:preview_template' template.id %}" class="text-blue-600 hover:underline">
                                <i class="fa-solid fa-eye mr-1"></i>Preview
                            </a>
                            <a href="{% url 'billmanagement:edit_template' template.id %}" class="text-green-600 hover:underline">
                                <i class="fa-solid fa-edit mr-1"></i>Edit
                            </a>
                            <a href="{% url 'billmanagement:delete_template' template.id %}" class="text-red-600 hover:underline">
                                <i class="fa-solid fa-trash mr-1"></i>Delete
                            </a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-span-3 p-4 bg-gray-100 rounded text-center">
                    <p class="text-gray-600">No templates found. <a href="{% url 'billmanagement:create_template' %}" class="text-blue-600 hover:underline">Create your first template</a>.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
