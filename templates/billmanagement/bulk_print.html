{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}



{% block extra_css %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-section, .print-section * {
            visibility: visible;
        }
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none !important;
        }
        .page-break {
            page-break-after: always;
        }
    }

    .receipt-container {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto 30px;
        padding: 20px;
        border: 1px solid #ddd;
    }

    .receipt-header {
        text-align: center;
        margin-bottom: 20px;
    }

    .receipt-header h1 {
        margin: 5px 0;
    }

    .receipt-header h2 {
        margin: 5px 0;
    }

    .receipt-body {
        margin-bottom: 20px;
    }

    .receipt-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .receipt-table th, .receipt-table td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    .receipt-table th {
        background-color: #0066cc;
        color: white;
    }

    .receipt-footer {
        text-align: center;
        margin-top: 30px;
    }

    .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
    }

    .signature-box {
        text-align: center;
        width: 40%;
    }

    .signature-line {
        border-top: 1px solid #000;
        margin-top: 50px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4 no-print">
            <h3 class="text-2xl font-bold">Bulk Print Receipts</h3>
            <div class="flex space-x-2">
                <a href="{% url 'billmanagement:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Bills
                </a>
                <button onclick="window.print()" class="bg-purple-600 text-white px-4 py-2 rounded"><i class="fa-solid fa-print mr-2"></i> Print All Receipts</button>
            </div>
        </div>

        <!-- Print Summary -->
        <div class="bg-white p-4 rounded shadow-md mb-4 no-print">
            <h4 class="text-lg font-semibold mb-2">Print Summary</h4>
            <p>Printing {{ bills.count }} receipt(s).</p>
            <p class="text-sm text-gray-600 mt-2">Click the "Print All Receipts" button above to print all receipts at once.</p>
        </div>

        <!-- Receipts -->
        <div class="print-section">
            {% for bill in bills %}
            <div class="receipt-container {% if not forloop.last %}page-break{% endif %}" style="background-color: #ffffff;">
                <div class="receipt-header">
                    <h1 style="color: #000000;">LEGEND FITNESS</h1>
                    <h2 style="color: #0066cc;">Bill Payment Receipt</h2>
                </div>

                <div class="receipt-body">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div>
                            <p><strong>Receipt No:</strong> {{ bill.bill_id }}</p>
                            <p><strong>Date:</strong> {{ bill.payment_date|date:"d-M-Y H:i" }}</p>
                            <p><strong>Payment Method:</strong> {{ bill.get_payment_method_display }}</p>
                        </div>
                        <div>
                            <p><strong>Provider/Vendor:</strong> {{ bill.provider }}</p>
                            <p><strong>Category:</strong> {{ bill.get_category_display }}</p>
                            <p><strong>Period:</strong> {{ bill.month_year|date:"F Y" }} ({{ bill.get_payment_period_display }})</p>
                        </div>
                    </div>

                    <table class="receipt-table">
                        <thead><tr>
                                <th>Description</th>
                                <th>Period</th>
                                <th>Amount (KHR)</th>
                                {% if bill.amount_usd %}
                                <th>Amount (USD)</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ bill.get_category_display }} - {{ bill.provider }}</td>
                                <td>{{ bill.get_payment_period_display }}</td>
                                <td>{{ bill.amount_khr|format_khr }}</td>
                                {% if bill.amount_usd %}
                                <td>{{ bill.amount_usd|format_usd }}</td>
                                {% endif %}
                            </tr>
                            {% if bill.description %}
                            <tr>
                                <td colspan="{% if bill.amount_usd %}4{% else %}3{% endif %}">
                                    <strong>Description:</strong> {{ bill.description }}
                                </td>
                            </tr>
                            {% endif %}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="2"><strong>Total</strong></td>
                                <td><strong>{{ bill.amount_khr|format_khr }}</strong></td>
                                {% if bill.amount_usd %}
                                <td><strong>{{ bill.amount_usd|format_usd }}</strong></td>
                                {% endif %}
                            </tr>
                        </tfoot>
                    </table>

                    {% if bill.notes %}
                    <div class="mb-4">
                        <p><strong>Notes:</strong></p>
                        <p>{{ bill.notes }}</p>
                    </div>
                    {% endif %}

                    <div class="mb-4 p-3 bg-gray-100 rounded">
                        <p><strong>Legend Fitness</strong></p>
                        <p>Address: Phnom Penh, Cambodia</p>
                        <p>Phone: +855 XX XXX XXX</p>
                        <p>Email: <EMAIL></p>
                    </div>
                </div>

                <div class="signature-section">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p>Authorized Signature</p>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p>Received By</p>
                    </div>
                </div>

                <div class="receipt-footer">
                    <p>Thank you for your business!</p>
                    <p>Printed on: {{ now|date:"d-M-Y H:i" }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
