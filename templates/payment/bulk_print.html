{% extends 'base.html' %}
{% load custom_filters %}



{% block head %}
<style>
    @media print {
        /* Hide everything by default */
        body * {
            visibility: hidden;
        }
        
        /* Hide sidebar specifically */
        .sidebar {
            display: none !important;
        }
        
        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
            display: block !important;
        }
        
        /* Hide everything else */
        body > *:not(.main-content),
        .main-content > *:not(.conponentSection),
        .conponentSection > *:not(.componentWrapper),
        .componentWrapper > *:not(.bg-white),
        .bg-white > *:not(.print-section) {
            display: none !important;
        }
        
        /* Position the print section at the top left */
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            margin: 0;
            padding: 0;
        }
        
        /* Remove margin that accommodates sidebar */
        .conponentSection {
            margin-left: 0 !important;
            padding: 0 !important;
        }
        
        /* Hide elements with no-print class */
        .no-print {
            display: none !important;
        }
        
        /* Reset any background colors for printing */
        .bg-gray-200 {
            background-color: white !important;
        }
        
        /* Add page breaks between receipts */
        .page-break {
            page-break-after: always;
        }
    }

    .receipt-border {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
        margin-bottom: 30px;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md">
            <!-- Header with navigation -->
            <div class="flex justify-between items-center mb-4 no-print">
                <h3 class="text-2xl font-bold">Bulk Print Payment Receipts</h3>
                <div class="flex space-x-2">
                    <a href="{% url 'payment:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                        <i class="fa-solid fa-arrow-left mr-2"></i>Back to Payments
                    </a>
                </div>
            </div>
            
            <!-- Print Button -->
            <div class="mb-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print All Receipts ({{ payments|length }})</button>
                <a href="{% url 'payment:index' %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Cancel</a>
            </div>
            
            <!-- Payment Receipts -->
            <div class="print-section">
                {% for payment in payments %}
                <div class="receipt-border {% if not forloop.last %}page-break{% endif %}">
                    <!-- Receipt Header -->
                    <div class="text-center mb-6">
                        <h1 class="text-3xl font-bold">LEGEND FITNESS</h1>
                        <p class="text-lg">Payment Receipt</p>
                    </div>

                    <!-- Receipt Content -->
                    <div class="border-t border-b border-gray-300 py-4 mb-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <p><strong>Invoice No:</strong> {{ payment.invoice_no }}</p>
                                <p><strong>Member:</strong> {{ payment.member.name }}</p>
                                <p><strong>Member ID:</strong> {{ payment.member.member_id }}</p>
                            </div>
                            <div>
                                <p><strong>Date:</strong> {{ payment.payment_date|date:"d-M-Y H:i" }}</p>
                                <p><strong>Payment Method:</strong> {{ payment.get_payment_method_display }}</p>
                                <p><strong>Collected By:</strong> {{ payment.collector.username }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Details -->
                    <div class="mb-6">
                        <h2 class="text-xl font-bold mb-3">Payment Details</h2>
                        <table class="w-full">
                            <tr class="border-b">
                                <td class="py-2">Amount</td>
                                <td class="py-2 text-right">{{ payment.amount_khr|format_khr }}</td>
                            </tr>
                            {% if payment.amount_usd %}
                            <tr class="border-b">
                                <td class="py-2">Amount (USD)</td>
                                <td class="py-2 text-right">${{ payment.amount_usd }}</td>
                            </tr>
                            {% endif %}
                            <tr class="border-t-2 border-gray-400 font-bold">
                                <td class="py-3">Total</td>
                                <td class="py-3 text-right text-lg">{{ payment.amount_khr|format_khr }}</td>
                            </tr>
                        </table>
                    </div>

                    {% if payment.notes %}
                    <div class="mb-6">
                        <h2 class="text-xl font-bold mb-2">Notes</h2>
                        <p class="bg-gray-50 p-3 rounded">{{ payment.notes }}</p>
                    </div>
                    {% endif %}

                    <!-- Receipt Footer -->
                    <div class="text-center text-sm khmer-font mt-8">
                        <div class="grid grid-cols-2 gap-8 mb-8">
                            <div class="text-center">
                                <p class="mb-12">_________________________</p>
                                <p>Member Signature</p>
                                <p>ហត្ថលេខាសមាជិក</p>
                            </div>
                            <div class="text-center">
                                <p class="mb-12">_________________________</p>
                                <p>Collector Signature</p>
                                <p>ហត្ថលេខាអ្នកប្រមូលប្រាក់</p>
                            </div>
                        </div>
                        <p class="mt-8">Thank you for your payment!</p>
                        <p>សូមអរគុណសម្រាប់ការទូទាត់របស់អ្នក!</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
