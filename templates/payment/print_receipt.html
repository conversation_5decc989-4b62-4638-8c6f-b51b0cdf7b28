{% extends 'base.html' %}
{% load custom_filters %}



{% block head %}
<style>
    /* Additional print styles specific to this template */
    @media print {
        /* Hide everything by default */
        body * {
            visibility: hidden;
        }

        /* Hide sidebar specifically */
        .sidebar {
            display: none !important;
        }

        /* Make the print section visible */
        .print-section, .print-section * {
            visibility: visible !important;
            display: block !important;
        }

        /* Hide everything else */
        body > *:not(.main-content),
        .main-content > *:not(.conponentSection),
        .conponentSection > *:not(.componentWrapper),
        .componentWrapper > *:not(.bg-white),
        .bg-white > *:not(.print-section) {
            display: none !important;
        }

        /* Ensure proper positioning */
        body {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Remove any background colors or borders */
        .receipt-border {
            border: 1px solid #ccc !important;
            background-color: white !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 800px !important;
        }
    }

    .receipt-border {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }

    {% if template %}
    /* Custom template styles */
    .payment-receipt {
        background-color: {{ template.background_color }};
        color: {{ template.text_color }};
    }

    .payment-receipt h1, .payment-receipt h2, .payment-receipt h3 {
        color: {{ template.accent_color }};
    }

    .payment-receipt .border-t, .payment-receipt .border-b, .payment-receipt .border-t-2 {
        border-color: {{ template.accent_color }};
    }

    /* Custom CSS from template */
    {{ template.custom_css|safe }}
    {% endif %}
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <!-- Template Selector -->
            <div class="no-print mb-4">
                <form method="get" class="flex items-center space-x-2">
                    <label class="text-sm font-medium">Template:</label>
                    <select name="template" class="border p-2 rounded" onchange="this.form.submit()">
                        {% for t in all_templates %}
                        <option value="{{ t.id }}" {% if template.id == t.id %}selected{% endif %}>
                            {{ t.name }} {% if t.is_default %}(Default){% endif %}
                        </option>
                        {% endfor %}
                    </select>
                    <a href="{% url 'payment:template_list' %}" class="text-blue-600 hover:underline">Manage Templates</a>
                </form>
            </div>

            <div class="print-section receipt-border payment-receipt">
                <!-- Receipt Header -->
                <div class="text-center mb-6">
                    {% if template.company_logo %}
                    <img src="{{ template.company_logo.url }}" alt="Company Logo" class="h-16 mx-auto mb-2">
                    {% endif %}
                    <h1 class="text-3xl font-bold">{{ template.header_text }}</h1>
                    <p class="text-lg">{{ template.subheader_text }}</p>
                </div>

                <!-- Receipt Content -->
                <div class="border-t border-b py-4 mb-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <p><strong>Invoice No:</strong> {{ payment.invoice_no }}</p>
                            <p><strong>Date:</strong> {{ payment.payment_date|date:"d-M-Y" }}</p>
                            <p><strong>Payment Method:</strong> {{ payment.get_payment_method_display }}</p>
                        </div>
                        <div>
                            <p><strong>Member ID:</strong> {{ payment.member.member_id }}</p>
                            <p><strong>Member Name:</strong> {{ payment.member.name }}</p>
                            <p><strong>Package:</strong> {{ payment.member.package.name }}</p>
                        </div>
                    </div>

                    <div class="mt-6">
                        <h3 class="text-xl font-bold mb-2">Payment Details</h3>
                        <div class="border-t pt-2">
                            <div class="flex justify-between">
                                <span>Amount (KHR):</span>
                                <span class="font-bold">{{ payment.amount_khr|format_khr }}</span>
                            </div>
                            {% if payment.amount_usd and payment.amount_usd > 0 %}
                            <div class="flex justify-between" style="display: none;">
                                <span>Amount (USD):</span>
                                <span class="font-bold">{{ payment.amount_usd }}៛</span>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    {% if payment.notes %}
                    <div class="mt-4">
                        <p><strong>Notes:</strong> {{ payment.notes }}</p>
                    </div>
                    {% endif %}
                </div>

                <!-- Receipt Footer -->
                <div class="text-center text-sm mt-8 {% if template.language == 'km' or template.language == 'both' %}khmer-font{% endif %}">
                    {% if template.show_signatures %}
                    <div class="grid grid-cols-2 gap-8 mb-8">
                        <div class="text-center">
                            <p class="mb-12">_________________________</p>
                            <p>Member Signature</p>
                            {% if template.language == 'km' or template.language == 'both' %}
                            <p>ហត្ថលេខាសមាជិក</p>
                            {% endif %}
                        </div>
                        <div class="text-center">
                            <p class="mb-12">_________________________</p>
                            <p>Collector Signature</p>
                            {% if template.language == 'km' or template.language == 'both' %}
                            <p>ហត្ថលេខាអ្នកប្រមូលប្រាក់</p>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    {% if template.language == 'km' or template.language == 'both' %}
                    <p>បានទទួល {{ payment.amount_khr|format_khr }}</p>
                    <p>ពី: {{ payment.member.name }} ({{ payment.member.member_id }})</p>
                    <p>សម្រាប់: {{ payment.member.package.name }}</p>
                    <p>ថ្ងៃ: {{ payment.payment_date|date:"d-M-Y" }}</p>
                    {% endif %}

                    <p class="mt-2">ABA: 012 345 678 | Telegram: @LegendFitness</p>
                    <p class="mt-4">{{ template.footer_text }}</p>
                    {% if template.language == 'km' or template.language == 'both' %}
                    <p>សូមអរគុណសម្រាប់ការទូទាត់របស់អ្នក!</p>
                    {% endif %}
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print Receipt</button>
                <a href="{% url 'payment:index' %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Back</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
