{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">Create New Payment</h3>
        </div>

        <!-- Payment Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-3 gap-4">
                    <!-- Member Selection -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Member*</label>
                        <select name="member" id="member-select" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="">Select Member</option>
                            {% for member in members %}
                            <option value="{{ member.id }}"
                                data-due="{{ member.due_payment }}"
                                data-package-price="{{ member.package.price_khr }}"
                                data-payment-status="{{ member.payment_status }}">{{ member.member_id }} - {{ member.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Amount KHR -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Amount (KHR)*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100"
                               name="amount_khr"
                               id="amount-khr"
                               type="number"
                               placeholder="120000"
                               required />
                        <p class="text-xs text-gray-500 mt-1">Exchange rate: 1$ = 4000៛</p>
                    </div>

                    <!-- Amount USD (Hidden) -->
                    <div style="display: none;">
                        <label class="block text-sm font-medium mb-1">Amount (USD) (Optional)</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100"
                               name="amount_usd"
                               type="number"
                               step="0.01"
                               value="0" />
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Payment Method*</label>
                        <select name="payment_method" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="cash">Cash</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Collector Initials -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Collector</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100"
                               type="text"
                               value="{{ request.user.username }}"
                               disabled />
                    </div>

                    <!-- Date -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Date</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100"
                               type="text"
                               value="{% now 'd-M-Y' %}"
                               disabled />
                    </div>

                    <!-- Notes -->
                    <div class="col-span-3">
                        <label class="block text-sm font-medium mb-1">Notes</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                  name="notes"
                                  rows="2"
                                  placeholder="Additional notes"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-span-3 flex justify-between mt-6 pt-4 border-t border-gray-200">
                        <a href="{% url 'payment:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200" type="submit">Record Payment</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Member payment form functionality
        const memberSelect = document.getElementById('member-select');
        const amountInput = document.getElementById('amount-khr');

        if (memberSelect && amountInput) {
            memberSelect.addEventListener('change', function() {
                const selectedOption = memberSelect.options[memberSelect.selectedIndex];

                if (selectedOption.value) {
                    // Get the data attributes from the selected option
                    const packagePrice = parseInt(selectedOption.getAttribute('data-package-price')) || 0;
                    const dueAmount = parseInt(selectedOption.getAttribute('data-due')) || 0;
                    const paymentStatus = selectedOption.getAttribute('data-payment-status');

                    // Remove any existing help text
                    const existingHelpText = document.getElementById('due-help-text');
                    if (existingHelpText) {
                        existingHelpText.remove();
                    }

                    // Remove any existing warning
                    const existingWarning = document.getElementById('payment-warning');
                    if (existingWarning) {
                        existingWarning.remove();
                    }

                    // Make sure the submit button is enabled by default
                    // (it will be disabled only for members who have already paid in full)
                    const submitButton = document.querySelector('button[type="submit"]');
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');

                    // Set the amount input value based on due amount and payment status
                    // The due_payment field can be 0 for two reasons:
                    // 1. The member has already paid in full
                    // 2. The member is new and hasn't made any payments yet

                    // Check if the due amount is greater than 0 (member has a pending payment)
                    if (dueAmount > 0) {
                        // Member has a due amount
                        amountInput.value = dueAmount;

                        // Add a helpful message below the input
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-red-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Due amount: ${dueAmount}៛ of ${packagePrice}៛ total`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                    // Check if the due amount is exactly 0 and the package price is also 0 (invalid state)
                    else if (dueAmount === 0 && packagePrice === 0) {
                        // This is an error state - member has no package assigned
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-red-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Error: Member has no package assigned`;
                        amountInput.parentNode.appendChild(helpText);
                        amountInput.value = 0;
                    }
                    // Check if the due amount is exactly equal to the package price (member hasn't paid anything)
                    else if (dueAmount === packagePrice) {
                        // Member hasn't made any payments yet
                        amountInput.value = packagePrice;

                        // Add a helpful message below the input
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-blue-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `First payment: ${packagePrice}៛`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                    // Check if the member has already paid in full (due amount is 0, payment status is 'paid', and package price is not 0)
                    else if (dueAmount === 0 && paymentStatus === 'paid' && packagePrice > 0) {
                        // Member has already paid in full
                        // Show a warning and disable the submit button by default
                        const warningDiv = document.createElement('div');
                        warningDiv.className = 'col-span-3 bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4';
                        warningDiv.id = 'payment-warning';
                        warningDiv.innerHTML = `
                            <p class="font-bold">Warning: Member has already paid in full</p>
                            <p>This member has no outstanding dues. Additional payments will automatically extend their membership.</p>

                            <div class="mt-3">
                                <p class="font-medium">Payment will be used to:</p>
                                <ul class="list-disc ml-5 mt-1">
                                    <li>Extend membership duration proportionally to payment amount</li>
                                    <li>For example, paying 50% of package price extends membership by 50% of package duration</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <label class="inline-flex items-center"><input type="checkbox" id="confirm-additional-payment" class="form-checkbox h-5 w-5 text-blue-600">
                                    <span class="ml-2">I confirm this is an intentional additional payment to extend membership</span>"</label>
                            </div>
                        `;

                        // Insert the warning before the form
                        const formElement = document.querySelector('form');
                        formElement.insertBefore(warningDiv, formElement.firstChild);

                        // Disable the submit button
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = true;
                        submitButton.classList.add('opacity-50', 'cursor-not-allowed');

                        // Add event listener to the checkbox
                        setTimeout(() => {
                            const checkbox = document.getElementById('confirm-additional-payment');
                            if (checkbox) {
                                checkbox.addEventListener('change', function() {
                                    submitButton.disabled = !this.checked;
                                    if (this.checked) {
                                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                                    } else {
                                        submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                                    }
                                });
                            }
                        }, 100);

                        // Still set a default amount
                        amountInput.value = packagePrice;
                    }
                    // Default case - handle any other scenarios
                    else {
                        // Set the amount to the package price
                        amountInput.value = packagePrice;

                        // Add a helpful message
                        const helpText = document.createElement('p');
                        helpText.className = 'text-xs text-blue-500 mt-1';
                        helpText.id = 'due-help-text';
                        helpText.textContent = `Payment amount: ${packagePrice}៛`;
                        amountInput.parentNode.appendChild(helpText);

                        // Ensure the submit button is enabled
                        const submitButton = document.querySelector('button[type="submit"]');
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    }
                } else {
                    // Clear the amount if no member is selected
                    amountInput.value = '';
                }
            });
        }
    });
</script>
{% endblock %}
