<!DOCTYPE html>
{% load static %}





<html lang="{{ LANGUAGE_CODE }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gym Management System - Legend Fitness</title>

    {% comment %} Include custom notification colors from settings {% endcomment %}
    {% include "partials/notification_colors.html" %}
    {% comment %} <meta http-equiv="refresh" content="5" > {% endcomment %}
     <!-- tailwind cdn  -->
     <script src="https://cdn.tailwindcss.com"></script>

     <!-- Bootstrap CSS -->
     <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

     <link rel="stylesheet"
     href="https://cdn.jsdelivr.net/npm/boxicons@latest/css/boxicons.min.css">

     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

     <!-- Khmer Fonts -->
     <link href="https://fonts.googleapis.com/css2?family=Hanuman:wght@100;300;400;700;900&display=swap" rel="stylesheet">

     <!-- Layout improvement styles -->
     <link rel="stylesheet" href="{% static 'css/breadcrumbs_new.css' %}">
     <link rel="stylesheet" href="{% static 'css/responsive-tables.css' %}">
     <link rel="stylesheet" href="{% static 'css/header.css' %}">
     <link rel="stylesheet" href="{% static 'css/footer.css' %}">
     <link rel="stylesheet" href="{% static 'css/notifications.css' %}">
     <link rel="stylesheet" href="{% static 'css/permission-manager.css' %}">

     {% block extra_css %}{% endblock %}

     <style>
        /* Custom styles for sidebar */
        .dashboardContainer {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        /* Improved sidebar styling */
        .dashBoardLinks ul li {
            margin-bottom: 0.5rem;
        }

        .dashBoardLinks a {
            transition: all 0.2s ease;
            border-radius: 0.5rem;
            padding: 0.75rem 1rem;
            font-weight: 500;
        }

        .dashBoardLinks > ul > li > a:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(3px);
        }

        /* Menu item with icon styling */
        .menu-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .menu-item i {
            font-size: 1.1rem;
            width: 1.5rem;
            text-align: center;
        }

        /* Submenu styling */
        .submenu {
            margin-left: 0.75rem;
            overflow: hidden;
            transition: all 0.15s ease;
        }

        .submenu ul {
            padding-left: 0.75rem;
        }

        .submenu a {
            padding: 0.6rem 0.75rem !important;
            margin-top: 0.25rem;
            border-left: 2px solid rgba(255, 255, 255, 0.2);
            background-color: transparent;
        }

        .submenu a:hover {
            background-color: rgba(29, 78, 216, 0.7) !important; /* blue-700 with opacity */
        }

        /* Ensure proper scrolling for menu items */
        .dashBoardLinks {
            scrollbar-width: thin;
            scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
        }

        .dashBoardLinks::-webkit-scrollbar {
            width: 4px;
        }

        .dashBoardLinks::-webkit-scrollbar-track {
            background: transparent;
        }

        .dashBoardLinks::-webkit-scrollbar-thumb {
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        .product-toggle {
            cursor: pointer;
        }

        /* Dropdown arrow styling */
        .product-toggle i.fa-chevron-down {
            font-size: 0.8rem;
            margin-right: 0.5rem;
            transition: transform 0.15s ease;
            opacity: 0.8;
        }

        /* Rotated arrow class */
        .fa-rotate-180 {
            transform: rotate(180deg);
        }

        /* Active menu item */
        .active-menu-item {
            background-color: rgba(255, 255, 255, 0.15) !important;
            border-left: 3px solid #fff !important;
        }

        /* Mobile sidebar toggle */
        .sidebar-toggle {
            position: fixed;
            top: 1rem;
            left: 0.75rem;
            z-index: 50;
            display: none; /* Hidden by default, shown only on mobile */
            background-color: #1e3a8a;
            color: white;
            padding: 0.5rem;
            border-radius: 0.375rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            width: 2.5rem;
            height: 2.5rem;
            align-items: center;
            justify-content: center;
        }

        /* Component section styling */
        .conponentSection {
            padding: 0;
            min-height: 100vh;
            margin-left: 16rem;
        }

        /* Desktop styles */
        @media (min-width: 641px) {
            .sidebar-toggle {
                display: none !important; /* Ensure it's hidden on desktop */
            }
        }

        /* Mobile responsive styles */
        @media (max-width: 640px) {
            .sidebar-toggle {
                display: flex; /* Show and use flexbox for centering on mobile */
                position: fixed;
                top: 1rem;
                left: 1rem;
                z-index: 50;
                background: rgba(30, 64, 175, 0.9);
                backdrop-filter: blur(10px);
                color: white;
                border: none;
                border-radius: 0.75rem;
                padding: 0.75rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                transition: all 0.3s ease;
            }

            .sidebar-toggle:hover {
                background: rgba(30, 64, 175, 1);
                transform: scale(1.05);
            }

            .dashboardContainer {
                width: 100%;
                max-width: 280px;
                backdrop-filter: blur(20px);
            }

            .main-content {
                margin-left: 0 !important;
            }

            .conponentSection {
                margin-left: 0 !important;
            }
        }

        /* Tablet responsive styles */
        @media (min-width: 641px) and (max-width: 1023px) {
            .dashboardContainer {
                width: 240px;
            }

            .conponentSection {
                margin-left: 15rem;
            }
        }

        /* Desktop styles */
        @media (min-width: 1024px) {
            .dashboardContainer {
                width: 256px;
            }

            .conponentSection {
                margin-left: 16rem;
            }
        }
     </style>

     <!-- Print-specific styles -->
     <style media="print">
        /* Hide the sidebar, header, and footer completely */
        .dashboardContainer, .app-header, .app-footer, .breadcrumbs-container {
            display: none !important;
        }

        /* Remove the margin for the main content */
        .main-content {
            margin-left: 0 !important;
            padding: 0 !important;
        }

        /* Remove the margin for component sections */
        .conponentSection {
            margin-left: 0 !important;
            padding: 0 !important;
        }

        /* Hide any elements with the no-print class */
        .no-print {
            display: none !important;
        }

        /* Reset background colors */
        body, .bg-gray-200 {
            background-color: white !important;
        }

        /* Ensure the print section is properly positioned */
        .print-section {
            position: absolute !important;
            left: 0 !important;
            top: 0 !important;
            width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
        }
     </style>
</head>
<body>
    {% include "sidebar.html" %}
    <!-- Global Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <div class="main-content sm:ml-0 transition-all duration-300">
        {% if request.path != '/product/pos/' and request.path != '/paypervisit/' and request.path != '/paypervisit/settings/' %}
            {% include "partials/messages.html" %}
        {% endif %}

        <!-- Header -->
        {% include "partials/header.html" %}

        <!-- Breadcrumbs -->
        {% include "partials/breadcrumbs.html" %}

        <!-- Main Content -->
        <div class="flex flex-col min-h-screen">
            {% block body %}
            {% endblock body %}
        </div>
    </div>

    <!-- Footer positioned outside main-content like header -->
    {% include "partials/footer.html" %}

    <!-- Mobile sidebar toggle button -->
    <button class="sidebar-toggle" id="sidebarToggle"><i class="fas fa-bars"></i></button>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <!-- Bootstrap Datepicker -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/css/bootstrap-datepicker.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-datepicker@1.9.0/dist/js/bootstrap-datepicker.min.js"></script>

    <!-- Layout improvement scripts -->
    <script src="{% static 'js/responsive-tables.js' %}"></script>
    <script src="{% static 'js/notifications.js' %}"></script>
    <script src="{% static 'js/currency-formatter.js' %}"></script>
    <script src="{% static 'js/permission-manager.js' %}"></script>

    <!-- Custom JavaScript for sidebar functionality -->
    <script>
        $(document).ready(function() {
            // Set active menu item based on current URL
            const currentUrl = window.location.pathname;
            $('.dashBoardLinks a').each(function() {
                const href = $(this).attr('href');
                if (href && currentUrl.includes(href) && href !== '#' && href.length > 1) {
                    // Add active styling to the current menu item
                    $(this).addClass('bg-white/20 shadow-lg');
                    $(this).find('i').addClass('text-white');

                    // If it's in a submenu, open the parent menu
                    const parentSubmenu = $(this).closest('.submenu');
                    if (parentSubmenu.length) {
                        parentSubmenu.show();
                        parentSubmenu.prev('.submenu-toggle').find('.fa-chevron-down').addClass('rotate-180');
                        parentSubmenu.prev('.submenu-toggle').addClass('bg-white/20');
                    }
                }
            });

            // Enhanced dropdown toggles with smooth animations
            $('.submenu-toggle').click(function(e) {
                e.preventDefault();
                const submenuId = $(this).attr('data-target');
                const chevronIcon = $(this).find('.fa-chevron-down');

                // Close other open submenus with smooth animation
                $('.submenu').not($(submenuId)).slideUp(200);
                $('.submenu-toggle').not($(this)).find('.fa-chevron-down').removeClass('rotate-180');
                $('.submenu-toggle').not($(this)).removeClass('bg-white/10');

                // Toggle current submenu with smooth animation
                $(submenuId).slideToggle(200);
                chevronIcon.toggleClass('rotate-180');
                $(this).toggleClass('bg-white/10');

                // Ensure the clicked item is visible with smooth scrolling
                const $this = $(this);
                setTimeout(function() {
                    const container = $('.dashBoardLinks');
                    const scrollPosition = $this.offset().top - container.offset().top + container.scrollTop();

                    if ($(submenuId).is(':visible')) {
                        // If submenu is now visible, make sure it's in view
                        container.animate({
                            scrollTop: scrollPosition - 20
                        }, 300);
                    }
                }, 220);
            });

            // Enhanced mobile sidebar toggle with improved animations
            $('#sidebarToggle').click(function() {
                $('.dashboardContainer').toggleClass('-translate-x-full');
                $(this).find('i').toggleClass('fa-bars fa-times');
            });

            // Close sidebar when clicking outside on mobile
            $(document).click(function(event) {
                if ($(window).width() <= 640) {
                    if (!$(event.target).closest('.dashboardContainer, #sidebarToggle').length) {
                        $('.dashboardContainer').addClass('-translate-x-full');
                        $('#sidebarToggle').find('i').removeClass('fa-times').addClass('fa-bars');
                    }
                }
            });

            // Handle window resize with improved responsive behavior
            $(window).resize(function() {
                if ($(window).width() > 640) {
                    $('.dashboardContainer').removeClass('-translate-x-full');
                    $('#sidebarToggle').find('i').removeClass('fa-times').addClass('fa-bars');
                }
            });

            // Add hover effects for better UX
            $('.sidebar-link, .submenu-link').hover(
                function() {
                    $(this).addClass('transform scale-105');
                },
                function() {
                    $(this).removeClass('transform scale-105');
                }
            );

            // Smooth scrolling for sidebar navigation
            $('.dashBoardLinks').on('scroll', function() {
                const scrollTop = $(this).scrollTop();
                if (scrollTop > 50) {
                    $(this).addClass('shadow-inner');
                } else {
                    $(this).removeClass('shadow-inner');
                }
            });
        });
    </script>

    {% block js %}{% endblock js %}
</body>
</html>