{% extends "../base.html" %}
{% load custom_filters %}
{% load permission_tags %}
{% load currency_filters %}

{% block extra_css %}
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .sales-table th:nth-child(2),
        .sales-table td:nth-child(2) {
            display: none; /* Hide Date column on mobile */
        }

        .sales-table th:nth-child(4),
        .sales-table td:nth-child(4) {
            display: none; /* Hide Payment Method column on mobile */
        }

        .delete-sale-btn {
            padding: 0.25rem 0.5rem !important;
            font-size: 0.625rem !important;
        }

        .delete-sale-btn i {
            margin-right: 0 !important;
        }

        .delete-sale-btn .btn-text {
            display: none;
        }
    }

    @media (max-width: 640px) {
        .sales-table th:nth-child(5),
        .sales-table td:nth-child(5) {
            display: none; /* Hide Sold By column on small mobile */
        }
    }
</style>
{% endblock %}

{% block body %}
<!-- Hidden CSRF token for JavaScript -->
<input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">

<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Sales Overview -->
        <div class="bg-white p-4 rounded shadow-md mb-4">

            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                    <a href="{% url 'product:pos' %}" class="bg-blue-900 text-white p-2 rounded-full hover:bg-blue-800 mr-3">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <h3 class="text-2xl font-bold">Sales History</h3>
                </div>
            </div>

            <!-- Filter Form -->
            <form method="GET" class="mb-4">
                <div class="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date" value="{{ start_date }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date" value="{{ end_date }}" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                        <select id="payment_method" name="payment_method" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Methods</option>
                            {% for value, label in payment_methods %}
                            <option value="{{ value }}" {% if payment_method_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <label for="cashier" class="block text-sm font-medium text-gray-700 mb-1">Cashier</label>
                        <select id="cashier" name="cashier" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">All Cashiers</option>
                            {% for cashier in cashiers %}
                            <option value="{{ cashier.id }}" {% if cashier_filter == cashier.id|stringformat:"s" %}selected{% endif %}>{{ cashier.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div>
                        <button type="submit" class="w-full bg-blue-900 text-white px-4 py-2 rounded-md hover:bg-blue-800 transition duration-200">
                            <i class="fas fa-filter mr-2"></i>Filter
                        </button>
                    </div>
                </div>
            </form>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded-lg border border-blue-200">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg mr-3">
                            <i class="fas fa-shopping-cart text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Sales</p>
                            <p class="text-xl font-bold text-blue-600">{{ page_obj.paginator.count }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg mr-3">
                            <i class="fas fa-dollar-sign text-green-600"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Total Revenue</p>
                            <p class="text-xl font-bold text-green-600">{{ total_amount|format_khr }}</p>
                        </div>
                    </div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg border border-purple-200">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg mr-3">
                            <i class="fas fa-chart-line text-purple-600"></i>
                        </div>
                        <div>
                            <p class="text-sm text-gray-600">Average Sale</p>
                            <p class="text-xl font-bold text-purple-600">
                                {% if sales.count > 0 %}
                                    {{ total_amount|div:sales.count|floatformat:0|format_khr }}
                                {% else %}
                                    0៛
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sales Table -->
            <div class="bg-white rounded-lg border shadow-sm overflow-hidden">
                <div class="relative overflow-x-auto">
                    <table class="w-full text-sm text-left sales-table">
                        <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                                <th scope="col" class="px-6 py-3">Transaction ID</th>
                                <th scope="col" class="px-6 py-3">Date</th>
                                <th scope="col" class="px-6 py-3">Total Amount</th>
                                <th scope="col" class="px-6 py-3">Payment Method</th>
                                <th scope="col" class="px-6 py-3">Sold By</th>
                                <th scope="col" class="px-6 py-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for sale in page_obj %}
                            <tr class="bg-white border-b hover:bg-gray-50">
                                <td class="px-6 py-4 font-medium text-gray-900">{{ sale.trxId }}</td>
                                <td class="px-6 py-4">{{ sale.date|date:"M d, Y H:i" }}</td>
                                <td class="px-6 py-4 font-semibold text-green-600">{{ sale.total_amount|format_khr }}</td>
                                <td class="px-6 py-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        {% if sale.payment_method == 'cash' %}bg-green-100 text-green-800
                                        {% elif sale.payment_method == 'bank_transfer' %}bg-blue-100 text-blue-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ sale.get_payment_method_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4">{{ sale.sold_by.username }}</td>
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-2">
                                        <button type="button" class="text-blue-600 hover:underline view-details" data-sale-id="{{ sale.id }}">View Details</button>
                                        {% has_permission user 'pos' 'full' as can_delete_sale %}
                                        {% if can_delete_sale %}
                                        <button
                                            type="button"
                                            class="bg-red-600 hover:bg-red-700 text-white font-medium py-1 px-3 rounded text-xs transition duration-200 delete-sale-btn"
                                            data-sale-id="{{ sale.id }}"
                                            data-sale-trx="{{ sale.trxId }}"
                                            data-sale-amount="{{ sale.total_amount|format_khr }}"
                                            data-sale-products="{{ sale.items.count }}"
                                            data-sale-soldby="{{ sale.sold_by.username|default:'Unknown' }}"
                                            title="Delete Sale">
                                            <i class="fas fa-trash mr-1"></i><span class="btn-text">Delete</span>
                                        </button>
                                        {% else %}
                                        <span class="text-gray-400 text-xs">No Access</span>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            <!-- Sale Details Row (Hidden by default) -->
                            <tr class="bg-blue-50 border-t-2 border-blue-200 hidden sale-details" id="details-{{ sale.id }}">
                                <td colspan="6" class="px-6 py-4">
                                    <div class="bg-white p-4 rounded-lg">
                                        <h4 class="font-semibold text-gray-800 mb-3">Sale Items</h4>
                                        <div class="overflow-x-auto">
                                            <table class="w-full text-sm">
                                                <thead class="bg-gray-100">
                                                    <tr>
                                                        <th class="px-3 py-2 text-left">Product</th>
                                                        <th class="px-3 py-2 text-center">Quantity</th>
                                                        <th class="px-3 py-2 text-right">Unit Price</th>
                                                        <th class="px-3 py-2 text-right">Total</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for item in sale.items.all %}
                                                    <tr class="border-b">
                                                        <td class="px-3 py-2">{{ item.product.name }}</td>
                                                        <td class="px-3 py-2 text-center">{{ item.quantity }}</td>
                                                        <td class="px-3 py-2 text-right">{{ item.price|format_khr }}</td>
                                                        <td class="px-3 py-2 text-right font-semibold">{{ item.quantity|mul:item.price|format_khr }}</td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                                <tfoot class="bg-gray-50">
                                                    <tr>
                                                        <td colspan="3" class="px-3 py-2 text-right font-semibold">Total:</td>
                                                        <td class="px-3 py-2 text-right font-bold text-green-600">{{ sale.total_amount|format_khr }}</td>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <i class="fas fa-shopping-cart text-4xl mb-2"></i>
                                    <p>No sales found for the selected period.</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

            <!-- Enhanced Pagination -->
            <div class="bg-white border-t border-gray-200">
                <!-- Mobile Pagination (Simple Previous/Next) -->
                <div class="flex justify-between items-center px-4 py-3 sm:hidden">
                    {% if page_obj.has_previous %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            <i class="fas fa-chevron-left mr-2"></i>Previous
                        </span>
                    {% endif %}

                    <span class="text-sm text-gray-700">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </a>
                    {% else %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-400 bg-gray-50 cursor-not-allowed">
                            Next<i class="fas fa-chevron-right ml-2"></i>
                        </span>
                    {% endif %}
                </div>

                <!-- Desktop Pagination (Full Controls) -->
                <div class="hidden sm:flex sm:flex-col lg:flex-row lg:items-center lg:justify-between px-6 py-4 space-y-4 lg:space-y-0">
                    <!-- Left side: Results info and items per page -->
                    <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                        <div>
                            <p class="text-sm text-gray-700">
                                Showing <span class="font-medium">{{ page_obj.start_index }}</span> to <span class="font-medium">{{ page_obj.end_index }}</span> of <span class="font-medium">{{ page_obj.paginator.count }}</span> entries
                            </p>
                        </div>
                        <div class="flex items-center">
                            <label for="items-per-page" class="text-sm text-gray-700 mr-2">Items per page:</label>
                            <select id="items-per-page" name="items_per_page" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500" onchange="changeItemsPerPage()">
                                <option value="5" {% if items_per_page == 5 %}selected{% endif %}>5</option>
                                <option value="10" {% if items_per_page == 10 %}selected{% endif %}>10</option>
                                <option value="25" {% if items_per_page == 25 %}selected{% endif %}>25</option>
                                <option value="50" {% if items_per_page == 50 %}selected{% endif %}>50</option>
                                <option value="100" {% if items_per_page == 100 %}selected{% endif %}>100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Right side: Pagination controls -->
                    {% if page_obj.has_other_pages %}
                    <div class="flex items-center space-x-2">
                        <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                            <!-- First Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page=1"
                                   class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="First page">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">First</span>
                                    <i class="fas fa-angle-double-left"></i>
                                </span>
                            {% endif %}

                            <!-- Previous Page -->
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.previous_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Previous</span>
                                    <i class="fas fa-chevron-left"></i>
                                </span>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span aria-current="page" class="relative z-10 inline-flex items-center bg-blue-900 px-4 py-2 text-sm font-semibold text-white focus:z-20 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-900">{{ num }}</span>
                                {% elif num > page_obj.number|add:"-3" and num < page_obj.number|add:"3" %}
                                    <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                       class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                {% elif num == 1 or num == page_obj.paginator.num_pages %}
                                    {% if num != page_obj.number %}
                                        <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ num }}"
                                           class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-900 ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:z-20 focus:outline-offset-0">{{ num }}</a>
                                    {% endif %}
                                {% elif num == page_obj.number|add:"-4" or num == page_obj.number|add:"4" %}
                                    <span class="relative inline-flex items-center px-4 py-2 text-sm font-semibold text-gray-700 ring-1 ring-inset ring-gray-300">...</span>
                                {% endif %}
                            {% endfor %}

                            <!-- Next Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.next_page_number }}"
                                   class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Next</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            {% endif %}

                            <!-- Last Page -->
                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET %}{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}{% endif %}page={{ page_obj.paginator.num_pages }}"
                                   class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                                   title="Last page">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            {% else %}
                                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-50 text-sm font-medium text-gray-300 cursor-not-allowed">
                                    <span class="sr-only">Last</span>
                                    <i class="fas fa-angle-double-right"></i>
                                </span>
                            {% endif %}
                        </nav>

                        <!-- Jump to Page -->
                        <div class="flex items-center ml-4">
                            <span class="text-sm text-gray-700 mr-2">Go to page:</span>
                            <input type="number" id="jump-to-page" min="1" max="{{ page_obj.paginator.num_pages }}" value="{{ page_obj.number }}"
                                   class="w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg p-1 focus:ring-blue-500 focus:border-blue-500">
                            <button id="jump-page-btn" class="ml-2 bg-blue-900 text-white px-2 py-1 rounded text-sm hover:bg-blue-800 transition duration-200">Go</button>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle sale details
        document.querySelectorAll('.view-details').forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const detailsRow = document.getElementById(`details-${saleId}`);

                if (detailsRow.classList.contains('hidden')) {
                    // Hide all other details first
                    document.querySelectorAll('.sale-details').forEach(row => {
                        row.classList.add('hidden');
                    });
                    // Show this one
                    detailsRow.classList.remove('hidden');
                    this.textContent = 'Hide Details';
                } else {
                    detailsRow.classList.add('hidden');
                    this.textContent = 'View Details';
                }
            });
        });

        // Items per page functionality
        const itemsPerPageSelect = document.getElementById('items-per-page');
        if (itemsPerPageSelect) {
            itemsPerPageSelect.addEventListener('change', function() {
                changeItemsPerPage();
            });
        }

        // Jump to page functionality
        const jumpToPageInput = document.getElementById('jump-to-page');
        const jumpPageBtn = document.getElementById('jump-page-btn');

        if (jumpToPageInput && jumpPageBtn) {
            jumpPageBtn.addEventListener('click', function() {
                const pageNum = parseInt(jumpToPageInput.value);
                const maxPage = parseInt(jumpToPageInput.getAttribute('max'));

                if (pageNum && pageNum > 0 && pageNum <= maxPage) {
                    // Build the URL with the current query parameters
                    let url = new URL(window.location.href);
                    let params = new URLSearchParams(url.search);

                    // Update or add the page parameter
                    params.set('page', pageNum);

                    // Redirect to the new URL
                    window.location.href = `${url.pathname}?${params.toString()}`;
                } else {
                    alert(`Please enter a valid page number between 1 and ${maxPage}`);
                    jumpToPageInput.value = "{{ page_obj.number }}";
                }
            });

            // Allow Enter key to trigger jump
            jumpToPageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    jumpPageBtn.click();
                }
            });
        }

        // Delete sale functionality with confirmation
        document.querySelectorAll('.delete-sale-btn').forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const saleTrx = this.getAttribute('data-sale-trx');
                const saleAmount = this.getAttribute('data-sale-amount');
                const saleProducts = this.getAttribute('data-sale-products');
                const soldBy = this.getAttribute('data-sale-soldby');

                // Use the product/products text based on the number of items
                const productText = saleProducts == 1 ? "product" : "products";

                // Create confirmation dialog
                const confirmMessage = `Are you sure you want to delete this sale?\n\n` +
                    `Transaction ID: ${saleTrx}\n` +
                    `Amount: ${saleAmount}\n` +
                    `Products: ${saleProducts} ${productText}\n` +
                    `Sold by: ${soldBy}\n\n` +
                    `This action cannot be undone. The sale amount will be deducted from gym funds.`;

                if (confirm(confirmMessage)) {
                    // Create a form and submit it to delete the sale
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/product/pos/history/delete/${saleId}/`;

                    // Add CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const csrfInput = document.createElement('input');
                    csrfInput.type = 'hidden';
                    csrfInput.name = 'csrfmiddlewaretoken';
                    csrfInput.value = csrfToken;
                    form.appendChild(csrfInput);

                    // Submit the form
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });
    });

    // Function to change items per page
    function changeItemsPerPage() {
        const itemsPerPage = document.getElementById('items-per-page').value;

        // Build the URL with current query parameters
        let url = new URL(window.location.href);
        let params = new URLSearchParams(url.search);

        // Update items per page and reset to page 1
        params.set('items_per_page', itemsPerPage);
        params.set('page', '1');

        // Redirect to the new URL
        window.location.href = `${url.pathname}?${params.toString()}`;
    }
</script>
{% endblock %}
