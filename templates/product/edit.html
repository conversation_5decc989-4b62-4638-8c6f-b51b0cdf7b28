{% extends "../base.html" %}
{% load static %}
{% load custom_filters %}
{% load currency_filters %}


{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Edit Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Edit Product: {{ product.name }}</h3>
            <form class="grid grid-cols-3 gap-4" method="post" enctype="multipart/form-data" action="{% url 'product:edit_product' product.id %}" id="product-form">
                {% csrf_token %}
                <!-- Col 1 -->
                <div class="grid gap-2">
                    <!-- Name -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Product Name" value="{{ product.name }}" required />
                    <!-- SKU -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="sku" type="text" placeholder="SKU (e.g., PW500)" value="{{ product.sku }}" required />
                    <!-- Category -->
                    <select name="category" class="border w-full p-4 leading-tight bg-slate-100">
                        <option value="">-- Select Category --</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if product.category.id == category.id %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <!-- Col 2 -->
                <div class="grid gap-2">
                    <!-- Cost Price -->
                    <div class="relative">
                        <input class="border w-full p-4 leading-tight bg-slate-100" id="cost_price" name="cost_price" type="text" placeholder="Cost Price (៛)" value="{{ product.cost_price }}" required />
                        <div class="absolute right-2 top-4 text-xs text-gray-500" id="cost_price_info"></div>
                    </div>
                    <!-- Retail Price -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" id="retail_price" name="retail_price" type="text" placeholder="Retail Price (៛)" value="{{ product.retail_price }}" required />
                    <!-- Quantity -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="quantity" type="number" placeholder="Current Quantity" value="{{ product.quantity }}" />
                </div>
                <!-- Col 3 -->
                <div class="grid gap-2">
                    <!-- Box Quantity -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" id="box_quantity" name="box_quantity" type="number" placeholder="Box Quantity" value="{{ product.box_quantity }}" />
                    <!-- Box Cost -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" id="box_cost" name="box_cost" type="text" placeholder="Box Cost (៛) (optional)" value="{{ product.box_cost|default:'' }}" />
                    <!-- Low Stock Threshold -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="low_stock_threshold" type="number" placeholder="Low Stock Alert Threshold" value="{{ product.low_stock_threshold }}" />
                    <!-- Active Status -->
                    <div class="border w-full p-4 leading-tight bg-slate-100 flex items-center">
                        <input id="is_active" name="is_active" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" {% if product.is_active %}checked{% endif %}>
                        <label for="is_active" class="ml-2 text-sm font-medium text-gray-900">Active (available for sale)</label>
                    </div>
                </div>
                <!-- Description - Full Width -->
                <div class="col-span-3">
                    <textarea class="border w-full p-4 leading-tight bg-slate-100" name="description" placeholder="Description" rows="3">{{ product.description }}</textarea>
                </div>
                <!-- Current Image and Upload - Full Width -->
                <div class="col-span-3">
                    {% if product.image %}
                    <div class="mb-4">
                        <p class="mb-2 text-sm font-medium">Current Image:</p>
                        <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-32 h-32 object-cover border" />
                    </div>
                    {% endif %}
                    <label class="block mb-2 text-sm font-medium">Update Product Image (optional)</label>
                    <input class="border w-full p-2 leading-tight bg-slate-100" name="image" type="file" accept="image/*" />
                </div>
                <!-- Submit Button - Full Width -->
                <div class="col-span-3 flex gap-4">
                    <button class="bg-blue-900 text-white font-bold py-2 px-4 flex-1" type="submit">Update Product</button>
                    <a href="{% url 'product:index' %}" class="bg-gray-500 text-white font-bold py-2 px-4 flex-1 text-center">Cancel</a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const boxQuantityInput = document.getElementById('box_quantity');
        const boxCostInput = document.getElementById('box_cost');
        const costPriceInput = document.getElementById('cost_price');
        const retailPriceInput = document.getElementById('retail_price');
        const costPriceInfo = document.getElementById('cost_price_info');
        const productForm = document.getElementById('product-form');

        // Setup formatted inputs
        setupFormattedInput(boxCostInput, 'khr');
        setupFormattedInput(costPriceInput, 'khr');
        setupFormattedInput(retailPriceInput, 'khr');

        // Function to get numeric value from formatted input
        function getNumericValue(input) {
            return parseFloat(input.value.replace(/[^\d.-]/g, '')) || 0;
        }

        // Function to calculate cost price from box cost and box quantity
        function calculateCostPrice() {
            const boxQuantity = parseFloat(boxQuantityInput.value) || 1;
            const boxCost = getNumericValue(boxCostInput);

            if (boxQuantity > 0 && boxCost > 0) {
                // Calculate cost price per unit
                const costPrice = boxCost / boxQuantity;

                // Update cost price input with formatted value
                costPriceInput.value = formatKHR(Math.round(costPrice)).replace('៛', '');

                // Show info about calculation
                costPriceInfo.textContent = `${formatKHR(boxCost)} ÷ ${boxQuantity} = ${formatKHR(Math.round(costPrice))}`;
            } else {
                costPriceInfo.textContent = '';
            }
        }

        // Function to calculate box cost from cost price and box quantity
        function calculateBoxCost() {
            const boxQuantity = parseFloat(boxQuantityInput.value) || 1;
            const costPrice = getNumericValue(costPriceInput);

            if (boxQuantity > 0 && costPrice > 0) {
                // Calculate box cost
                const boxCost = costPrice * boxQuantity;

                // Update box cost input with formatted value
                boxCostInput.value = formatKHR(Math.round(boxCost)).replace('៛', '');

                // Show info about calculation
                costPriceInfo.textContent = `${formatKHR(costPrice)} × ${boxQuantity} = ${formatKHR(Math.round(boxCost))}`;
            }
        }

        // Add event listeners
        boxQuantityInput.addEventListener('input', function() {
            if (boxCostInput.value) {
                calculateCostPrice();
            } else if (costPriceInput.value) {
                calculateBoxCost();
            }
        });

        boxCostInput.addEventListener('input', calculateCostPrice);

        costPriceInput.addEventListener('input', calculateBoxCost);

        // Initialize calculation info if both values are present
        if (boxCostInput.value && boxQuantityInput.value) {
            calculateCostPrice();
        }

        // Handle form submission to convert formatted values back to numeric values
        if (productForm) {
            productForm.addEventListener('submit', function(e) {
                // Convert formatted values back to numeric values
                boxCostInput.value = getNumericValue(boxCostInput);
                costPriceInput.value = getNumericValue(costPriceInput);
                retailPriceInput.value = getNumericValue(retailPriceInput);
            });
        }
    });
</script>
{% endblock %}
