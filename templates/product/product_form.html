{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}
{% load currency_filters %}


{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">
                {% if product %}
                    Edit Product: {{ product.name }}
                {% else %}
                    Create New Product
                {% endif %}
            </h3>
        </div>

        <!-- Product Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post" enctype="multipart/form-data" id="product-form">
                {% csrf_token %}

                <!-- Basic Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Basic Information</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label class="block text-sm font-medium mb-1">"Product Name <span class="text-red-500">*</span>"</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="name"
                                   type="text"
                                   placeholder="Enter product name"
                                   value="{{ product.name|default:'' }}"
                                   required />
                        </div>

                        <!-- SKU -->
                        <div>
                            <label class="block text-sm font-medium mb-1">"SKU <span class="text-red-500">*</span>"</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="sku"
                                   type="text"
                                   placeholder="SKU (e.g., PW500)"
                                   value="{{ product.sku|default:'' }}"
                                   {% if product %}readonly{% endif %}
                                   required />
                            <p class="text-xs text-gray-500 mt-1">Format: PW500, DR100, etc.</p>
                        </div>

                        <!-- Category -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Category</label>
                            <div class="flex items-center space-x-2">
                                <div class="relative flex-grow">
                                    <select id="category-select" name="category" class="border w-full p-4 leading-tight bg-slate-100">
                                        <option value="">-- Select Category --</option>
                                        {% for category in categories %}
                                        <option value="{{ category.id }}" {% if product.category.id == category.id %}selected{% endif %}>{{ category.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                {% if categories %}
                                <button type="button" id="add-category-btn" class="flex-shrink-0 h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-600 border border-gray-300 rounded" title="Add New Category"><i class="fas fa-plus"></i></button>
                                {% endif %}
                            </div>
                            {% if not categories %}
                            <div class="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
                                <p class="text-yellow-700 mb-2">No categories available. Please add a category first.</p>
                                <button type="button" id="add-category-btn" class="bg-blue-600 hover:bg-blue-700 text-white py-1 px-3 rounded text-xs"><i class="fas fa-plus mr-1"></i> Add New Category</button>
                            </div>
                            {% endif %}
                        </div>

                        <!-- Description -->
                        <div class="col-span-2">
                            <label class="block text-sm font-medium mb-1">Description</label>
                            <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                      name="description"
                                      rows="2"
                                      placeholder="Product description">{{ product.description|default:'' }}</textarea>
                        </div>
                    </div>
                </div>


                <!-- Inventory Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Inventory Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Quantity -->
                        <div>
                            <label class="block text-sm font-medium mb-1">"Quantity <span class="text-red-500">*</span>"</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="quantity"
                                   type="number"
                                   min="0"
                                   placeholder="Initial Quantity"
                                   value="{{ product.quantity|default:0 }}"
                                   required />
                        </div>



                        <!-- Box Quantity -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Box Quantity <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   id="box_quantity"
                                   name="box_quantity"
                                   type="number"
                                   min="1"
                                   placeholder="Enter units per box"
                                   value="{{ product.box_quantity|default:1 }}"
                                   required />
                            <p class="text-xs text-gray-500 mt-1">Number of units in one box (used for cost calculation)</p>
                        </div>

                        <!-- Box Cost -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Box Cost (៛) <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   id="box_cost"
                                   name="box_cost"
                                   type="text"
                                   placeholder="Enter box cost in Riel"
                                   value="{{ product.box_cost|default:'' }}"
                                   required />
                            <p class="text-xs text-gray-500 mt-1">Total cost for purchasing one box (required for cost calculation)</p>
                        </div>
                    </div>
                </div>

                <!-- Pricing Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Pricing Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Cost Price -->
                        <div class="relative">
                            <label class="block text-sm font-medium mb-1">Cost Price (៛) <span class="text-blue-600 text-xs">(Auto-calculated)</span></label>
                            <input class="border w-full p-4 leading-tight bg-gray-100 text-gray-700 cursor-not-allowed"
                                   id="cost_price"
                                   name="cost_price"
                                   type="text"
                                   placeholder="Will be calculated automatically"
                                   value="{{ product.cost_price|default:'' }}"
                                   readonly
                                   required />
                            <div class="absolute right-2 top-9 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded" id="cost_price_indicator">
                                <i class="fas fa-calculator mr-1"></i>Auto
                            </div>
                            <p class="text-xs text-blue-600 mt-1"><i class="fas fa-info-circle mr-1"></i>Automatically calculated: Box Cost ÷ Box Quantity</p>
                        </div>

                        <!-- Retail Price -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Retail Price (៛) <span class="text-red-500">*</span></label>
                            <input class="border w-full p-4 leading-tight bg-slate-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                   id="retail_price"
                                   name="retail_price"
                                   type="text"
                                   placeholder="Enter selling price per unit"
                                   value="{{ product.retail_price|default:'' }}"
                                   required />
                            <p class="text-xs text-gray-500 mt-1">Selling price per unit (should be ≥ cost price)</p>
                        </div>
                    </div>
                </div>

                <!-- Additional Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Additional Information</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Active Status -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Product Status</label>
                            <div class="border w-full p-4 leading-tight bg-slate-100 flex items-center">
                                <input id="is_active" name="is_active" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500" {% if product.is_active|default:True %}checked{% endif %}>
                                <label for="is_active" class="ml-2 text-sm font-medium text-gray-900">Active (available for sale)</label>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Inactive products won't appear in sales</p>
                        </div>

                        <!-- Image Upload -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Product Image</label>
                            <input class="border w-full p-3 leading-tight bg-slate-100"
                                   name="image"
                                   type="file"
                                   accept="image/*" />

                            {% if product.image %}
                            <div class="mt-2">
                                <img src="{{ product.image.url }}" alt="{{ product.name }}" class="w-16 h-16 object-cover border" />
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex flex-col sm:flex-row justify-between gap-3 mt-6 pt-4 border-t border-gray-200">
                    <a href="{% url 'product:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded transition duration-200 text-center">Cancel</a>
                    <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-3 px-6 rounded transition duration-200" type="submit">
                        {% if product %}Update{% else %}Create{% endif %} Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Category Modal -->
<div id="category-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md mx-4">
        <div class="p-4 border-b border-gray-200">
            <div class="flex justify-between items-center">
                <h3 class="text-lg font-semibold">Add New Category</h3>
                <button type="button" id="close-modal" class="text-gray-500 hover:text-gray-700"><i class="fas fa-times"></i></button>
            </div>
        </div>
        <div class="p-4">
            <form id="category-form">
                {% csrf_token %}
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">"Category Name <span class="text-red-500">*</span>"</label>
                    <input type="text" id="category-name" name="name" class="border w-full p-3 rounded" required>
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium mb-1">Description</label>
                    <textarea id="category-description" name="description" class="border w-full p-3 rounded" rows="3"></textarea>
                </div>
                <div class="flex justify-end space-x-2">
                    <button type="button" id="cancel-category" class="bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded">Cancel</button>
                    <button type="submit" id="save-category" class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded">Save Category</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const boxQuantityInput = document.getElementById('box_quantity');
        const boxCostInput = document.getElementById('box_cost');
        const costPriceInput = document.getElementById('cost_price');
        const retailPriceInput = document.getElementById('retail_price');

        // Setup formatted inputs
        setupFormattedInput(boxCostInput, 'khr');
        setupFormattedInput(costPriceInput, 'khr');
        setupFormattedInput(retailPriceInput, 'khr');

        // Function to get numeric value from formatted input
        function getNumericValue(input) {
            return parseFloat(input.value.replace(/[^\d.-]/g, '')) || 0;
        }

        // Function to calculate cost price from box cost and box quantity
        function calculateCostPrice() {
            const boxQuantity = parseFloat(boxQuantityInput.value) || 1;
            const boxCost = getNumericValue(boxCostInput);
            const costPriceIndicator = document.getElementById('cost_price_indicator');

            if (boxQuantity > 0 && boxCost > 0) {
                // Calculate cost price per unit
                const costPrice = boxCost / boxQuantity;

                // Update cost price input with formatted value
                costPriceInput.value = formatKHR(Math.round(costPrice)).replace('៛', '');

                // Update calculation indicator with formula
                if (costPriceIndicator) {
                    costPriceIndicator.title = `${formatKHR(boxCost)} ÷ ${boxQuantity} = ${formatKHR(Math.round(costPrice))}`;
                }

                // Add visual feedback with blue highlight for auto-calculation
                costPriceInput.classList.add('bg-blue-50', 'border-blue-300');
                setTimeout(() => {
                    costPriceInput.classList.remove('bg-blue-50', 'border-blue-300');
                }, 1000);
            } else {
                // Clear cost price if calculation is not possible
                costPriceInput.value = '';
            }
        }

        // Prevent manual editing of cost price field
        function preventCostPriceEdit(e) {
            e.preventDefault();
            return false;
        }

        // Function to validate retail price is greater than cost price
        function validatePrices() {
            const costPrice = getNumericValue(costPriceInput);
            const retailPrice = getNumericValue(retailPriceInput);

            if (retailPrice > 0 && costPrice > retailPrice) {
                retailPriceInput.setCustomValidity('Retail price should be greater than or equal to cost price');
                retailPriceInput.classList.add('border-red-500');
            } else {
                retailPriceInput.setCustomValidity('');
                retailPriceInput.classList.remove('border-red-500');
            }
        }

        // Function to validate required fields
        function validateRequiredFields() {
            const boxCost = getNumericValue(boxCostInput);
            const boxQuantity = parseFloat(boxQuantityInput.value) || 0;

            // Validate Box Cost
            if (boxCost <= 0) {
                boxCostInput.setCustomValidity('Box cost is required and must be greater than 0');
                boxCostInput.classList.add('border-red-500');
            } else {
                boxCostInput.setCustomValidity('');
                boxCostInput.classList.remove('border-red-500');
            }

            // Validate Box Quantity
            if (boxQuantity <= 0) {
                boxQuantityInput.setCustomValidity('Box quantity is required and must be greater than 0');
                boxQuantityInput.classList.add('border-red-500');
            } else {
                boxQuantityInput.setCustomValidity('');
                boxQuantityInput.classList.remove('border-red-500');
            }
        }

        // Add event listeners for price calculations
        if (boxQuantityInput && boxCostInput && costPriceInput) {
            boxQuantityInput.addEventListener('input', function() {
                validateRequiredFields();
                calculateCostPrice();
                validatePrices();
            });

            boxCostInput.addEventListener('input', function() {
                validateRequiredFields();
                calculateCostPrice();
                validatePrices();
            });

            // Prevent manual editing of cost price field
            costPriceInput.addEventListener('keydown', preventCostPriceEdit);
            costPriceInput.addEventListener('paste', preventCostPriceEdit);
            costPriceInput.addEventListener('input', preventCostPriceEdit);

            if (retailPriceInput) {
                retailPriceInput.addEventListener('input', validatePrices);
            }
        }

        // Initialize validation and calculation on page load
        if (boxCostInput && boxQuantityInput) {
            validateRequiredFields();
            // Always calculate cost price when page loads (for both new and existing products)
            calculateCostPrice();
        }

        // Handle form submission to convert formatted values back to numeric values
        const productForm = document.getElementById('product-form');
        if (productForm) {
            productForm.addEventListener('submit', function(e) {
                // Validate all fields before submission
                validateRequiredFields();
                validatePrices();

                // Check if there are any validation errors
                const invalidFields = productForm.querySelectorAll(':invalid');
                if (invalidFields.length > 0) {
                    e.preventDefault();

                    // Focus on the first invalid field
                    invalidFields[0].focus();

                    // Show error message
                    const firstError = invalidFields[0].validationMessage;
                    if (firstError) {
                        alert('Please fix the following error: ' + firstError);
                    }
                    return false;
                }

                // Convert formatted values back to numeric values
                boxCostInput.value = getNumericValue(boxCostInput);
                costPriceInput.value = getNumericValue(costPriceInput);
                retailPriceInput.value = getNumericValue(retailPriceInput);
            });
        }

        // Category Modal Functionality
        const categoryModal = document.getElementById('category-modal');
        const addCategoryBtn = document.getElementById('add-category-btn');
        const closeModalBtn = document.getElementById('close-modal');
        const cancelCategoryBtn = document.getElementById('cancel-category');
        const categoryForm = document.getElementById('category-form');
        const categorySelect = document.getElementById('category-select');

        // Function to show modal
        function showModal() {
            categoryModal.classList.remove('hidden');
            document.getElementById('category-name').focus();
        }

        // Function to hide modal
        function hideModal() {
            categoryModal.classList.add('hidden');
            categoryForm.reset();
        }

        // Show modal when add category button is clicked
        if (addCategoryBtn) {
            addCategoryBtn.addEventListener('click', showModal);
        }

        // Hide modal when close button is clicked
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', hideModal);
        }

        // Hide modal when cancel button is clicked
        if (cancelCategoryBtn) {
            cancelCategoryBtn.addEventListener('click', hideModal);
        }

        // Handle category form submission
        if (categoryForm) {
            categoryForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const formData = new FormData(categoryForm);
                const categoryName = formData.get('name');

                if (!categoryName) {
                    alert('Category name is required');
                    return;
                }

                // Get CSRF token
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                // Send AJAX request to create category
                fetch('{% url "product:create_category_ajax" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Add new category to select dropdown
                        const option = document.createElement('option');
                        option.value = data.category.id;
                        option.textContent = data.category.name;
                        option.selected = true;

                        // If this is the first category, clear the warning message
                        if (categorySelect.options.length === 1) {
                            // Remove the warning message if it exists
                            const warningMessage = document.querySelector('.bg-yellow-50');
                            if (warningMessage) {
                                warningMessage.remove();

                                // Add the + button next to the dropdown
                                const flexContainer = document.querySelector('.flex.items-center.space-x-2');
                                const button = document.createElement('button');
                                button.type = 'button';
                                button.id = 'add-category-btn';
                                button.className = 'flex-shrink-0 h-12 w-12 flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-600 border border-gray-300 rounded';
                                button.title = 'Add New Category';
                                button.innerHTML = '<i class="fas fa-plus"></i>';
                                button.addEventListener('click', showModal);
                                flexContainer.appendChild(button);
                            }
                        }

                        categorySelect.appendChild(option);

                        // Show success message
                        alert(data.message);

                        // Hide modal
                        hideModal();
                    } else {
                        alert(data.message || 'Error creating category');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while creating the category');
                });
            });
        }

        // Close modal when clicking outside
        categoryModal.addEventListener('click', function(e) {
            if (e.target === categoryModal) {
                hideModal();
            }
        });
    });
</script>
{% endblock %}
