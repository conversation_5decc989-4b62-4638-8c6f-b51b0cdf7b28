{% extends "../base.html" %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with Add Button -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Suppliers</h3>
            <a href="{% url 'product:create_supplier' %}" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                <i class="fas fa-plus mr-2"></i> Add New Supplier
            </a>
        </div>

        <!-- Suppliers List -->
        <div class="bg-white p-4 rounded shadow-md">
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Phone</th>
                            <th scope="col" class="px-6 py-3">Telegram/Messenger</th>
                            <th scope="col" class="px-6 py-3">Address</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ supplier.name }}</td>
                            <td class="px-6 py-4">{{ supplier.phone|default:"-" }}</td>
                            <td class="px-6 py-4">{{ supplier.telegram|default:"-" }}</td>
                            <td class="px-6 py-4">{{ supplier.address|default:"-" }}</td>
                            <td class="px-6 py-4">
                                <a href="{% url 'product:edit_supplier' supplier.id %}" class="text-blue-600 hover:underline mr-2">Edit</a>
                                <a href="{% url 'product:delete_supplier' supplier.id %}" class="text-red-600 hover:underline" onclick="return confirm('Are you sure you want to delete this supplier?')">Delete</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="5" class="px-6 py-4 text-center">No suppliers found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
