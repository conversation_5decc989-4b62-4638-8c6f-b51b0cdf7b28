{% extends 'base.html' %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">
                {% if supplier %}
                    Edit Supplier: {{ supplier.name }}
                {% else %}
                    Create New Supplier
                {% endif %}
            </h3>
        </div>

        <!-- Supplier Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post">
                {% csrf_token %}

                <!-- Supplier Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">Supplier Information</h4>
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Name -->
                        <div>
                            <label class="block text-sm font-medium mb-1">"Supplier Name <span class="text-red-500">*</span>"</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="name"
                                   type="text"
                                   placeholder="Enter supplier name"
                                   value="{{ supplier.name|default:'' }}"
                                   required />
                        </div>

                        <!-- Phone -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Phone Number</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="phone"
                                   type="text"
                                   placeholder="Phone Number"
                                   value="{{ supplier.phone|default:'' }}" />
                        </div>

                        <!-- Telegram/Messenger -->
                        <div>
                            <label class="block text-sm font-medium mb-1">Telegram/Messenger</label>
                            <input class="border w-full p-4 leading-tight bg-slate-100"
                                   name="telegram"
                                   type="text"
                                   placeholder="Telegram/Messenger"
                                   value="{{ supplier.telegram|default:'' }}" />
                        </div>

                        <!-- Address - Full Width -->
                        <div class="col-span-2">
                            <label class="block text-sm font-medium mb-1">Address</label>
                            <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                      name="address"
                                      placeholder="Address"
                                      rows="3">{{ supplier.address|default:'' }}</textarea>
                        </div>

                        <!-- Note - Full Width -->
                        <div class="col-span-2">
                            <label class="block text-sm font-medium mb-1">Note</label>
                            <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                      name="note"
                                      placeholder="Additional notes about the supplier"
                                      rows="3">{{ supplier.note|default:'' }}</textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-between mt-6 pt-4 border-t border-gray-200">
                    <a href="{% url 'product:suppliers' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">Cancel</a>
                    <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200" type="submit">
                        {% if supplier %}Update{% else %}Create{% endif %} Supplier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
