{% extends 'base.html' %}
{% load custom_filters %}
{% load i18n %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">{% trans "Create New Purchase" %}</h3>
        </div>

        <!-- Purchase Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post" id="purchaseForm">
                {% csrf_token %}

                <!-- Purchase Information Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">{% trans "Purchase Information" %}</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Payment Method -->
                        <div>
                            <label class="block text-sm font-medium mb-1">{% trans "Payment Method" %} <span class="text-red-500">*</span></label>
                            <select name="payment_method" class="border border-gray-300 p-3 bg-white w-full rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                <option value="cash">{% trans "Cash" %}</option>
                                <option value="bank">{% trans "Bank Transfer" %}</option>
                                <option value="other">{% trans "Other" %}</option>
                            </select>
                        </div>
                        <!-- Notes -->
                        <div>
                            <label class="block text-sm font-medium mb-1">{% trans "Notes" %} ({% trans "Optional" %})</label>
                            <textarea name="notes" class="border border-gray-300 w-full p-3 leading-tight bg-white rounded-md focus:ring-blue-500 focus:border-blue-500" rows="2" placeholder="{% trans 'Enter any additional notes about this purchase...' %}"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Purchase Items Section -->
                <div class="mb-6">
                    <h4 class="text-lg font-semibold mb-3 pb-2 border-b border-gray-200">{% trans "Purchase Items" %}</h4>

                    <div id="purchaseItems" class="space-y-4">
                        <!-- First purchase item -->
                        <div class="purchase-item bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <!-- Product Selection -->
                                <div class="lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Product" %} <span class="text-red-500">*</span></label>
                                    <select name="product[]" class="border border-gray-300 p-3 bg-white product-select w-full rounded-md focus:ring-blue-500 focus:border-blue-500" required>
                                        <option value="">-- {% trans "Select Product" %} --</option>
                                        {% for product in products %}
                                        <option value="{{ product.id }}"
                                                data-box-quantity="{{ product.box_quantity }}"
                                                data-box-cost="{{ product.box_cost|default:'0' }}"
                                                data-cost-price="{{ product.cost_price }}">{{ product.name }} ({{ product.sku }})</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Supplier Selection -->
                                <div class="lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Supplier" %}</label>
                                    <select name="supplier[]" class="border border-gray-300 p-3 bg-white supplier-select w-full rounded-md focus:ring-blue-500 focus:border-blue-500">
                                        <option value="">-- {% trans "Select Supplier" %} --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Quantity -->
                                <div class="lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Quantity" %} <span class="text-red-500">*</span></label>
                                    <input type="number" name="quantity[]" placeholder="{% trans 'Quantity' %}" class="border border-gray-300 p-3 bg-white quantity-input w-full rounded-md focus:ring-blue-500 focus:border-blue-500" min="1" value="1" required>
                                    <div class="box-info hidden mt-1">
                                        <span class="text-xs text-gray-500 box-quantity-display">1 box = ? units</span>
                                    </div>
                                </div>

                                <!-- Box Cost -->
                                <div class="lg:col-span-1">
                                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Box Cost" %} <span class="text-red-500">*</span></label>
                                    <input type="number" name="cost[]" placeholder="{% trans 'Box Cost (៛)' %}" step="0.01" class="border border-gray-300 p-3 bg-gray-50 cost-input w-full rounded-md" readonly required>
                                    <input type="hidden" name="is_box_purchase[]" value="0" class="box-purchase-hidden">
                                    <div class="mt-1">
                                        <span class="text-xs text-gray-500 purchase-type-label">{% trans "Boxes" %}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Item Management Buttons -->
                    <div class="mt-4 flex flex-col sm:flex-row gap-3 justify-between items-start sm:items-center">
                        <div class="flex flex-wrap gap-2">
                            <button type="button" id="addItemBtn" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-200 flex items-center space-x-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                <span>{% trans "Add Another Item" %}</span>
                            </button>
                        </div>
                        <div class="text-sm text-gray-500">
                            <span id="itemCount">1</span> {% trans "item(s) selected" %}
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-between mt-6 pt-4 border-t border-gray-200">
                    <a href="{% url 'product:purchases' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">{% trans "Cancel" %}</a>
                    <button type="submit" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200">{% trans "Add Purchase" %}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let itemCounter = 1;

        // Initialize the first row
        initializePurchaseRow(document.querySelector('.purchase-item'));
        updateItemCount();

        // Form validation
        const form = document.getElementById('purchaseForm');
        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });

        // Add new purchase item
        document.getElementById('addItemBtn').addEventListener('click', function() {
            const itemsContainer = document.getElementById('purchaseItems');
            const firstItem = document.querySelector('.purchase-item');
            const newItem = firstItem.cloneNode(true);

            itemCounter++;

            // Clear form values
            clearItemForm(newItem);

            // Add remove button to new item
            addRemoveButton(newItem);

            // Initialize the new row
            initializePurchaseRow(newItem);

            itemsContainer.appendChild(newItem);
            updateItemCount();
        });

        // Clear form values for new item
        function clearItemForm(item) {
            // Clear inputs
            item.querySelectorAll('input:not([type="hidden"])').forEach(input => {
                if (input.classList.contains('quantity-input')) {
                    input.value = '1';
                } else {
                    input.value = '';
                }
            });

            // Reset selects
            item.querySelectorAll('select').forEach(select => {
                select.selectedIndex = 0;
            });

            // Reset hidden values and labels
            item.querySelector('.box-purchase-hidden').value = '0';
            item.querySelector('.purchase-type-label').textContent = '{% trans "Units" %}';

            // Hide box info
            item.querySelector('.box-info').classList.add('hidden');
        }

        // Add remove button to item
        function addRemoveButton(item) {
            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs transition duration-200';
            removeBtn.innerHTML = '×';
            removeBtn.title = 'Remove this item';

            removeBtn.addEventListener('click', function() {
                item.remove();
                itemCounter--;
                updateItemCount();
            });

            // Make item relative positioned and add button
            item.style.position = 'relative';
            item.appendChild(removeBtn);
        }

        // Update item count
        function updateItemCount() {
            const items = document.querySelectorAll('.purchase-item');
            document.getElementById('itemCount').textContent = items.length;
        }

        // Form validation
        function validateForm() {
            const items = document.querySelectorAll('.purchase-item');

            for (let i = 0; i < items.length; i++) {
                const item = items[i];
                const productSelect = item.querySelector('.product-select');
                const quantityInput = item.querySelector('.quantity-input');
                const costInput = item.querySelector('.cost-input');

                // Check product selection
                if (!productSelect.value) {
                    alert(`Please select a product for item ${i + 1}`);
                    productSelect.focus();
                    return false;
                }

                // Check quantity
                const quantity = parseInt(quantityInput.value);
                if (!quantity || quantity < 1) {
                    alert(`Please enter a valid quantity for item ${i + 1}`);
                    quantityInput.focus();
                    return false;
                }

                // Check cost
                const cost = parseFloat(costInput.value);
                if (!cost || cost <= 0) {
                    alert(`Box cost is required for item ${i + 1}`);
                    return false;
                }
            }
            return true;
        }

        // Initialize purchase row
        function initializePurchaseRow(row) {
            const productSelect = row.querySelector('.product-select');

            productSelect.addEventListener('change', function() {
                const costInput = row.querySelector('.cost-input');
                const boxInfo = row.querySelector('.box-info');
                const boxQuantityDisplay = row.querySelector('.box-quantity-display');
                const purchaseTypeLabel = row.querySelector('.purchase-type-label');
                const boxPurchaseHidden = row.querySelector('.box-purchase-hidden');

                if (this.value) {
                    const option = this.options[this.selectedIndex];
                    const boxQuantity = parseInt(option.getAttribute('data-box-quantity')) || 1;
                    const boxCost = parseFloat(option.getAttribute('data-box-cost')) || 0;
                    const costPrice = parseFloat(option.getAttribute('data-cost-price')) || 0;

                    // Determine if this is a box purchase (products with box_quantity > 1)
                    if (boxQuantity > 1) {
                        // This is a box purchase
                        boxPurchaseHidden.value = '1';
                        purchaseTypeLabel.textContent = '{% trans "Boxes" %}';
                        boxQuantityDisplay.textContent = `1 {% trans "box" %} = ${boxQuantity} {% trans "units" %}`;
                        boxInfo.classList.remove('hidden');

                        // Use box_cost if available, otherwise calculate from unit cost
                        if (boxCost > 0) {
                            // Use the predefined box cost
                            costInput.value = boxCost;
                            console.log(`Using predefined box cost: ${boxCost} for product with ${boxQuantity} units per box`);
                        } else {
                            // Calculate box cost from unit cost price
                            const calculatedBoxCost = costPrice * boxQuantity;
                            costInput.value = calculatedBoxCost;
                            console.log(`Calculated box cost: ${calculatedBoxCost} (${costPrice} × ${boxQuantity}) for product`);
                        }
                    } else {
                        // This is a unit purchase
                        costInput.value = costPrice;
                        boxPurchaseHidden.value = '0';
                        purchaseTypeLabel.textContent = '{% trans "Units" %}';
                        boxInfo.classList.add('hidden');
                        console.log(`Using unit cost: ${costPrice} for single unit product`);
                    }
                } else {
                    // Reset when no product is selected
                    costInput.value = '';
                    boxInfo.classList.add('hidden');
                    purchaseTypeLabel.textContent = '{% trans "Units" %}';
                    boxPurchaseHidden.value = '0';
                }
            });
        }
    });
</script>
{% endblock %}
