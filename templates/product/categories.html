{% extends "../base.html" %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Entry Form starts  -->
        <div class="formSection bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">New Category Form</h3>
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-1 gap-4">
                    <!-- Name -->
                    <input class="border w-full p-4 leading-tight bg-slate-100" name="name" type="text" placeholder="Category Name" required />
                    <!-- Description -->
                    <textarea class="border w-full p-4 leading-tight bg-slate-100" name="description" placeholder="Description" rows="3"></textarea>
                    <button class="bg-blue-900 text-white font-bold py-2 px-4 w-full" type="submit">Add New Category</button>
                </div>
            </form>
        </div>

        <!-- Categories List -->
        <div class="bg-white p-4 rounded shadow-md">
            <h3 class="text-2xl font-bold mb-4">Categories</h3>
            <div class="relative overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Name</th>
                            <th scope="col" class="px-6 py-3">Description</th>
                            <th scope="col" class="px-6 py-3">Products Count</th>
                            <th scope="col" class="px-6 py-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr class="bg-white border">
                            <td class="px-6 py-4">{{ category.name }}</td>
                            <td class="px-6 py-4">{{ category.description|default:"-" }}</td>
                            <td class="px-6 py-4">{{ category.products.count }}</td>
                            <td class="px-6 py-4">
                                <a href="{% url 'product:edit_category' category.id %}" class="text-blue-600 hover:underline mr-2">Edit</a>
                                <a href="{% url 'product:delete_category' category.id %}" class="text-red-600 hover:underline" onclick="return confirm('Are you sure you want to delete this category?')">Delete</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr class="bg-white border">
                            <td colspan="4" class="px-6 py-4 text-center">No categories found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}
