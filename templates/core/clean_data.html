{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<!-- component sections starts  -->
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold mb-4">Data Cleaning Tools</h3>
            <p class="text-red-600 font-bold mb-4">Warning: These actions are irreversible. Use with caution!</p>

            <!-- Current Data Stats -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-blue-50 p-4 rounded shadow">
                    <h4 class="text-lg font-semibold mb-2">Member Data</h4>
                    <p><span class="font-medium">Members:</span> {{ data_counts.members }}</p>
                    <p><span class="font-medium">Packages:</span> {{ data_counts.packages }}</p>
                    <p><span class="font-medium">Payments:</span> {{ data_counts.payments }}</p>
                </div>
                <div class="bg-green-50 p-4 rounded shadow">
                    <h4 class="text-lg font-semibold mb-2">Product Data</h4>
                    <p><span class="font-medium">Products:</span> {{ data_counts.products }}</p>
                    <p><span class="font-medium">Categories:</span> {{ data_counts.categories }}</p>
                    <p><span class="font-medium">Suppliers:</span> {{ data_counts.suppliers }}</p>
                    <p><span class="font-medium">Purchases:</span> {{ data_counts.purchases }}</p>
                    <p><span class="font-medium">Sales:</span> {{ data_counts.sales }}</p>
                </div>
                <div class="bg-yellow-50 p-4 rounded shadow">
                    <h4 class="text-lg font-semibold mb-2">Other Data</h4>
                    <p><span class="font-medium">Pay-per-visit Records:</span> {{ data_counts.paypervisits }}</p>
                    <p><span class="font-medium">Salary Payments:</span> {{ data_counts.salary_payments }}</p>
                    <p><span class="font-medium">Bills:</span> {{ data_counts.bills }}</p>
                    <p><span class="font-medium">Non-admin Users:</span> {{ data_counts.users }}</p>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-yellow-50 p-4 rounded shadow-md mb-6">
                <h4 class="text-lg font-semibold mb-2">Instructions</h4>
                <p>Use these tools to clean data from the database. This is useful for:</p>
                <ul class="list-disc pl-5 mt-2">
                    <li>Fixing foreign key constraint errors</li>
                    <li>Starting fresh with empty tables</li>
                    <li>Removing test data before going to production</li>
                </ul>
            </div>

            <!-- Clean Transaction Tables -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Clean Legacy Transaction Tables</h4>
                <p class="mb-4">This will clean the transaction_payrollrecord and transaction_transaction tables that might be causing foreign key constraint errors.</p>
                <div class="bg-yellow-50 p-3 rounded mb-4">
                    <p class="text-sm text-yellow-800"><i class="fas fa-exclamation-triangle mr-1"></i> These tables are legacy tables that are no longer used by the application but may still exist in the database.</p>
                </div>
                <form method="post" onsubmit="return confirm('Are you sure you want to clean the transaction tables? This action cannot be undone.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_transaction_table">
                    <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Clean Transaction Tables</button>
                </form>
            </div>

            <!-- Member Data Section -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Member Data</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Clean Members -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Members</h5>
                        <p class="mb-4 text-sm">This will delete all members from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL members? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_members">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Members ({{ data_counts.members }})</button>
                        </form>
                    </div>

                    <!-- Clean Packages -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Packages</h5>
                        <p class="mb-4 text-sm">This will delete all membership packages from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL packages? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_packages">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Packages ({{ data_counts.packages }})</button>
                        </form>
                    </div>

                    <!-- Clean Payments -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Payments</h5>
                        <p class="mb-4 text-sm">This will delete all payment records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL payments? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_payments">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Payments ({{ data_counts.payments }})</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Product Data Section -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Product Data</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Clean Products -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Products</h5>
                        <p class="mb-4 text-sm">This will delete all products from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL products? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_products">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Products ({{ data_counts.products }})</button>
                        </form>
                    </div>

                    <!-- Clean Categories -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Categories</h5>
                        <p class="mb-4 text-sm">This will delete all product categories from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL categories? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_categories">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Categories ({{ data_counts.categories }})</button>
                        </form>
                    </div>

                    <!-- Clean Suppliers -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Suppliers</h5>
                        <p class="mb-4 text-sm">This will delete all suppliers from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL suppliers? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_suppliers">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Suppliers ({{ data_counts.suppliers }})</button>
                        </form>
                    </div>

                    <!-- Clean Purchases -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Purchases</h5>
                        <p class="mb-4 text-sm">This will delete all purchase records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL purchases? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_purchases">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Purchases ({{ data_counts.purchases }})</button>
                        </form>
                    </div>

                    <!-- Clean Sales -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Sales</h5>
                        <p class="mb-4 text-sm">This will delete all sale records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL sales? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_sales">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Sales ({{ data_counts.sales }})</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Other Data Section -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Other Data</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Clean Pay-per-visit -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Pay-per-visit Records</h5>
                        <p class="mb-4 text-sm">This will delete all pay-per-visit records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL pay-per-visit records? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_paypervisit">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Pay-per-visit Records ({{ data_counts.paypervisits }})</button>
                        </form>
                    </div>

                    <!-- Clean Salary Payments -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Salary Payments</h5>
                        <p class="mb-4 text-sm">This will delete all salary payment records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL salary payments? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_salary_payments">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Salary Payments ({{ data_counts.salary_payments }})</button>
                        </form>
                    </div>

                    <!-- Clean Bills -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Bills</h5>
                        <p class="mb-4 text-sm">This will delete all bill records from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL bills? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_bills">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Bills ({{ data_counts.bills }})</button>
                        </form>
                    </div>

                    <!-- Clean Users -->
                    <div class="bg-white border border-gray-200 p-4 rounded shadow-sm">
                        <h5 class="text-md font-semibold mb-2">Delete All Non-Admin Users</h5>
                        <p class="mb-4 text-sm">This will delete all non-admin users from the database.</p>
                        <form method="post" onsubmit="return confirm('Are you sure you want to delete ALL non-admin users? This action cannot be undone.')">
                            {% csrf_token %}
                            <input type="hidden" name="action" value="clean_users">
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Delete All Non-Admin Users ({{ data_counts.users }})</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Clean All Data -->
            <div class="bg-white border border-gray-200 p-4 rounded shadow-md mb-4">
                <h4 class="text-lg font-semibold mb-2">Clean All Data</h4>
                <p class="mb-4">This will delete all data from all tables. Use this to start fresh with an empty database.</p>
                <div class="bg-red-50 p-3 rounded mb-4">
                    <p class="text-sm text-red-800"><i class="fas fa-exclamation-triangle mr-1"></i>
                        <strong>EXTREME CAUTION:</strong> This will delete ALL data from ALL tables. This action cannot be undone."</p>
                </div>
                <form method="post" onsubmit="return confirm('Are you ABSOLUTELY SURE you want to clean ALL data from ALL tables? This action CANNOT be undone and will delete EVERYTHING except admin users.')">
                    {% csrf_token %}
                    <input type="hidden" name="action" value="clean_all">
                    <button type="submit" class="bg-red-700 hover:bg-red-800 text-white font-bold py-3 px-6 rounded">Clean All Data</button>
                </form>
            </div>

            <!-- Back Button -->
            <div class="mt-6">
                <a href="{% url 'adminDashboard' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">Back to Dashboard</a>
            </div>
        </div>
    </div>
</div>
{% endblock body %}
