{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">Create Salary Payment</h3>
        </div>

        <!-- Salary Payment Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-3 gap-4">
                    <!-- Employee Selection -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employee*</label>
                        <select name="employee" id="employee-select" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="">Select Employee</option>
                            {% for employee in employees %}
                            <option value="{{ employee.id }}" data-salary="{{ employee.salary }}">{{ employee.name }} ({{ employee.role|title }})</option>
                            {% endfor %}
                        </select>
                    </div>

                    <!-- Month & Year -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Month & Year*</label>
                        <input type="month" name="month" class="border w-full p-4 leading-tight bg-slate-100"
                               value="{{ current_month }}" required>
                    </div>

                    <!-- Employment Type -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Employment Type*</label>
                        <select name="employment_type" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="full_time">Full-time</option>
                            <option value="part_time">Part-time</option>
                        </select>
                    </div>

                    <!-- Base Salary -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Base Salary (៛)*</label>
                        <input type="number" name="base_salary" id="base-salary" class="border w-full p-4 leading-tight bg-slate-100" required>
                    </div>

                    <!-- Bonus -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Bonus (៛)</label>
                        <input type="number" name="bonus" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Deduction -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Deduction (៛)</label>
                        <input type="number" name="deduction" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Overtime Hours -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Overtime Hours</label>
                        <input type="number" name="overtime_hours" id="overtime-hours" class="border w-full p-4 leading-tight bg-slate-100" value="0">
                    </div>

                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Payment Method*</label>
                        <select name="payment_method" class="border w-full p-4 leading-tight bg-slate-100" required>
                            <option value="cash">Cash</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <!-- Notes -->
                    <div class="col-span-3">
                        <label class="block text-sm font-medium mb-1">Notes</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100"
                                  name="notes"
                                  rows="2"
                                  placeholder="Additional notes"></textarea>
                    </div>

                    <!-- Submit Button -->
                    <div class="col-span-3 flex justify-between mt-6 pt-4 border-t border-gray-200">
                        <a href="{% url 'payroll:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">Cancel</a>
                        <button class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200" type="submit">Create Salary Record</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Employee salary auto-fill
        const employeeSelect = document.getElementById('employee-select');
        const baseSalaryInput = document.getElementById('base-salary');

        if (employeeSelect) {
            employeeSelect.addEventListener('change', function() {
                const selectedOption = employeeSelect.options[employeeSelect.selectedIndex];
                const salary = selectedOption.getAttribute('data-salary');

                if (salary) {
                    baseSalaryInput.value = salary;
                } else {
                    baseSalaryInput.value = '';
                }
            });
        }
    });
</script>
{% endblock %}
