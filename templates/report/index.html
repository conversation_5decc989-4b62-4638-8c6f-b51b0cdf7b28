{% extends "../base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h3 class="text-2xl font-bold">{{ title }}</h3>
            <p class="text-gray-600">Access comprehensive reports and analytics for your business</p>
        </div>

        <!-- Report Categories -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <!-- Sales Reports -->
            <div class="bg-white p-4 rounded shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-100 p-3 rounded-full mr-3">
                        <i class="fa-solid fa-chart-line text-blue-600 text-xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold">Sales Reports</h4>
                </div>
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'report:sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Sales Overview
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:daily_sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Daily Sales
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:weekly_sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Weekly Sales
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:monthly_sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Monthly Sales
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:yearly_sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Yearly Sales
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Product Reports -->
            <div class="bg-white p-4 rounded shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-green-100 p-3 rounded-full mr-3">
                        <i class="fa-solid fa-box text-green-600 text-xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold">Product Reports</h4>
                </div>
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'report:product_performance_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Product Performance
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:category_performance_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Category Performance
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:inventory_turnover_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Inventory Turnover
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:profit_margin_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Profit Margin Analysis
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Other Reports -->
            <div class="bg-white p-4 rounded shadow-md">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-100 p-3 rounded-full mr-3">
                        <i class="fa-solid fa-users text-purple-600 text-xl"></i>
                    </div>
                    <h4 class="text-xl font-semibold">Other Reports</h4>
                </div>
                <ul class="space-y-2">
                    <li>
                        <a href="{% url 'report:employee_sales_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Employee Sales
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'report:payment_method_report' %}" class="text-blue-600 hover:underline flex items-center">
                            <i class="fa-solid fa-angle-right mr-2"></i> Payment Methods
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="bg-white p-4 rounded shadow-md mb-4">
            <h4 class="text-xl font-semibold mb-4">Quick Stats</h4>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="bg-blue-100 p-4 rounded-lg">
                    <p class="text-sm text-blue-600">Today's Sales</p>
                    <p class="text-2xl font-bold" id="today-sales">Loading...</p>
                </div>
                <div class="bg-green-100 p-4 rounded-lg">
                    <p class="text-sm text-green-600">This Week</p>
                    <p class="text-2xl font-bold" id="week-sales">Loading...</p>
                </div>
                <div class="bg-yellow-100 p-4 rounded-lg">
                    <p class="text-sm text-yellow-600">This Month</p>
                    <p class="text-2xl font-bold" id="month-sales">Loading...</p>
                </div>
                <div class="bg-purple-100 p-4 rounded-lg">
                    <p class="text-sm text-purple-600">Total Products</p>
                    <p class="text-2xl font-bold" id="total-products">Loading...</p>
                </div>
            </div>
        </div>

        <!-- Recent Sales -->
        <div class="bg-white p-4 rounded shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-xl font-semibold">Recent Sales</h4>
                <a href="{% url 'report:sales_report' %}" class="text-blue-600 hover:underline">View All</a>
            </div>
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-sm uppercase bg-blue-900 text-gray-50"><tr>
                            <th scope="col" class="px-6 py-3">Transaction ID</th>
                            <th scope="col" class="px-6 py-3">Date</th>
                            <th scope="col" class="px-6 py-3">Total Amount</th>
                            <th scope="col" class="px-6 py-3">Payment Method</th>
                            <th scope="col" class="px-6 py-3">Sold By</th>
                        </tr>
                    </thead>
                    <tbody id="recent-sales-table">
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center">Loading recent sales...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch quick stats
        fetch('/report/api/quick-stats/')
            .then(response => response.json())
            .then(data => {
                document.getElementById('today-sales').textContent = data.today_sales;
                document.getElementById('week-sales').textContent = data.week_sales;
                document.getElementById('month-sales').textContent = data.month_sales;
                document.getElementById('total-products').textContent = data.total_products;
            })
            .catch(error => {
                console.error('Error fetching quick stats:', error);
                document.getElementById('today-sales').textContent = 'Error loading data';
                document.getElementById('week-sales').textContent = 'Error loading data';
                document.getElementById('month-sales').textContent = 'Error loading data';
                document.getElementById('total-products').textContent = 'Error loading data';
            });

        // Fetch recent sales
        fetch('/report/api/recent-sales/')
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('recent-sales-table');
                tableBody.innerHTML = '';

                if (data.sales.length === 0) {
                    const row = document.createElement('tr');
                    row.innerHTML = `<td colspan="5" class="px-6 py-4 text-center">No recent sales found</td>`;
                    tableBody.appendChild(row);
                } else {
                    data.sales.forEach(sale => {
                        const row = document.createElement('tr');
                        row.className = 'bg-white border';
                        row.innerHTML = `
                            <td class="px-6 py-4">${sale.trxId}</td>
                            <td class="px-6 py-4">${sale.date}</td>
                            <td class="px-6 py-4">${sale.total_amount}</td>
                            <td class="px-6 py-4">${sale.payment_method}</td>
                            <td class="px-6 py-4">${sale.sold_by}</td>
                        `;
                        tableBody.appendChild(row);
                    });
                }
            })
            .catch(error => {
                console.error('Error fetching recent sales:', error);
                const tableBody = document.getElementById('recent-sales-table');
                tableBody.innerHTML = `<tr><td colspan="5" class="px-6 py-4 text-center">Error loading recent sales</td></tr>`;
            });
    });
</script>
{% endblock %}
