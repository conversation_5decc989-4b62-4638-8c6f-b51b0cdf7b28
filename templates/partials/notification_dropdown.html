{% load static %}



<!-- Notification Dropdown -->
<div class="relative inline-block" id="notification-dropdown">
    <!-- Notification Bell Icon with Badge -->
    <button type="button" class="notification-bell p-2 text-white relative" id="notification-bell">
        <i class="fas fa-bell text-xl"></i>
        {% if unread_count > 0 %}
            <span class="notification-badge absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full">
                {{ unread_count }}
            </span>
        {% endif %}
    </button>
    
    <!-- Dropdown Content -->
    <div class="notification-dropdown-content hidden absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg z-50 overflow-hidden" id="notification-content">
        <div class="py-2 px-4 bg-blue-900 text-white flex justify-between items-center">
            <h3 class="text-sm font-semibold">Notifications</h3>
            <a href="{% url 'notification:mark_all_as_read' %}" class="text-xs text-white hover:text-gray-200">Mark All as Read</a>
        </div>
        
        <div class="max-h-64 overflow-y-auto" id="notification-list">
            {% if notifications %}
                {% for notification in notifications|slice:":5" %}
                    <a href="{% url 'notification:mark_as_read' notification.id %}" class="block px-4 py-2 {% if not notification.is_read %}bg-blue-50{% else %}hover:bg-gray-100{% endif %} border-b border-gray-200">
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mt-1">
                                {% if notification.notification_type == 'info' %}
                                    <i class="fas fa-info-circle text-blue-500"></i>
                                {% elif notification.notification_type == 'success' %}
                                    <i class="fas fa-check-circle text-green-500"></i>
                                {% elif notification.notification_type == 'warning' %}
                                    <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                {% elif notification.notification_type == 'error' %}
                                    <i class="fas fa-times-circle text-red-500"></i>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-900">{{ notification.title }}</p>
                                <p class="text-xs text-gray-500 truncate">{{ notification.short_message }}</p>
                                <p class="text-xs text-gray-400 mt-1">{{ notification.created_at|timesince }} ago</p>
                            </div>
                        </div>
                    </a>
                {% endfor %}
                
                {% if notifications.count > 5 %}
                    <div class="px-4 py-2 text-center">
                        <a href="{% url 'notification:list' %}" class="text-sm text-blue-600 hover:underline">
                            View All ({{ notifications.count }})
                        </a>
                    </div>
                {% endif %}
            {% else %}
                <div class="px-4 py-6 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p class="text-sm">No notifications</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Notification Dropdown JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const notificationBell = document.getElementById('notification-bell');
        const notificationContent = document.getElementById('notification-content');
        
        // Toggle dropdown when clicking the bell
        notificationBell.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationContent.classList.toggle('hidden');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!notificationContent.contains(e.target) && e.target !== notificationBell) {
                notificationContent.classList.add('hidden');
            }
        });
        
        // Check for new notifications every 60 seconds
        function checkNotifications() {
            fetch('{% url "notification:get_unread_count" %}')
                .then(response => response.json())
                .then(data => {
                    const badge = notificationBell.querySelector('.notification-badge');
                    
                    if (data.unread_count > 0) {
                        if (badge) {
                            badge.textContent = data.unread_count;
                        } else {
                            const newBadge = document.createElement('span');
                            newBadge.className = 'notification-badge absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-red-100 bg-red-600 rounded-full';
                            newBadge.textContent = data.unread_count;
                            notificationBell.appendChild(newBadge);
                        }
                    } else if (badge) {
                        badge.remove();
                    }
                })
                .catch(error => console.error('Error checking notifications:', error));
        }
        
        // Check for new notifications every 60 seconds
        setInterval(checkNotifications, 60000);
    });
</script>

<!-- Notification Dropdown CSS -->
<style>
    .notification-bell {
        cursor: pointer;
    }
    
    .notification-badge {
        transform: translate(25%, -25%);
    }
    
    .notification-dropdown-content {
        right: 0;
        width: 320px;
        max-height: 400px;
    }
</style>
