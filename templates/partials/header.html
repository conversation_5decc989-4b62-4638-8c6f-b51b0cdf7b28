{% load static %}



<!-- Header Component -->
<header class="app-header">
    <div class="header-container">
        <div class="header-left">
            <!-- Page Title (dynamically set based on current page) -->
            <h1 class="header-title">
                {% if title %}{{ title }}{% else %}Legend Fitness Club{% endif %}
            </h1>
            <!-- Optional Subtitle -->
            {% if subtitle %}
            <p class="header-subtitle">{{ subtitle }}</p>
            {% endif %}
        </div>

        <div class="header-right">
            <!-- Language dropdown -->
            <div class="language-dropdown">
                <div class="dropdown">
                    <button
                        class="dropdown-toggle"
                        type="button"
                        id="languageDropdown"
                        data-bs-toggle="dropdown"
                        aria-expanded="false"
                    >
                        {% if LANGUAGE_CODE == 'km' %}
                            <img
                                src="https://flagcdn.com/w20/kh.png"
                                alt="Khmer"
                                class="lang-flag"
                            />
                            <span>Khmer</span>
                        {% else %}
                            <img
                                src="https://flagcdn.com/w20/gb.png"
                                alt="English"
                                class="lang-flag"
                            />
                            <span>English</span>
                        {% endif %}
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                        <li>
                            <a class="dropdown-item d-flex align-items-center" href="{% url 'set_language' 'en' %}">
                                <img src="https://flagcdn.com/w20/gb.png" alt="English" class="lang-flag" />
                                English
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item d-flex align-items-center" href="{% url 'set_language' 'km' %}">
                                <img src="https://flagcdn.com/w20/kh.png" alt="Khmer" class="lang-flag" />
                                Khmer
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- User Dropdown -->
            <div class="user-dropdown">
                <div class="user-dropdown-toggle" id="userDropdownToggle">
                    <!-- User Avatar (first letter of username) -->
                    <div class="user-avatar">
                        {{ request.user.username|first|upper }}
                    </div>

                    <!-- User Info (visible on larger screens) -->
                    <div class="user-info">
                        <div class="user-name">{{ request.user.username }}</div>
                        <div class="user-role">{{ request.user.role|title }}</div>
                    </div>

                    <i class="fas fa-chevron-down text-xs"></i>
                </div>

                <!-- Dropdown Menu -->
                <div class="user-dropdown-menu" id="userDropdownMenu">
                    <div class="user-dropdown-header">
                        <div class="user-dropdown-name">{{ request.user.username }}</div>
                        <div class="user-dropdown-email">{{ request.user.email }}</div>
                    </div>

                    <div class="user-dropdown-items">
                        {% if request.user.role == 'admin' or request.user.role == 'manager' %}
                        <a href="{% url 'settings:dashboard' %}" class="user-dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>Settings</span>
                        </a>
                        {% endif %}
                    </div>

                    <div class="user-dropdown-divider"></div>

                    <a href="{% url 'logout' %}" class="user-dropdown-logout">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Header JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // User dropdown functionality
        const userDropdownToggle = document.getElementById('userDropdownToggle');
        const userDropdownMenu = document.getElementById('userDropdownMenu');

        if (userDropdownToggle && userDropdownMenu) {
            userDropdownToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                userDropdownMenu.classList.toggle('show');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!userDropdownToggle.contains(e.target) && !userDropdownMenu.contains(e.target)) {
                    userDropdownMenu.classList.remove('show');
                }
            });
        }

        // Initialize Bootstrap dropdowns
        var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'))
        var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
            return new bootstrap.Dropdown(dropdownToggleEl)
        });
    });
</script>
