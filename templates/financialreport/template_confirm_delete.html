{% extends "base.html" %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <!-- Header with title and actions -->
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-red-600">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                    <div class="flex items-center">
                        <a href="{% url 'financialreport:template_list' %}" class="mr-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2 rounded-full transition-colors">
                            <i class="fa-solid fa-arrow-left"></i>
                        </a>
                        <div>
                            <h2 class="text-3xl font-bold text-gray-800">{{ title }}</h2>
                            <p class="text-gray-600 mt-1">Confirm deletion of template</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
            <div class="text-center py-8">
                <div class="text-red-500 mb-4">
                    <i class="fa-solid fa-triangle-exclamation text-5xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 mb-4">Are you sure you want to delete this template?</h3>
                <p class="text-gray-600 mb-6">
                    You are about to delete the template <strong>{{ template.name }}</strong> for <strong>{{ template.get_template_type_display }}</strong>.
                    {% if template.is_default %}
                    <span class="block mt-2 text-red-600 font-medium">Warning: This is a default template. Deleting it will cause another template to become the default.</span>
                    {% endif %}
                </p>
                
                <div class="flex justify-center space-x-4">
                    <a href="{% url 'financialreport:template_list' %}" class="bg-gray-200 text-gray-700 px-6 py-2 rounded-md hover:bg-gray-300 transition-colors">Cancel</a>
                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700 transition-colors">Delete Template</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
