{% extends 'base.html' %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Form Header -->
        <div class="mb-4">
            <h3 class="text-2xl font-bold">
                {% if package %}
                    Edit Package: {{ package.name }}
                {% else %}
                    Create New Package
                {% endif %}
            </h3>
        </div>
        
        <!-- Package Form -->
        <div class="bg-white p-4 rounded shadow-md">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-2 gap-4">
                    <!-- Package ID -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Package ID*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100" 
                               name="package_id" 
                               type="text" 
                               placeholder="PKG-001" 
                               value="{{ package.package_id|default:'' }}"
                               {% if package %}readonly{% endif %}
                               required />
                        <p class="text-xs text-gray-500 mt-1">Format: PKG-001, PKG-002, etc.</p>
                    </div>
                    
                    <!-- Name -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Name*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100" 
                               name="name" 
                               type="text" 
                               placeholder="Basic / Premium / VIP" 
                               value="{{ package.name|default:'' }}"
                               required />
                    </div>
                    
                    <!-- Duration -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Duration (Months)*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100" 
                               name="duration" 
                               type="number" 
                               min="1" 
                               max="36" 
                               placeholder="1 / 3 / 6 / 12" 
                               value="{{ package.duration|default:'' }}"
                               required />
                    </div>
                    
                    <!-- Price KHR -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Price (KHR)*</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100" 
                               name="price_khr" 
                               type="number" 
                               placeholder="120000" 
                               value="{{ package.price_khr|default:'' }}"
                               required />
                    </div>
                    
                    <!-- Price USD -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Price (USD) (Optional)</label>
                        <input class="border w-full p-4 leading-tight bg-slate-100" 
                               name="price_usd" 
                               type="number" 
                               step="0.01" 
                               placeholder="30.00" 
                               value="{{ package.price_usd|default:'' }}" />
                    </div>
                    
                    <!-- Access Type -->
                    <div>
                        <label class="block text-sm font-medium mb-1">Access Type*</label>
                        <select name="access_type" class="border w-full p-4 leading-tight bg-slate-100" required>
                            {% for value, label in access_types %}
                            <option value="{{ value }}" {% if package.access_type == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Freeze Policy -->
                    <div class="flex items-center mt-8">
                        <input type="checkbox" 
                               name="freeze_policy" 
                               id="freeze_policy" 
                               class="mr-2"
                               {% if package.freeze_policy %}checked{% endif %} />
                        <label for="freeze_policy">Allow membership freeze/pause</label>
                    </div>
                    
                    <!-- Description -->
                    <div class="col-span-2">
                        <label class="block text-sm font-medium mb-1">Description</label>
                        <textarea class="border w-full p-4 leading-tight bg-slate-100" 
                                  name="description" 
                                  rows="3" 
                                  placeholder="Package description and benefits">{{ package.description|default:'' }}</textarea>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="col-span-2 flex justify-between mt-4">
                        <a href="{% url 'member:package_list' %}" class="bg-gray-500 text-white font-bold py-2 px-4 rounded">Cancel</a>
                        <button class="bg-blue-900 text-white font-bold py-2 px-4 rounded" type="submit">
                            {% if package %}Update{% else %}Create{% endif %} Package
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
