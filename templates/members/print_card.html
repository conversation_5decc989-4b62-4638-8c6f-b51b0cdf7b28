{% extends 'base.html' %}



{% block head %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .print-section, .print-section * {
            visibility: visible;
        }
        .print-section {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
        }
        .no-print {
            display: none;
        }
    }

    .id-card {
        width: 85.6mm;
        height: 54mm;
        border: 1px solid #000;
        border-radius: 10px;
        padding: 10px;
        margin: 0 auto;
        background-color: #fff;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
    }

    .id-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('/static/img/logo.png') no-repeat center;
        background-size: 50%;
        opacity: 0.05;
        z-index: 0;
    }

    .id-card-header {
        text-align: center;
        border-bottom: 2px solid #0f3460;
        padding-bottom: 5px;
        margin-bottom: 10px;
        position: relative;
        z-index: 1;
    }

    .id-card-content {
        display: flex;
        position: relative;
        z-index: 1;
    }

    .id-card-photo {
        width: 25mm;
        height: 30mm;
        border: 1px solid #ccc;
        margin-right: 10px;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
    }

    .id-card-photo img {
        max-width: 100%;
        max-height: 100%;
    }

    .id-card-details {
        flex: 1;
        font-size: 12px;
    }

    .id-card-details p {
        margin: 3px 0;
    }

    .id-card-footer {
        text-align: center;
        font-size: 10px;
        margin-top: 5px;
        border-top: 1px solid #ccc;
        padding-top: 5px;
        position: relative;
        z-index: 1;
    }

    .khmer-font {
        font-family: 'Khmer OS', 'Khmer OS System', sans-serif;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <div class="print-section">
                <div class="id-card">
                    <!-- ID Card Header -->
                    <div class="id-card-header">
                        <h1 class="text-xl font-bold">LEGEND FITNESS</h1>
                        <p class="text-sm">Member ID Card</p>
                    </div>

                    <!-- ID Card Content -->
                    <div class="id-card-content">
                        <div class="id-card-photo">
                            {% if member.photo %}
                            <img src="{{ member.photo.url }}" alt="{{ member.name }}">
                            {% else %}
                            <div class="text-center text-gray-400">No Photo</div>
                            {% endif %}
                        </div>
                        <div class="id-card-details">
                            <p><strong>ID:</strong> {{ member.member_id }}</p>
                            <p><strong>Name:</strong> {{ member.name }}</p>
                            <p><strong>Package:</strong> {{ member.package.name }}</p>
                            <p><strong>Valid Until:</strong> {{ member.end_date|date:"d-M-Y" }}</p>
                            <p><strong>Blood Type:</strong> {{ member.blood_type|default:"Not specified" }}</p>
                            <p><strong>Emergency:</strong> {{ member.emergency_contact|default:"Not specified" }}</p>
                        </div>
                    </div>

                    <!-- ID Card Footer -->
                    <div class="id-card-footer khmer-font">
                        <p>ABA: 012 345 678 | Telegram: @LegendFitness</p>
                        <p>សូមយកកាតនេះមកជាមួយពេលចូលហាត់ប្រាណ</p>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-900 text-white font-bold py-2 px-6 rounded">Print ID Card</button>
                <a href="{% url 'member:index' %}" class="bg-gray-500 text-white font-bold py-2 px-6 rounded ml-2">Back</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
