{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}
{% load static %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Make a Deposit</h3>
            <div class="flex space-x-2">
                <a href="{% url 'finance:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Finance
                </a>
            </div>
        </div>

        <!-- Deposit Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        <!-- Amount -->
                        <div>
                            <label for="amount_khr" class="block text-sm font-medium text-gray-700 mb-1">Amount (KHR)*</label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500">៛</span>
                                </div>
                                <input type="text" id="amount_khr" name="amount_khr" class="pl-8 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" placeholder="0" required>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Enter the amount in Cambodian Riel</p>
                        </div>

                        <!-- Payment Method -->
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method*</label>
                            <select id="payment_method" name="payment_method" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" required>
                                {% for value, label in payment_methods %}
                                <option value="{{ value }}">{{ label }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Notes -->
                        <div>
                            <label for="notes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                            <textarea id="notes" name="notes" rows="4" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-4 leading-tight bg-slate-100" placeholder="Additional details about this deposit"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="mt-8 pt-5 border-t border-gray-200 flex justify-between">
                    <a href="{% url 'finance:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Complete Deposit
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Format amount as currency
        const amountInput = document.getElementById('amount_khr');

        // Setup formatted input
        setupFormattedInput(amountInput, 'khr');

        // Ensure the form submits the numeric value
        const form = amountInput.closest('form');
        form.addEventListener('submit', function(e) {
            // Get the current formatted value
            const formattedValue = amountInput.value;

            // Convert to numeric value for submission
            amountInput.value = formattedValue.replace(/[^\d]/g, '');
        });
    });
</script>
{% endblock %}
