{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">
            <!-- Template Selector -->
            <div class="no-print mb-4">
                <form method="get" class="flex items-center space-x-2">
                    <label class="text-sm font-medium">Template:</label>
                    <select name="template" class="border p-2 rounded" onchange="this.form.submit()">
                        {% for t in all_templates %}
                        <option value="{{ t.id }}" {% if template.id == t.id %}selected{% endif %}>
                            {{ t.name }} {% if t.is_default %}(Default){% endif %}
                        </option>
                        {% endfor %}
                    </select>
                    <a href="{% url 'finance:template_list' %}" class="text-blue-600 hover:underline">
                        Manage Templates
                    </a>
                </form>
            </div>

            <!-- Print Controls -->
            <div class="no-print mb-6 flex justify-between">
                <a href="{% url 'finance:index' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>Back
                </a>
                <button onclick="window.print()" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-4 rounded transition duration-200">
                    <i class="fas fa-print mr-2"></i>Print Receipt
                </button>
            </div>

            <!-- Receipt Template -->
            <div class="receipt-container" style="background-color: {{ template.background_color }}; color: {{ template.text_color }}; border: 1px solid #ddd; padding: 20px;">
                <!-- Header -->
                <div class="text-center mb-6">
                    {% if template.company_logo %}
                    <div class="mb-4">
                        <img src="{{ template.company_logo.url }}" alt="Company Logo" class="mx-auto h-20">
                    </div>
                    {% endif %}
                    <h1 class="text-2xl font-bold" style="color: {{ template.accent_color }};">{{ template.header_text }}</h1>
                    <h2 class="text-xl">{{ template.subheader_text }}</h2>
                </div>

                <!-- Company Info -->
                {% if template.show_company_info %}
                <div class="mb-6 text-center">
                    <p>Legend Fitness Club</p>
                    <p>Phnom Penh, Cambodia</p>
                    <p>Phone: ***********</p>
                </div>
                {% endif %}

                <!-- Transaction Details -->
                <div class="mb-6">
                    <div class="border-b-2 border-t-2 py-2 mb-4" style="border-color: {{ template.accent_color }};">
                        <h3 class="text-lg font-bold text-center" style="color: {{ template.accent_color }};">
                            {% if transaction.transaction_type == 'deposit' %}
                                DEPOSIT RECEIPT
                            {% else %}
                                WITHDRAWAL RECEIPT
                            {% endif %}
                        </h3>
                    </div>

                    <table class="w-full">
                        <tr>
                            <td class="py-2 font-semibold">Transaction ID:</td>
                            <td class="py-2">{{ transaction.transaction_id }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Date:</td>
                            <td class="py-2">{{ transaction.transaction_date|date:"d M Y H:i" }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Type:</td>
                            <td class="py-2">{{ transaction.get_transaction_type_display }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Amount:</td>
                            <td class="py-2">{{ transaction.amount_khr|format_khr }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Payment Method:</td>
                            <td class="py-2">{{ transaction.get_payment_method_display }}</td>
                        </tr>

                        <tr>
                            <td class="py-2 font-semibold">Status:</td>
                            <td class="py-2">{{ transaction.get_status_display }}</td>
                        </tr>
                        <tr>
                            <td class="py-2 font-semibold">Staff:</td>
                            <td class="py-2">{{ transaction.staff.name }}</td>
                        </tr>
                        {% if transaction.approved_by %}
                        <tr>
                            <td class="py-2 font-semibold">Approved By:</td>
                            <td class="py-2">{{ transaction.approved_by.name }}</td>
                        </tr>
                        {% endif %}
                        {% if transaction.notes %}
                        <tr>
                            <td class="py-2 font-semibold">Notes:</td>
                            <td class="py-2">{{ transaction.notes }}</td>
                        </tr>
                        {% endif %}
                    </table>
                </div>

                <!-- Signatures -->
                {% if template.show_signatures %}
                <div class="mt-12 grid grid-cols-2 gap-8">
                    <div class="text-center">
                        <div class="border-t border-gray-400 pt-2">
                            Authorized Signature
                        </div>
                    </div>
                    <div class="text-center">
                        <div class="border-t border-gray-400 pt-2">
                            Recipient Signature
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Footer -->
                <div class="mt-12 text-center text-sm">
                    <p>{{ template.footer_text }}</p>
                    <p class="mt-2">Printed on {{ transaction.transaction_date|date:"d M Y H:i" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<style>
    @media print {
        body * {
            visibility: hidden;
        }
        .receipt-container, .receipt-container * {
            visibility: visible;
        }
        .receipt-container {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            padding: 40px !important;
        }
        .no-print {
            display: none !important;
        }
    }

    /* Apply custom CSS if provided */
    {% if template.custom_css %}
    {{ template.custom_css|safe }}
    {% endif %}
</style>
{% endblock %}
