{% extends 'base.html' %}
{% load custom_filters %}



{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <!-- Header with navigation -->
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-2xl font-bold">Create Receipt Template</h3>
            <div class="flex space-x-2">
                <a href="{% url 'finance:template_list' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                    <i class="fa-solid fa-arrow-left mr-2"></i>Back to Templates
                </a>
            </div>
        </div>

        <!-- Template Form -->
        <div class="bg-white p-6 rounded shadow-md">
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Left Column -->
                    <div class="space-y-6">
                        <!-- Basic Information -->
                        <div class="bg-gray-50 p-4 rounded border border-gray-200">
                            <h4 class="text-lg font-semibold mb-4">Basic Information</h4>
                            
                            <!-- Template Name -->
                            <div class="mb-4">
                                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Template Name*</label>
                                <input type="text" id="name" name="name" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" required>
                            </div>
                            
                            <!-- Language -->
                            <div class="mb-4">
                                <label for="language" class="block text-sm font-medium text-gray-700 mb-1">Language*</label>
                                <select id="language" name="language" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" required>
                                    {% for value, label in languages %}
                                    <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <!-- Is Default -->
                            <div class="mb-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="is_default" name="is_default" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                    <label for="is_default" class="ml-2 block text-sm text-gray-700">Set as Default Template</label>
                                </div>
                            </div>
                        </div>

                        <!-- Content -->
                        <div class="bg-gray-50 p-4 rounded border border-gray-200">
                            <h4 class="text-lg font-semibold mb-4">Content</h4>
                            
                            <!-- Header Text -->
                            <div class="mb-4">
                                <label for="header_text" class="block text-sm font-medium text-gray-700 mb-1">Header Text</label>
                                <input type="text" id="header_text" name="header_text" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="LEGEND FITNESS">
                            </div>
                            
                            <!-- Subheader Text -->
                            <div class="mb-4">
                                <label for="subheader_text" class="block text-sm font-medium text-gray-700 mb-1">Subheader Text</label>
                                <input type="text" id="subheader_text" name="subheader_text" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="Transaction Receipt">
                            </div>
                            
                            <!-- Footer Text -->
                            <div class="mb-4">
                                <label for="footer_text" class="block text-sm font-medium text-gray-700 mb-1">Footer Text</label>
                                <input type="text" id="footer_text" name="footer_text" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="Thank you for your business!">
                            </div>
                            
                            <!-- Company Logo -->
                            <div>
                                <label for="company_logo" class="block text-sm font-medium text-gray-700 mb-1">Company Logo</label>
                                <input type="file" id="company_logo" name="company_logo" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                            </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="space-y-6">
                        <!-- Styling -->
                        <div class="bg-gray-50 p-4 rounded border border-gray-200">
                            <h4 class="text-lg font-semibold mb-4">Styling</h4>
                            
                            <!-- Background Color -->
                            <div class="mb-4">
                                <label for="background_color" class="block text-sm font-medium text-gray-700 mb-1">Background Color</label>
                                <div class="flex">
                                    <input type="color" id="background_color_picker" class="h-10 w-10 border-gray-300 rounded-l-md" value="#ffffff">
                                    <input type="text" id="background_color" name="background_color" class="block w-full border-gray-300 rounded-r-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="#ffffff">
                                </div>
                            </div>
                            
                            <!-- Text Color -->
                            <div class="mb-4">
                                <label for="text_color" class="block text-sm font-medium text-gray-700 mb-1">Text Color</label>
                                <div class="flex">
                                    <input type="color" id="text_color_picker" class="h-10 w-10 border-gray-300 rounded-l-md" value="#000000">
                                    <input type="text" id="text_color" name="text_color" class="block w-full border-gray-300 rounded-r-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="#000000">
                                </div>
                            </div>
                            
                            <!-- Accent Color -->
                            <div class="mb-4">
                                <label for="accent_color" class="block text-sm font-medium text-gray-700 mb-1">Accent Color</label>
                                <div class="flex">
                                    <input type="color" id="accent_color_picker" class="h-10 w-10 border-gray-300 rounded-l-md" value="#0c4a6e">
                                    <input type="text" id="accent_color" name="accent_color" class="block w-full border-gray-300 rounded-r-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" value="#0c4a6e">
                                </div>
                            </div>
                        </div>

                        <!-- Options -->
                        <div class="bg-gray-50 p-4 rounded border border-gray-200">
                            <h4 class="text-lg font-semibold mb-4">Options</h4>
                            
                            <!-- Show Company Info -->
                            <div class="mb-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="show_company_info" name="show_company_info" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                    <label for="show_company_info" class="ml-2 block text-sm text-gray-700">Show Company Information</label>
                                </div>
                            </div>
                            
                            <!-- Show Signatures -->
                            <div class="mb-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="show_signatures" name="show_signatures" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                                    <label for="show_signatures" class="ml-2 block text-sm text-gray-700">Show Signature Lines</label>
                                </div>
                            </div>
                        </div>

                        <!-- Custom CSS -->
                        <div class="bg-gray-50 p-4 rounded border border-gray-200">
                            <h4 class="text-lg font-semibold mb-4">Advanced</h4>
                            
                            <!-- Custom CSS -->
                            <div>
                                <label for="custom_css" class="block text-sm font-medium text-gray-700 mb-1">Custom CSS</label>
                                <textarea id="custom_css" name="custom_css" rows="6" class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2 leading-tight bg-white" placeholder="Add custom CSS styles here"></textarea>
                                <p class="mt-1 text-sm text-gray-500">Advanced users only. Add custom CSS to further customize the receipt template.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="mt-8 pt-5 border-t border-gray-200 flex justify-between">
                    <a href="{% url 'finance:template_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Cancel
                    </a>
                    <button type="submit" class="bg-blue-900 hover:bg-blue-800 text-white font-bold py-2 px-6 rounded transition duration-200">
                        Create Template
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Color picker synchronization
        const colorPickers = [
            { picker: 'background_color_picker', input: 'background_color' },
            { picker: 'text_color_picker', input: 'text_color' },
            { picker: 'accent_color_picker', input: 'accent_color' }
        ];
        
        colorPickers.forEach(item => {
            const picker = document.getElementById(item.picker);
            const input = document.getElementById(item.input);
            
            // Update input when picker changes
            picker.addEventListener('input', function() {
                input.value = this.value;
            });
            
            // Update picker when input changes
            input.addEventListener('input', function() {
                if (this.value.startsWith('#') && (this.value.length === 4 || this.value.length === 7)) {
                    picker.value = this.value;
                }
            });
        });
    });
</script>
{% endblock %}
