{% extends 'base.html' %}
{% load custom_filters %}



{% block extra_css %}
<style>
    @media print {
        .no-print {
            display: none !important;
        }

        /* Remove any background colors or borders */
        .receipt-border {
            border: 1px solid #ccc !important;
            background-color: white !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 800px !important;
        }
    }

    .receipt-border {
        border: 1px solid #ccc;
        padding: 20px;
        border-radius: 5px;
    }

    .khmer-font {
        font-family: 'Hanuman', 'Khmer OS', 'Khmer OS System', sans-serif;
        line-height: 1.5;
    }

    /* Receipt styles */
    .receipt {
        background-color: #ffffff;
        color: #333333;
    }

    .receipt h1, .receipt h2, .receipt h3 {
        color: #1e40af;
    }

    .receipt .border-t, .receipt .border-b, .receipt .border-t-2 {
        border-color: #1e40af;
    }
</style>
{% endblock %}

{% block body %}
<div class="conponentSection p-5 bg-gray-200">
    <div class="componentWrapper">
        <div class="bg-white p-8 rounded shadow-md max-w-2xl mx-auto">


            <!-- Header with navigation -->
            <div class="flex justify-between items-center mb-4 no-print">
                <h3 class="text-2xl font-bold">
                    {% if is_preview %}
                    Receipt Preview
                    {% else %}
                    Pay-per-visit Receipt
                    {% endif %}
                </h3>
                <div class="flex space-x-2">
                    {% if is_preview %}
                    <a href="{% url 'paypervisit:index' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                        <i class="fa-solid fa-arrow-left mr-2"></i>Back to POS
                    </a>
                    {% else %}
                    <a href="{% url 'paypervisit:transaction' %}" class="bg-blue-900 text-white px-4 py-2 rounded">
                        <i class="fa-solid fa-arrow-left mr-2"></i>Back to Transactions
                    </a>
                    {% endif %}
                    <button onclick="window.print()" class="bg-purple-600 text-white px-4 py-2 rounded">
                        <i class="fa-solid fa-print mr-2"></i>Print Receipt
                    </button>
                </div>
            </div>

            {% if is_preview %}
            <!-- Preview Notice -->
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-6 no-print">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            This is a preview of how your receipt will look. The transaction has not been processed yet.
                        </p>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Receipt Preview -->
            <div class="receipt-border receipt">
                <!-- Header -->
                <div class="text-center mb-6">
                    <h1 class="text-2xl font-bold">LEGEND FITNESS</h1>
                    <h2 class="text-xl">Pay-per-visit Receipt</h2>
                </div>

                <!-- Company Info -->
                <div class="mb-4 text-center text-sm">
                    <p>Address: 123 Main Street, Phnom Penh, Cambodia</p>
                    <p>Phone: +855 12 345 678</p>
                </div>

                <!-- Receipt Details -->
                <div class="mb-4">
                    <div class="flex justify-between border-b pb-2 mb-2">
                        <span class="font-semibold">Receipt No:</span>
                        <span>{{ transaction.trxId }}</span>
                    </div>
                    <div class="flex justify-between border-b pb-2 mb-2">
                        <span class="font-semibold">Date:</span>
                        <span>{{ transaction.date|date:"F j, Y H:i" }}</span>
                    </div>
                    <div class="flex justify-between border-b pb-2 mb-2">
                        <span class="font-semibold">Cashier:</span>
                        <span>{{ transaction.received_by.username }}</span>
                    </div>
                    <div class="flex justify-between border-b pb-2 mb-2">
                        <span class="font-semibold">Payment Method:</span>
                        <span>{{ transaction.get_payment_method_display }}</span>
                    </div>
                </div>

                <!-- Transaction Details -->
                <div class="mb-6">
                    <h3 class="font-bold mb-2 border-b-2 pb-1">Transaction Details</h3>
                    <table class="w-full">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Description</th>
                                <th class="text-right py-2">Amount</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="border-b">
                                <td class="py-2">
                                    Pay-per-visit ({{ transaction.num_people }} {% if transaction.num_people == 1 %}person{% else %}people{% endif %})
                                </td>
                                <td class="text-right py-2">{{ transaction.amount }}៛</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="font-bold">
                                <td class="py-2">Total</td>
                                <td class="text-right py-2">{{ transaction.amount }}៛</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Footer -->
                <div class="mt-8 text-center text-sm">
                    <p>Thank you for visiting Legend Fitness!</p>
                    <p class="text-xs mt-2">
                        Printed on {% now "F j, Y H:i" %}
                    </p>
                </div>
            </div>

            <!-- Print Button -->
            <div class="mt-6 text-center no-print">
                <button onclick="window.print()" class="bg-blue-600 text-white px-6 py-2 rounded hover:bg-blue-700">
                    <i class="fa-solid fa-print mr-2"></i>Print Receipt
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}
