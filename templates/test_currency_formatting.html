{% extends 'base.html' %}
{% load custom_filters %}
{% load currency_filters %}
{% load static %}

{% block title %}Currency Formatting Test{% endblock %}

{% block body %}
<div class="conponentSection p-5 sm:ml-64 bg-gray-100">
    <div class="componentWrapper max-w-7xl mx-auto">
        <div class="bg-white p-6 rounded-lg shadow-sm mb-6 border-l-4 border-blue-600">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">Currency Formatting Test</h2>
            <p class="text-gray-600 mb-6">This page tests the currency formatting filters and JavaScript functions.</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Template Filters Test -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">Template Filters Test</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">KHR Formatting</h4>
                            <table class="w-full text-sm text-left mt-2">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Original Value</th>
                                        <th class="px-4 py-2">Formatted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="px-4 py-2">1000</td>
                                        <td class="px-4 py-2">{{ 1000|format_khr }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">10000</td>
                                        <td class="px-4 py-2">{{ 10000|format_khr }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1000000</td>
                                        <td class="px-4 py-2">{{ 1000000|format_khr }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1234567</td>
                                        <td class="px-4 py-2">{{ 1234567|format_khr }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">USD Formatting</h4>
                            <table class="w-full text-sm text-left mt-2">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Original Value</th>
                                        <th class="px-4 py-2">Formatted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="px-4 py-2">1000</td>
                                        <td class="px-4 py-2">{{ 1000|format_usd }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">10000</td>
                                        <td class="px-4 py-2">{{ 10000|format_usd }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1000000</td>
                                        <td class="px-4 py-2">{{ 1000000|format_usd }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1234.56</td>
                                        <td class="px-4 py-2">{{ 1234.56|format_usd }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">Number Formatting</h4>
                            <table class="w-full text-sm text-left mt-2">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Original Value</th>
                                        <th class="px-4 py-2">Formatted Value</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="px-4 py-2">1000</td>
                                        <td class="px-4 py-2">{{ 1000|format_number_with_commas }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">10000</td>
                                        <td class="px-4 py-2">{{ 10000|format_number_with_commas }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1000000</td>
                                        <td class="px-4 py-2">{{ 1000000|format_number_with_commas }}</td>
                                    </tr>
                                    <tr>
                                        <td class="px-4 py-2">1234.56</td>
                                        <td class="px-4 py-2">{{ 1234.56|format_number_with_commas }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- JavaScript Functions Test -->
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-xl font-bold text-gray-800 mb-4">JavaScript Functions Test</h3>
                    
                    <div class="space-y-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">KHR Formatting</h4>
                            <table class="w-full text-sm text-left mt-2">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Original Value</th>
                                        <th class="px-4 py-2">Formatted Value</th>
                                    </tr>
                                </thead>
                                <tbody id="khr-test-results">
                                    <!-- Will be filled by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">USD Formatting</h4>
                            <table class="w-full text-sm text-left mt-2">
                                <thead>
                                    <tr class="bg-gray-100">
                                        <th class="px-4 py-2">Original Value</th>
                                        <th class="px-4 py-2">Formatted Value</th>
                                    </tr>
                                </thead>
                                <tbody id="usd-test-results">
                                    <!-- Will be filled by JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div>
                            <h4 class="text-lg font-semibold text-gray-700">Input Field Formatting</h4>
                            <div class="mt-2 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">KHR Input</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500">៛</span>
                                        </div>
                                        <input type="text" id="khr-input" class="pl-8 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2" placeholder="0">
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Type a number and click outside the field to see formatting</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">USD Input</label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <span class="text-gray-500">$</span>
                                        </div>
                                        <input type="text" id="usd-input" class="pl-8 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 p-2" placeholder="0.00">
                                    </div>
                                    <p class="text-sm text-gray-500 mt-1">Type a number and click outside the field to see formatting</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Test KHR formatting
        const khrTestValues = [1000, 10000, 1000000, 1234567];
        const khrTestResults = document.getElementById('khr-test-results');
        
        khrTestValues.forEach(value => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2">${value}</td>
                <td class="px-4 py-2">${formatKHR(value)}</td>
            `;
            khrTestResults.appendChild(row);
        });
        
        // Test USD formatting
        const usdTestValues = [1000, 10000, 1000000, 1234.56];
        const usdTestResults = document.getElementById('usd-test-results');
        
        usdTestValues.forEach(value => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-4 py-2">${value}</td>
                <td class="px-4 py-2">${formatUSD(value)}</td>
            `;
            usdTestResults.appendChild(row);
        });
        
        // Setup formatted inputs
        const khrInput = document.getElementById('khr-input');
        const usdInput = document.getElementById('usd-input');
        
        setupFormattedInput(khrInput, 'khr');
        setupFormattedInput(usdInput, 'usd');
    });
</script>
{% endblock %}
