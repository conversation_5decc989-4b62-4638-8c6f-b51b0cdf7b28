from django.contrib import admin
from .models import Transaction, TransactionTemplate

@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ('transaction_id', 'transaction_type', 'amount_khr', 'amount_usd', 'status', 'staff', 'transaction_date')
    list_filter = ('transaction_type', 'status', 'payment_method', 'transaction_date')
    search_fields = ('transaction_id', 'notes', 'staff__name')
    readonly_fields = ('transaction_id', 'transaction_date', 'updated_at')
    fieldsets = (
        ('Transaction Details', {
            'fields': ('transaction_id', 'transaction_type', 'amount_khr', 'amount_usd', 'payment_method', 'status')
        }),
        ('Additional Information', {
            'fields': ('notes',)
        }),
        ('User Information', {
            'fields': ('staff', 'approved_by')
        }),
        ('Timestamps', {
            'fields': ('transaction_date', 'updated_at')
        }),
    )

@admin.register(TransactionTemplate)
class TransactionTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'language', 'is_default', 'created_at')
    list_filter = ('language', 'is_default')
    search_fields = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        ('Template Information', {
            'fields': ('name', 'language', 'is_default')
        }),
        ('Content', {
            'fields': ('header_text', 'subheader_text', 'footer_text', 'company_logo')
        }),
        ('Styling', {
            'fields': ('background_color', 'text_color', 'accent_color', 'show_company_info', 'show_signatures', 'custom_css')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )
