from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from user.models import User, MetaData
from core.utils import generate_unique_transaction_id

class Transaction(models.Model):
    """
    Model for tracking financial transactions (deposits and withdrawals)
    """
    TRANSACTION_TYPE_CHOICES = [
        ('deposit', _('Deposit')),
        ('withdrawal', _('Withdrawal')),
    ]

    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('other', _('Other')),
    ]

    STATUS_CHOICES = [
        ('completed', _('Completed')),
        ('pending', _('Pending')),
        ('rejected', _('Rejected')),
    ]



    transaction_id = models.CharField(_("Transaction ID"), max_length=30, unique=True)
    transaction_type = models.CharField(_("Transaction Type"), max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    amount_khr = models.IntegerField(_("Amount (KHR)"))
    amount_usd = models.DecimalField(_("Amount (USD)"), max_digits=10, decimal_places=2, null=True, blank=True)
    payment_method = models.CharField(_("Payment Method"), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')



    notes = models.TextField(_("Notes"), null=True, blank=True)
    status = models.CharField(_("Status"), max_length=20, choices=STATUS_CHOICES, default='completed')

    # User who made the transaction
    staff = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='transactions')

    # User who approved the transaction (for withdrawals)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_transactions')

    transaction_date = models.DateTimeField(_("Transaction Date"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # Generate transaction_id if not provided
        if not self.transaction_id:
            prefix = "DEP" if self.transaction_type == "deposit" else "WDR"
            self.transaction_id = generate_unique_transaction_id(prefix=prefix)

        # Update MetaData funds
        try:
            metadata = MetaData.objects.first()
            if metadata and self.status == 'completed':
                if self.transaction_type == 'deposit':
                    metadata.funds += self.amount_khr
                elif self.transaction_type == 'withdrawal':
                    metadata.funds -= self.amount_khr
                metadata.lastChecked = timezone.now().date()
                metadata.save()
        except Exception as e:
            print(f"Error updating funds: {str(e)}")

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.transaction_id} - {self.get_transaction_type_display()} ({self.amount_khr} ៛)"

    class Meta:
        ordering = ['-transaction_date']
        verbose_name = _("Transaction")
        verbose_name_plural = _("Transactions")


class TransactionTemplate(models.Model):
    """
    Model for transaction receipt templates
    """
    LANGUAGE_CHOICES = [
        ('en', _('English')),
        ('km', _('Khmer')),
        ('both', _('Both (Bilingual)')),
    ]

    name = models.CharField(_("Template Name"), max_length=100)
    is_default = models.BooleanField(_("Default Template"), default=False)
    language = models.CharField(_("Language"), max_length=10, choices=LANGUAGE_CHOICES, default='en')
    header_text = models.CharField(_("Header Text"), max_length=200, default="LEGEND FITNESS")
    subheader_text = models.CharField(_("Subheader Text"), max_length=200, default="Transaction Receipt")
    footer_text = models.CharField(_("Footer Text"), max_length=200, default="Thank you for your business!")
    company_logo = models.ImageField(_("Company Logo"), upload_to='transaction_templates/', null=True, blank=True)
    background_color = models.CharField(_("Background Color"), max_length=20, default="#ffffff")
    text_color = models.CharField(_("Text Color"), max_length=20, default="#000000")
    accent_color = models.CharField(_("Accent Color"), max_length=20, default="#0c4a6e")
    show_company_info = models.BooleanField(_("Show Company Info"), default=True)
    show_signatures = models.BooleanField(_("Show Signature Lines"), default=True)
    custom_css = models.TextField(_("Custom CSS"), blank=True, null=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # If this template is being set as default, unset default for all other templates
        if self.is_default:
            TransactionTemplate.objects.filter(is_default=True).update(is_default=False)

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({'Default' if self.is_default else 'Custom'})"

    class Meta:
        ordering = ['-is_default', 'name']
        verbose_name = _("Transaction Template")
        verbose_name_plural = _("Transaction Templates")
