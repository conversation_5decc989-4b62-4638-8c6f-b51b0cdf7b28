# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Transaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_id', models.CharField(max_length=30, unique=True, verbose_name='Transaction ID')),
                ('transaction_type', models.CharField(choices=[('deposit', 'Deposit'), ('withdrawal', 'Withdrawal')], max_length=20, verbose_name='Transaction Type')),
                ('amount_khr', models.IntegerField(verbose_name='Amount (KHR)')),
                ('amount_usd', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Amount (USD)')),
                ('payment_method', models.Char<PERSON>ield(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('status', models.CharField(choices=[('completed', 'Completed'), ('pending', 'Pending'), ('rejected', 'Rejected')], default='completed', max_length=20, verbose_name='Status')),
                ('transaction_date', models.DateTimeField(auto_now_add=True, verbose_name='Transaction Date')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Transaction',
                'verbose_name_plural': 'Transactions',
                'ordering': ['-transaction_date'],
            },
        ),
        migrations.CreateModel(
            name='TransactionTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Template Name')),
                ('is_default', models.BooleanField(default=False, verbose_name='Default Template')),
                ('language', models.CharField(choices=[('en', 'English'), ('km', 'Khmer'), ('both', 'Both (Bilingual)')], default='en', max_length=10, verbose_name='Language')),
                ('header_text', models.CharField(default='LEGEND FITNESS', max_length=200, verbose_name='Header Text')),
                ('subheader_text', models.CharField(default='Transaction Receipt', max_length=200, verbose_name='Subheader Text')),
                ('footer_text', models.CharField(default='Thank you for your business!', max_length=200, verbose_name='Footer Text')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='transaction_templates/', verbose_name='Company Logo')),
                ('background_color', models.CharField(default='#ffffff', max_length=20, verbose_name='Background Color')),
                ('text_color', models.CharField(default='#000000', max_length=20, verbose_name='Text Color')),
                ('accent_color', models.CharField(default='#0c4a6e', max_length=20, verbose_name='Accent Color')),
                ('show_company_info', models.BooleanField(default=True, verbose_name='Show Company Info')),
                ('show_signatures', models.BooleanField(default=True, verbose_name='Show Signature Lines')),
                ('custom_css', models.TextField(blank=True, null=True, verbose_name='Custom CSS')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Transaction Template',
                'verbose_name_plural': 'Transaction Templates',
                'ordering': ['-is_default', 'name'],
            },
        ),
    ]
