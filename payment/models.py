from django.db import models
from django.utils.translation import gettext_lazy as _
from members.models import Member
from user.models import User
from django.core.exceptions import ValidationError

class Payment(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('cash', _('Cash')),
        ('bank', _('Bank Transfer')),
        ('other', _('Other')),
    ]

    invoice_no = models.CharField(_("Invoice No."), max_length=30, unique=True)
    member = models.ForeignKey(Member, on_delete=models.CASCADE, related_name='payments')
    amount_khr = models.IntegerField(_("Amount (KHR)"))
    amount_usd = models.DecimalField(_("Amount (USD)"), max_digits=10, decimal_places=2, null=True, blank=True)
    payment_method = models.CharField(_("Payment Method"), max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    payment_date = models.DateTimeField(_("Payment Date"), auto_now_add=True)
    collector = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='collected_payments')
    notes = models.TextField(_("Notes"), null=True, blank=True)

    def __str__(self):
        return f"{self.invoice_no} - {self.member.name} ({self.amount_khr}៛)"

    class Meta:
        ordering = ['-payment_date']

class PaymentTemplate(models.Model):
    LANGUAGE_CHOICES = [
        ('en', 'English'),
        ('km', 'Khmer'),
        ('both', 'Both (Bilingual)'),
    ]

    name = models.CharField(_("Template Name"), max_length=100)
    is_default = models.BooleanField(_("Default Template"), default=False)
    language = models.CharField(_("Language"), max_length=10, choices=LANGUAGE_CHOICES, default='en')
    header_text = models.CharField(_("Header Text"), max_length=200, default="LEGEND FITNESS")
    subheader_text = models.CharField(_("Subheader Text"), max_length=200, default="Payment Receipt")
    footer_text = models.CharField(_("Footer Text"), max_length=200, default="Thank you for your payment!")
    company_logo = models.ImageField(_("Company Logo"), upload_to='payment_templates/', null=True, blank=True)
    background_color = models.CharField(_("Background Color"), max_length=20, default="#ffffff")
    text_color = models.CharField(_("Text Color"), max_length=20, default="#000000")
    accent_color = models.CharField(_("Accent Color"), max_length=20, default="#0c4a6e")
    show_company_info = models.BooleanField(_("Show Company Info"), default=True)
    show_signatures = models.BooleanField(_("Show Signature Lines"), default=True)
    custom_css = models.TextField(_("Custom CSS"), blank=True, null=True)
    created_at = models.DateTimeField(_("Created At"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Updated At"), auto_now=True)

    def save(self, *args, **kwargs):
        # If this template is being set as default, unset any other default templates
        if self.is_default:
            PaymentTemplate.objects.filter(is_default=True).update(is_default=False)

        # If no default template exists, make this one default
        if not PaymentTemplate.objects.filter(is_default=True).exists():
            self.is_default = True

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({'Default' if self.is_default else 'Custom'})"
