from django.contrib import admin
from .models import Payment, PaymentTemplate

@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ('invoice_no', 'member', 'amount_khr', 'payment_method', 'payment_date', 'collector')
    list_filter = ('payment_method', 'payment_date')
    search_fields = ('invoice_no', 'member__name', 'notes')
    date_hierarchy = 'payment_date'

@admin.register(PaymentTemplate)
class PaymentTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'language', 'is_default', 'created_at', 'updated_at')
    list_filter = ('language', 'is_default')
    search_fields = ('name',)
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'is_default', 'language')
        }),
        ('Content', {
            'fields': ('header_text', 'subheader_text', 'footer_text', 'company_logo')
        }),
        ('Styling', {
            'fields': ('background_color', 'text_color', 'accent_color', 'custom_css')
        }),
        ('Display Options', {
            'fields': ('show_company_info', 'show_signatures')
        }),
    )
