from django.urls import path
from . import views

app_name = "payment"

urlpatterns = [
    path('', views.index, name='index'),
    path('create/', views.create_payment, name='create_payment'),
    path('delete/<int:pk>/', views.delete_payment, name='delete_payment'),
    path('print_receipt/<int:pk>/', views.print_receipt, name='print_receipt'),
    path('bulk-actions/', views.bulk_actions, name='bulk_actions'),

    # Template management
    path('templates/', views.template_list, name='template_list'),
    path('templates/create/', views.create_template, name='create_template'),
    path('templates/edit/<int:pk>/', views.edit_template, name='edit_template'),
    path('templates/delete/<int:pk>/', views.delete_template, name='delete_template'),
    path('templates/preview/<int:pk>/', views.preview_template, name='preview_template'),
]
