from django.shortcuts import redirect
from django.contrib import messages
from functools import wraps
from settings.models import RolePermission

def manager_access(view_func):
    """
    Legacy decorator - now redirects to admin_access
    Kept for backward compatibility
    """
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.role == 'admin':
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, "Only administrators can access this page.")
                return redirect('/employeeDashboard/')
        return redirect('/')
    return wrapper

def admin_access(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            # Only allow users with admin role
            if request.user.role == 'admin':
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, "Only administrators can access this page.")
                return redirect('/employeeDashboard/')
        return redirect('/')
    return wrapper

def coach_access(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.role == 'coach' or request.user.role == 'admin':
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, "Only coaches and administrators can access this page.")
                return redirect('/employeeDashboard/')
        return redirect('/')
    return wrapper

def cashier_access(view_func):
    @wraps(view_func)
    def wrapper(request, *args, **kwargs):
        if request.user.is_authenticated:
            if request.user.role == 'cashier' or request.user.role == 'admin':
                return view_func(request, *args, **kwargs)
            else:
                messages.error(request, "Only cashiers and administrators can access this page.")
                return redirect('/employeeDashboard/')
        return redirect('/')
    return wrapper

def role_required(allowed_roles=None):
    """
    Decorator that checks if the user has one of the allowed roles.
    Usage: @role_required(allowed_roles=['admin', 'cashier'])
    """
    if allowed_roles is None:
        allowed_roles = []

    # Always add 'admin' to allowed roles for full access
    if 'admin' not in allowed_roles:
        allowed_roles.append('admin')

    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if request.user.is_authenticated:
                # Check if user's role is in the allowed roles
                if request.user.role in allowed_roles:
                    return view_func(request, *args, **kwargs)
                else:
                    messages.error(request, f"Access denied. Required role: {', '.join(allowed_roles)}")
                    return redirect('/employeeDashboard/')
            return redirect('/')
        return wrapper
    return decorator


def module_permission_required(module, required_level='view'):
    """
    Decorator that checks if the user has the required permission level for a module.
    Usage: @module_permission_required(module='member', required_level='edit')

    Args:
        module (str): The module to check permissions for
        required_level (str): The minimum required permission level ('view', 'edit', or 'full')
    """
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            if request.user.is_authenticated:
                # Check if the user has the required permission level for this module
                if RolePermission.has_permission(request.user.role, module, required_level):
                    return view_func(request, *args, **kwargs)
                else:
                    # Get the display name for the module
                    module_display = module.replace('_', ' ').title()

                    # Get the display name for the required level
                    level_display = {
                        'view': 'View',
                        'edit': 'Edit',
                        'full': 'Full'
                    }.get(required_level, required_level.title())

                    # Get the user's current permission level for this module
                    user_permission = RolePermission.get_module_permission(request.user.role, module)

                    if user_permission == 'none':
                        # If user has no access, redirect to dashboard with error message
                        messages.error(request, f"Access denied. You do not have permission to access the {module_display} module.")
                        return redirect('/employeeDashboard/')
                    else:
                        # If user has some access but not enough, show appropriate message
                        messages.error(request, f"Access denied. You need {level_display} permission for the {module_display} module.")

                        # If they at least have view permission, redirect to the module index
                        if user_permission == 'view' and required_level in ['edit', 'full']:
                            # Try to redirect to the module index if possible
                            try:
                                return redirect(f'/{module}/')
                            except:
                                return redirect('/employeeDashboard/')
                        else:
                            return redirect('/employeeDashboard/')
            return redirect('/')
        return wrapper
    return decorator