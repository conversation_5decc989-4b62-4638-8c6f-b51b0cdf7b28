# Import necessary libraries
import os
from pathlib import Path
import environ

# Initialize environ
env = environ.Env()

# Read the .env file
environ.Env.read_env()

# Set the base directory
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = env("SECRET_KEY")

# Set debug mode
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'daphne',  # Add daphne for WebSocket support
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',  # Added for intcomma filter
    'channels',  # Add channels for WebSocket support
    'core',  # Core app for shared functionality
    'members',
    'user',
    'payment',
    'product',
    'paypervisit',
    'payroll',
    'billmanagement',  # Bill Management app
    'financialreport',  # Financial Report app
    'settings',  # Centralized settings app
    'finance',  # Finance app for deposits and withdrawals
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',  # Add LocaleMiddleware for language selection
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'core.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
                'settings.context_processors.settings_processor',
            ],
        },
    },
]

WSGI_APPLICATION = 'core.wsgi.application'
ASGI_APPLICATION = 'core.asgi.application'

# Channels configuration with safe Redis detection
import logging

def get_channel_layers_config():
    """
    Safely configure channel layers with Redis version detection
    """
    try:
        import redis
        # Test Redis connection with short timeout
        r = redis.Redis(host='127.0.0.1', port=6379, db=0, socket_timeout=0.5, socket_connect_timeout=0.5)
        r.ping()

        # Check Redis version
        info = r.info()
        redis_version = info.get('redis_version', '0.0.0')
        major_version = int(redis_version.split('.')[0])

        if major_version >= 5:
            # Redis 5.0+ supports all modern commands
            print(f"✓ Redis {redis_version} detected - using Redis channel layer")
            return {
                'default': {
                    'BACKEND': 'channels_redis.core.RedisChannelLayer',
                    'CONFIG': {
                        "hosts": [('127.0.0.1', 6379)],
                        "capacity": 1500,
                        "expiry": 60,
                    },
                },
            }
        else:
            # Redis 3.x/4.x - use in-memory for WebSocket, Redis for cache only
            print(f"⚠ Redis {redis_version} detected (older version) - using in-memory channel layer for WebSocket compatibility")
            return {
                'default': {
                    'BACKEND': 'channels.layers.InMemoryChannelLayer',
                    'CONFIG': {
                        "capacity": 300,
                        "expiry": 60,
                    },
                },
            }
    except Exception as e:
        # Redis not available or import failed, use in-memory channel layer
        print(f"⚠ Redis not available ({e}) - using in-memory channel layer")
        return {
            'default': {
                'BACKEND': 'channels.layers.InMemoryChannelLayer',
                'CONFIG': {
                    "capacity": 300,
                    "expiry": 60,
                },
            },
        }

CHANNEL_LAYERS = get_channel_layers_config()

# Cache configuration with safe Redis detection
def get_cache_config():
    """
    Safely configure cache backend with Redis detection
    """
    try:
        import redis
        # Test Redis connection with short timeout
        r = redis.Redis(host='127.0.0.1', port=6379, db=1, socket_timeout=0.5, socket_connect_timeout=0.5)
        r.ping()

        # Redis is available, use Redis cache
        print("✓ Redis detected - using Redis cache backend")
        return {
            'default': {
                'BACKEND': 'django_redis.cache.RedisCache',
                'LOCATION': 'redis://127.0.0.1:6379/1',
                'OPTIONS': {
                    'CLIENT_CLASS': 'django_redis.client.DefaultClient',
                    'CONNECTION_POOL_KWARGS': {
                        'max_connections': 50,
                        'retry_on_timeout': True,
                    },
                    'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
                    'IGNORE_EXCEPTIONS': True,
                },
                'KEY_PREFIX': 'legend_fitness_club',
                'TIMEOUT': 1800,  # 30 minutes default timeout
                'VERSION': 1,
            }
        }
    except Exception as e:
        # Redis not available or import failed, use database cache
        print(f"⚠ Redis not available ({e}) - using database cache backend")
        return {
            'default': {
                'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
                'LOCATION': 'cache_table',
                'TIMEOUT': 1800,  # 30 minutes default timeout
                'OPTIONS': {
                    'MAX_ENTRIES': 1000,
                    'CULL_FREQUENCY': 3,
                }
            }
        }

CACHES = get_cache_config()

# Alternative: Real Redis cache configuration (uncomment when Redis server is running)
# CACHES = {
#     'default': {
#         'BACKEND': 'django_redis.cache.RedisCache',
#         'LOCATION': 'redis://127.0.0.1:6379/1',
#         'OPTIONS': {
#             'CLIENT_CLASS': 'django_redis.client.DefaultClient',
#         },
#         'KEY_PREFIX': 'legend_fitness_club',
#         'TIMEOUT': 1800,  # 30 minutes default timeout
#     }
# }

# Fallback: Database cache (if Redis is not available)
# CACHES = {
#     'default': {
#         'BACKEND': 'django.core.cache.backends.db.DatabaseCache',
#         'LOCATION': 'cache_table',
#         'TIMEOUT': 1800,  # 30 minutes default timeout
#         'OPTIONS': {
#             'MAX_ENTRIES': 1000,
#         }
#     }
# }

# Permission cache timeout (in seconds)
PERMISSION_CACHE_TIMEOUT = 1800  # 30 minutes

# Redis configuration settings
REDIS_HOST = '127.0.0.1'
REDIS_PORT = 6379
REDIS_DB_CHANNELS = 0  # Database for channels
REDIS_DB_CACHE = 1     # Database for cache
REDIS_PASSWORD = None  # Set password if Redis requires authentication

# Redis connection pool settings
REDIS_CONNECTION_POOL_KWARGS = {
    'max_connections': 50,
    'retry_on_timeout': True,
    'socket_timeout': 5,
    'socket_connect_timeout': 5,
    'health_check_interval': 30,
}

#MySQL Database
DATABASES = {
    'default': {
        # Server
        # 'ENGINE': 'mysql.connector.django',
        # Local
        'ENGINE': 'django.db.backends.mysql',
        'NAME': env("DB_NAME"),
        'USER': env("DB_USER"),
        'PASSWORD': env("DB_PASSWORD"),
        'HOST': env("DB_HOST"),
        'PORT': env("DB_PORT"),
        "OPTIONS": {
            "sql_mode": "STRICT_ALL_TABLES",
        },
    }
}

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

from django.utils.translation import gettext_lazy as _

LANGUAGE_CODE = 'en-us'

LANGUAGES = [
    ('en', _('English')),
    ('km', _('Khmer')),
]

# Define paths where Django should look for translation files
LOCALE_PATHS = [
    BASE_DIR / 'locale',
]

TIME_ZONE = 'Asia/Dhaka'

USE_I18N = True

USE_TZ = False


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [ BASE_DIR / 'static' ]

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTH_USER_MODEL = 'user.User'
LOGIN_URL = '/'

# For product images
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Product settings
PRODUCT_AUTO_DEACTIVATE_OUT_OF_STOCK = True  # Automatically deactivate products when they go out of stock
PRODUCT_AUTO_REACTIVATE_IN_STOCK = True  # Automatically reactivate products when they come back in stock
PRODUCT_DEFAULT_ITEMS_PER_PAGE = 10  # Default number of products to show per page

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': BASE_DIR / 'logs' / 'permission_manager.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'settings.cache_manager': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'settings.signals': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'core.consumers': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}

# Create logs directory if it doesn't exist
import os
logs_dir = BASE_DIR / 'logs'
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)
