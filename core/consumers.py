import json
import logging
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth import get_user_model
from settings.models import RolePermission
from settings.utils import get_user_permissions

logger = logging.getLogger(__name__)
User = get_user_model()

class PermissionConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for real-time permission updates
    """
    
    async def connect(self):
        """
        Handle WebSocket connection
        """
        # Get user from session
        user = self.scope["user"]
        
        if user.is_authenticated:
            # Add user to their role group for targeted updates
            self.role_group_name = f"role_{user.role}"
            self.user_group_name = f"user_{user.id}"
            
            # Join role group
            await self.channel_layer.group_add(
                self.role_group_name,
                self.channel_name
            )
            
            # Join individual user group
            await self.channel_layer.group_add(
                self.user_group_name,
                self.channel_name
            )
            
            await self.accept()
            
            # Send initial permissions to client
            permissions = await self.get_user_permissions(user)
            await self.send(text_data=json.dumps({
                'type': 'permission_sync',
                'permissions': permissions,
                'user_role': user.role,
                'timestamp': self.get_timestamp()
            }))
            
            logger.info(f"WebSocket connected for user {user.username} (role: {user.role})")
        else:
            await self.close()
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        user = self.scope["user"]
        
        if user.is_authenticated:
            # Leave role group
            await self.channel_layer.group_discard(
                self.role_group_name,
                self.channel_name
            )
            
            # Leave user group
            await self.channel_layer.group_discard(
                self.user_group_name,
                self.channel_name
            )
            
            logger.info(f"WebSocket disconnected for user {user.username}")
    
    async def receive(self, text_data):
        """
        Handle messages from WebSocket
        """
        try:
            text_data_json = json.loads(text_data)
            message_type = text_data_json.get('type')
            
            if message_type == 'permission_check':
                # Client requesting permission check
                user = self.scope["user"]
                permissions = await self.get_user_permissions(user)
                
                await self.send(text_data=json.dumps({
                    'type': 'permission_update',
                    'permissions': permissions,
                    'user_role': user.role,
                    'timestamp': self.get_timestamp()
                }))
                
        except json.JSONDecodeError:
            logger.error("Invalid JSON received in WebSocket")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {str(e)}")
    
    async def permission_update(self, event):
        """
        Handle permission update events from group
        """
        await self.send(text_data=json.dumps({
            'type': 'permission_update',
            'permissions': event['permissions'],
            'user_role': event['user_role'],
            'changes': event.get('changes', {}),
            'message': event.get('message', ''),
            'timestamp': self.get_timestamp()
        }))
    
    async def permission_notification(self, event):
        """
        Handle permission notification events
        """
        await self.send(text_data=json.dumps({
            'type': 'permission_notification',
            'message': event['message'],
            'notification_type': event['notification_type'],
            'changes': event.get('changes', {}),
            'timestamp': self.get_timestamp()
        }))
    
    @database_sync_to_async
    def get_user_permissions(self, user):
        """
        Get user permissions asynchronously
        """
        return get_user_permissions(user)
    
    def get_timestamp(self):
        """
        Get current timestamp
        """
        from django.utils import timezone
        return timezone.now().isoformat()


class SystemNotificationConsumer(AsyncWebsocketConsumer):
    """
    WebSocket consumer for system-wide notifications
    """
    
    async def connect(self):
        """
        Handle WebSocket connection for system notifications
        """
        user = self.scope["user"]
        
        if user.is_authenticated:
            # Add to system notifications group
            await self.channel_layer.group_add(
                "system_notifications",
                self.channel_name
            )
            
            await self.accept()
            logger.info(f"System notification WebSocket connected for user {user.username}")
        else:
            await self.close()
    
    async def disconnect(self, close_code):
        """
        Handle WebSocket disconnection
        """
        user = self.scope["user"]
        
        if user.is_authenticated:
            await self.channel_layer.group_discard(
                "system_notifications",
                self.channel_name
            )
            
            logger.info(f"System notification WebSocket disconnected for user {user.username}")
    
    async def system_notification(self, event):
        """
        Handle system notification events
        """
        await self.send(text_data=json.dumps({
            'type': 'system_notification',
            'message': event['message'],
            'notification_type': event['notification_type'],
            'timestamp': self.get_timestamp()
        }))
    
    def get_timestamp(self):
        """
        Get current timestamp
        """
        from django.utils import timezone
        return timezone.now().isoformat()
