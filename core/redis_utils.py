"""
Redis Utilities for Legend Fitness Club
Provides Redis connection management, health checks, and monitoring
"""

import redis
import logging
from django.conf import settings
from django.core.cache import cache
from channels.layers import get_channel_layer
import time
import json

logger = logging.getLogger(__name__)

class RedisHealthChecker:
    """
    Redis health monitoring and connection management
    """
    
    @staticmethod
    def is_redis_available():
        """
        Check if Redis server is available and responding
        """
        try:
            r = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=0,
                socket_timeout=1,
                password=getattr(settings, 'REDIS_PASSWORD', None)
            )
            response = r.ping()
            return response is True
        except (redis.ConnectionError, redis.TimeoutError, Exception) as e:
            logger.warning(f"Redis health check failed: {e}")
            return False
    
    @staticmethod
    def get_redis_info():
        """
        Get Redis server information and statistics
        """
        try:
            r = redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=0,
                socket_timeout=5,
                password=getattr(settings, 'REDIS_PASSWORD', None)
            )
            
            info = r.info()
            return {
                'available': True,
                'version': info.get('redis_version', 'Unknown'),
                'uptime': info.get('uptime_in_seconds', 0),
                'connected_clients': info.get('connected_clients', 0),
                'used_memory': info.get('used_memory_human', '0B'),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0),
                'hit_rate': RedisHealthChecker._calculate_hit_rate(
                    info.get('keyspace_hits', 0),
                    info.get('keyspace_misses', 0)
                )
            }
        except Exception as e:
            logger.error(f"Failed to get Redis info: {e}")
            return {
                'available': False,
                'error': str(e)
            }
    
    @staticmethod
    def _calculate_hit_rate(hits, misses):
        """
        Calculate cache hit rate percentage
        """
        total = hits + misses
        if total == 0:
            return 0.0
        return round((hits / total) * 100, 2)
    
    @staticmethod
    def test_redis_operations():
        """
        Test basic Redis operations (set, get, delete)
        """
        try:
            test_key = 'redis_health_test'
            test_value = f'test_value_{int(time.time())}'
            
            # Test cache operations
            cache.set(test_key, test_value, 60)
            retrieved_value = cache.get(test_key)
            cache.delete(test_key)
            
            success = retrieved_value == test_value
            
            return {
                'cache_operations': success,
                'set_get_delete': success,
                'error': None if success else 'Value mismatch'
            }
        except Exception as e:
            logger.error(f"Redis operations test failed: {e}")
            return {
                'cache_operations': False,
                'set_get_delete': False,
                'error': str(e)
            }
    
    @staticmethod
    def test_channel_layer():
        """
        Test WebSocket channel layer functionality
        """
        try:
            channel_layer = get_channel_layer()
            if channel_layer is None:
                return {
                    'channel_layer': False,
                    'error': 'Channel layer not configured'
                }
            
            # Check if it's Redis-based
            backend_name = channel_layer.__class__.__name__
            is_redis = 'Redis' in backend_name
            
            return {
                'channel_layer': True,
                'backend': backend_name,
                'is_redis': is_redis,
                'error': None
            }
        except Exception as e:
            logger.error(f"Channel layer test failed: {e}")
            return {
                'channel_layer': False,
                'error': str(e)
            }
    
    @staticmethod
    def get_comprehensive_status():
        """
        Get comprehensive Redis and system status
        """
        redis_available = RedisHealthChecker.is_redis_available()
        
        status = {
            'timestamp': time.time(),
            'redis_available': redis_available,
            'cache_backend': settings.CACHES['default']['BACKEND'],
            'channel_backend': settings.CHANNEL_LAYERS['default']['BACKEND'],
        }
        
        if redis_available:
            status.update(RedisHealthChecker.get_redis_info())
            status.update(RedisHealthChecker.test_redis_operations())
        
        status.update(RedisHealthChecker.test_channel_layer())
        
        return status

class RedisConnectionManager:
    """
    Manages Redis connections with automatic failover
    """
    
    @staticmethod
    def get_redis_client(db=0):
        """
        Get Redis client with connection pooling
        """
        try:
            return redis.Redis(
                host=getattr(settings, 'REDIS_HOST', '127.0.0.1'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=db,
                password=getattr(settings, 'REDIS_PASSWORD', None),
                **getattr(settings, 'REDIS_CONNECTION_POOL_KWARGS', {})
            )
        except Exception as e:
            logger.error(f"Failed to create Redis client: {e}")
            return None
    
    @staticmethod
    def execute_with_fallback(operation, fallback_value=None):
        """
        Execute Redis operation with fallback on failure
        """
        try:
            return operation()
        except Exception as e:
            logger.warning(f"Redis operation failed, using fallback: {e}")
            return fallback_value

def monitor_redis_performance():
    """
    Monitor Redis performance and log metrics
    """
    try:
        status = RedisHealthChecker.get_comprehensive_status()
        
        if status['redis_available']:
            logger.info(
                f"Redis Performance - "
                f"Hit Rate: {status.get('hit_rate', 0)}%, "
                f"Memory: {status.get('used_memory', 'Unknown')}, "
                f"Clients: {status.get('connected_clients', 0)}"
            )
        else:
            logger.warning("Redis not available - using fallback backends")
            
        return status
    except Exception as e:
        logger.error(f"Redis monitoring failed: {e}")
        return {'error': str(e)}

# Utility functions for easy access
def is_redis_available():
    """Quick Redis availability check"""
    return RedisHealthChecker.is_redis_available()

def get_redis_status():
    """Get Redis status information"""
    return RedisHealthChecker.get_comprehensive_status()

def test_redis_connection():
    """Test Redis connection and operations"""
    return RedisHealthChecker.test_redis_operations()
