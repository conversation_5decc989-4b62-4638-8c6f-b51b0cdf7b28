import uuid
import datetime

def generate_unique_transaction_id(prefix="TRX"):
    """
    Generate a unique transaction ID with the format PREFIX-YYYYMMDD-RANDOM
    """
    today = datetime.datetime.now().strftime('%Y%m%d')
    random_str = str(uuid.uuid4()).split('-')[0]
    return f"{prefix}-{today}-{random_str}"

def generate_unique_payroll_id(prefix="PAY"):
    """
    Generate a unique payroll ID with the format PREFIX-YYYYMMDD-RANDOM
    """
    today = datetime.datetime.now().strftime('%Y%m%d')
    random_str = str(uuid.uuid4()).split('-')[0]
    return f"{prefix}-{today}-{random_str}"
