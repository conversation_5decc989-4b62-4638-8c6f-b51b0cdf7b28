"""
Django management command to test Redis connectivity and performance
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from core.redis_utils import RedisHealth<PERSON>he<PERSON>, monitor_redis_performance
import json
import time

class Command(BaseCommand):
    help = 'Test Redis connectivity and performance for Legend Fitness Club'

    def add_arguments(self, parser):
        parser.add_argument(
            '--detailed',
            action='store_true',
            help='Show detailed Redis information',
        )
        parser.add_argument(
            '--monitor',
            action='store_true',
            help='Continuously monitor Redis (Ctrl+C to stop)',
        )
        parser.add_argument(
            '--interval',
            type=int,
            default=5,
            help='Monitoring interval in seconds (default: 5)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Redis Connectivity Test for Legend Fitness Club')
        )
        self.stdout.write('=' * 60)

        if options['monitor']:
            self.monitor_redis(options['interval'])
        else:
            self.test_redis_once(options['detailed'])

    def test_redis_once(self, detailed=False):
        """Run Redis test once"""
        
        # Basic connectivity test
        self.stdout.write('\n1. Testing Redis Connectivity...')
        is_available = RedisHealthChecker.is_redis_available()
        
        if is_available:
            self.stdout.write(
                self.style.SUCCESS('✓ Redis is available and responding')
            )
        else:
            self.stdout.write(
                self.style.ERROR('✗ Redis is not available')
            )
            self.stdout.write(
                self.style.WARNING('System will use fallback backends (database cache, in-memory channels)')
            )

        # Get comprehensive status
        self.stdout.write('\n2. Getting System Status...')
        status = RedisHealthChecker.get_comprehensive_status()
        
        # Display cache backend
        cache_backend = status.get('cache_backend', 'Unknown')
        if 'Redis' in cache_backend:
            self.stdout.write(
                self.style.SUCCESS(f'✓ Cache Backend: {cache_backend}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠ Cache Backend: {cache_backend} (fallback)')
            )

        # Display channel backend
        channel_backend = status.get('channel_backend', 'Unknown')
        if 'Redis' in channel_backend:
            self.stdout.write(
                self.style.SUCCESS(f'✓ Channel Backend: {channel_backend}')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠ Channel Backend: {channel_backend} (fallback)')
            )

        if is_available and detailed:
            self.show_detailed_info(status)

        # Test operations
        self.stdout.write('\n3. Testing Cache Operations...')
        ops_result = RedisHealthChecker.test_redis_operations()
        
        if ops_result['cache_operations']:
            self.stdout.write(
                self.style.SUCCESS('✓ Cache operations (set/get/delete) working')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'✗ Cache operations failed: {ops_result.get("error", "Unknown error")}')
            )

        # Test channel layer
        self.stdout.write('\n4. Testing Channel Layer...')
        channel_result = RedisHealthChecker.test_channel_layer()
        
        if channel_result['channel_layer']:
            backend_type = "Redis-based" if channel_result['is_redis'] else "In-memory"
            self.stdout.write(
                self.style.SUCCESS(f'✓ Channel layer working ({backend_type})')
            )
        else:
            self.stdout.write(
                self.style.ERROR(f'✗ Channel layer failed: {channel_result.get("error", "Unknown error")}')
            )

        # Summary
        self.stdout.write('\n' + '=' * 60)
        if is_available:
            self.stdout.write(
                self.style.SUCCESS('✓ Redis is fully operational!')
            )
            self.stdout.write('Real-time WebSocket features are enabled.')
        else:
            self.stdout.write(
                self.style.WARNING('⚠ Redis not available - using fallback backends')
            )
            self.stdout.write('System will work but with reduced performance.')
            self.stdout.write('\nTo enable Redis:')
            self.stdout.write('1. Install Redis server')
            self.stdout.write('2. Start Redis on 127.0.0.1:6379')
            self.stdout.write('3. Restart Django server')

    def show_detailed_info(self, status):
        """Show detailed Redis information"""
        self.stdout.write('\n📊 Detailed Redis Information:')
        self.stdout.write('-' * 40)
        
        info_items = [
            ('Version', status.get('version', 'Unknown')),
            ('Uptime', f"{status.get('uptime', 0)} seconds"),
            ('Connected Clients', status.get('connected_clients', 0)),
            ('Memory Usage', status.get('used_memory', 'Unknown')),
            ('Commands Processed', status.get('total_commands_processed', 0)),
            ('Cache Hit Rate', f"{status.get('hit_rate', 0)}%"),
        ]
        
        for label, value in info_items:
            self.stdout.write(f'{label:20}: {value}')

    def monitor_redis(self, interval):
        """Continuously monitor Redis"""
        self.stdout.write(f'\n🔍 Monitoring Redis every {interval} seconds...')
        self.stdout.write('Press Ctrl+C to stop\n')
        
        try:
            while True:
                status = monitor_redis_performance()
                
                timestamp = time.strftime('%H:%M:%S')
                if status.get('redis_available'):
                    hit_rate = status.get('hit_rate', 0)
                    memory = status.get('used_memory', 'Unknown')
                    clients = status.get('connected_clients', 0)
                    
                    self.stdout.write(
                        f'[{timestamp}] Redis OK - '
                        f'Hit Rate: {hit_rate}%, '
                        f'Memory: {memory}, '
                        f'Clients: {clients}'
                    )
                else:
                    self.stdout.write(
                        f'[{timestamp}] Redis UNAVAILABLE - using fallback'
                    )
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.stdout.write('\n\nMonitoring stopped.')
            self.stdout.write(
                self.style.SUCCESS('Redis monitoring completed.')
            )
