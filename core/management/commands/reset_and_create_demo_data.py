from django.core.management.base import BaseCommand
from django.db import transaction, connection
from django.utils import timezone
from django.contrib.auth import get_user_model
import random
from datetime import datetime, timedelta
import pytz

# Import all models
from user.models import User, MetaData
from members.models import Package, Member
from product.models import Category, Product, Supplier, Purchase, PurchaseItem, Sale, SaleItem
from paypervisit.models import PayPerVisit, PayPerVisitSettings
from payment.models import Payment, PaymentTemplate
from payroll.models import SalaryPayment
from finance.models import Transaction as FinanceTransaction
from billmanagement.models import Bill
from settings.models import Settings


class Command(BaseCommand):
    help = 'Reset all data and create comprehensive demo data for Legend Fitness Club'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to reset all data and create demo data',
        )
        parser.add_argument(
            '--preserve-users',
            nargs='+',
            default=['developer', 'owner'],
            help='List of usernames to preserve during data reset (default: developer, owner)',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will DELETE ALL DATA except specified users and create demo data.\n'
                    'Use --confirm to proceed.\n'
                    f'Users to preserve: {", ".join(options["preserve_users"])}'
                )
            )
            return

        self.stdout.write(
            self.style.SUCCESS('=== Legend Fitness Club - Data Reset & Demo Creation ===')
        )

        try:
            with transaction.atomic():
                # Step 1: Clean existing data
                self.clean_all_data(options['preserve_users'])

                # Step 2: Create demo data
                self.create_demo_data()

                self.stdout.write(
                    self.style.SUCCESS('\n✅ Data reset and demo creation completed successfully!')
                )

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error during data reset and demo creation: {str(e)}')
            )
            raise

    def clean_all_data(self, preserve_users):
        """Clean all existing data while preserving specified users"""
        self.stdout.write('\n🧹 Cleaning existing data...')

        # Clean legacy transaction tables
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM transaction_payrollrecord")
            cursor.execute("DELETE FROM transaction_transaction")

        # Clean all data from all apps
        models_to_clean = [
            ('payment.models', 'Payment'),
            ('members.models', 'Member'),
            ('members.models', 'Package'),
            ('product.models', 'Sale'),
            ('product.models', 'SaleItem'),
            ('product.models', 'Purchase'),
            ('product.models', 'PurchaseItem'),
            ('product.models', 'Product'),
            ('product.models', 'Category'),
            ('product.models', 'Supplier'),
            ('paypervisit.models', 'PayPerVisit'),
            ('payroll.models', 'SalaryPayment'),
            ('billmanagement.models', 'Bill'),
            ('finance.models', 'Transaction'),
        ]

        total_deleted = 0
        for module_path, model_name in models_to_clean:
            try:
                module = __import__(module_path, fromlist=[model_name])
                model_class = getattr(module, model_name)
                count = model_class.objects.count()
                model_class.objects.all().delete()
                total_deleted += count
                self.stdout.write(f'  - Deleted {count} {model_name} records')
            except Exception as e:
                self.stdout.write(f'  - Warning: Could not clean {model_name}: {str(e)}')

        # Clean users except preserved ones
        users_to_delete = User.objects.exclude(username__in=preserve_users)
        user_count = users_to_delete.count()
        users_to_delete.delete()
        self.stdout.write(f'  - Deleted {user_count} users (preserved: {", ".join(preserve_users)})')

        # Reset MetaData
        meta = MetaData.objects.first()
        if meta:
            meta.funds = 0
            meta.save()
            self.stdout.write('  - Reset financial metadata')

        self.stdout.write(f'✅ Cleaned {total_deleted + user_count} total records')

    def create_demo_data(self):
        """Create comprehensive demo data"""
        self.stdout.write('\n🏗️  Creating demo data...')

        # Initialize system components
        self.init_settings()
        self.init_metadata()

        # Create users and employees
        self.create_users()

        # Create membership packages
        self.create_packages()

        # Create members
        self.create_members()

        # Create products and suppliers
        self.create_products_and_suppliers()

        # Create financial data
        self.create_purchases_and_sales()
        self.create_paypervisit_transactions()
        self.create_member_payments()
        self.create_salary_payments()
        self.create_finance_transactions()
        self.create_bills()

        # Print summary
        self.print_summary()

    def init_settings(self):
        """Initialize system settings"""
        settings, created = Settings.objects.get_or_create(
            id=1,
            defaults={
                'gym_name': 'Legend Fitness Club',
                'contact_email': '<EMAIL>',
                'contact_phone': '012 345 678',
                'address': '#123, Street 271, Chamkar Mon, Phnom Penh, Cambodia',
                'auto_deactivate_out_of_stock': True,
                'auto_reactivate_in_stock': True,
                'default_items_per_page': 10,
                'paypervisit_price_per_person': 4000,
                'paypervisit_quick_select_1': 2,
                'paypervisit_quick_select_2': 5,
                'paypervisit_quick_select_3': 10,
                'paypervisit_custom_price_1': 8000,
                'paypervisit_custom_price_2': 20000,
                'paypervisit_custom_price_3': 40000,
                'notification_success_color': '#065f46',
                'notification_error_color': '#b91c1c',
                'notification_warning_color': '#b45309',
                'notification_info_color': '#1e3a8a',
                'exchange_rate_usd_to_khr': 4000,
                'last_data_cleanup': timezone.now(),
            }
        )
        if created:
            self.stdout.write('  - Initialized system settings')

    def init_metadata(self):
        """Initialize metadata"""
        metadata, created = MetaData.objects.get_or_create(
            id=1,
            defaults={
                'funds': 0,
                'lastChecked': timezone.now().date(),
            }
        )
        if created:
            self.stdout.write('  - Initialized metadata')

    def generate_phone(self):
        """Generate a random Cambodian phone number"""
        prefixes = ['010', '012', '015', '016', '017', '069', '070', '077', '078', '085', '086', '087', '092', '093', '095', '096', '097', '098']
        return f"{random.choice(prefixes)} {random.randint(100, 999)} {random.randint(100, 999)}"

    def generate_transaction_id(self, prefix):
        """Generate a unique transaction ID"""
        return f"{prefix}-{random.randint(10000, 99999)}"

    def generate_cambodian_address(self):
        """Generate a realistic Cambodian address"""
        districts = [
            'Chamkar Mon', 'Doun Penh', 'Prampir Meakkakra', 'Tuol Kouk', 'Dangkao', 'Mean Chey',
            'Russey Keo', 'Sen Sok', 'Pou Senchey', 'Chroy Changvar', 'Prek Pnov', 'Chbar Ampov'
        ]

        street_types = ['Street', 'St.', 'Road', 'Blvd']
        house_num = random.randint(1, 999)
        street_num = random.randint(1, 999)
        district = random.choice(districts)
        street_type = random.choice(street_types)

        return f"#{house_num}, {street_type} {street_num}, {district}, Phnom Penh"

    def get_cambodian_names(self):
        """Get authentic Cambodian names"""
        male_first_names = [
            'Dara', 'Sokha', 'Veasna', 'Kosal', 'Makara', 'Chamroeun', 'Vannak', 'Rithy', 'Pisach', 'Thida',
            'Sophea', 'Ratana', 'Pich', 'Sokhom', 'Kunthea', 'Virak', 'Samnang', 'Bunroeun', 'Chenda', 'Keo'
        ]

        female_first_names = [
            'Bopha', 'Srey', 'Sopheap', 'Chenda', 'Phalla', 'Sothea', 'Chantha', 'Thida', 'Sreypov', 'Channary',
            'Devi', 'Kanha', 'Leap', 'Mealea', 'Neary', 'Pheakdey', 'Rachana', 'Socheat', 'Tevy', 'Vanna'
        ]

        last_names = [
            'Sok', 'Chhay', 'Meas', 'Prak', 'Tep', 'Yong', 'Khun', 'Keo', 'Chea', 'Pov',
            'Heng', 'Seng', 'Nhem', 'Vann', 'Leng', 'Mao', 'Ung', 'Yin', 'Lim', 'Chum'
        ]

        return male_first_names, female_first_names, last_names

    def create_users(self):
        """Create 4 employees with different roles"""
        self.stdout.write('\n👥 Creating employees...')

        roles = [
            {
                'username': 'coach1',
                'first_name': 'Sokha',
                'last_name': 'Meas',
                'role': 'coach',
                'salary': 900000,  # 900,000 KHR
                'is_manager': False,
                'is_employee': True,
                'gender': 'male'
            },
            {
                'username': 'cashier1',
                'first_name': 'Bopha',
                'last_name': 'Chhay',
                'role': 'cashier',
                'salary': 800000,  # 800,000 KHR
                'is_manager': False,
                'is_employee': True,
                'gender': 'female'
            },
            {
                'username': 'cashier2',
                'first_name': 'Sreypov',
                'last_name': 'Prak',
                'role': 'cashier',
                'salary': 800000,  # 800,000 KHR
                'is_manager': False,
                'is_employee': True,
                'gender': 'female'
            },
            {
                'username': 'cleaner1',
                'first_name': 'Srey',
                'last_name': 'Tep',
                'role': 'cleaner',
                'salary': 600000,  # 600,000 KHR
                'is_manager': False,
                'is_employee': True,
                'gender': 'female'
            }
        ]

        for i, role_data in enumerate(roles, 1):
            if not User.objects.filter(username=role_data['username']).exists():
                full_name = f"{role_data['first_name']} {role_data['last_name']}"
                user = User.objects.create_user(
                    username=role_data['username'],
                    password='password123',
                    email=f"{role_data['username']}@legendfitness.com",
                    name=full_name,
                    phone=self.generate_phone(),
                    role=role_data['role'],
                    is_manager=role_data['is_manager'],
                    is_employee=role_data['is_employee'],
                    salary=role_data['salary'],
                    join_date=timezone.now().date() - timedelta(days=random.randint(30, 300)),
                    gender=role_data['gender'],
                    emp_id=f"LFC-{i:04d}",
                    address=self.generate_cambodian_address(),
                    dob=datetime(random.randint(1985, 2000), random.randint(1, 12), random.randint(1, 28)).date()
                )
                self.stdout.write(f'  - Created {role_data["role"]}: {user.username} ({full_name})')

        self.stdout.write(f'✅ Created {len(roles)} employees')

    def create_packages(self):
        """Create membership packages"""
        self.stdout.write('\n📦 Creating membership packages...')

        packages = [
            {
                'package_id': 'PKG-001',
                'name': 'Basic',
                'duration': 1,
                'price_khr': 120000,  # 120,000 KHR
                'price_usd': 30,
                'access_type': 'peak_only',
                'description': 'Basic package with access during peak hours only.'
            },
            {
                'package_id': 'PKG-002',
                'name': 'Premium',
                'duration': 3,
                'price_khr': 300000,  # 300,000 KHR
                'price_usd': 75,
                'access_type': 'all_hours',
                'description': 'Premium package with all-hours access and 1 free PT session.'
            },
            {
                'package_id': 'PKG-003',
                'name': 'VIP',
                'duration': 6,
                'price_khr': 550000,  # 550,000 KHR
                'price_usd': 137.5,
                'access_type': 'all_hours',
                'description': 'VIP package with all-hours access, locker, and 3 free PT sessions.'
            },
            {
                'package_id': 'PKG-004',
                'name': 'Super VIP',
                'duration': 12,
                'price_khr': 1000000,  # 1,000,000 KHR
                'price_usd': 250,
                'access_type': 'all_hours',
                'description': 'Super VIP package with all-hours access, locker, and 10 free PT sessions.'
            }
        ]

        for package_data in packages:
            Package.objects.get_or_create(
                package_id=package_data['package_id'],
                defaults=package_data
            )
            self.stdout.write(f'  - Created package: {package_data["name"]} ({package_data["duration"]} months)')

        self.stdout.write(f'✅ Created {len(packages)} packages')

    def create_members(self):
        """Create 4-6 members with varied membership statuses"""
        self.stdout.write('\n🏃 Creating members...')

        packages = Package.objects.all()
        if not packages:
            self.stdout.write('  - Warning: No packages found. Please create packages first.')
            return

        male_names, female_names, last_names = self.get_cambodian_names()

        # Create 5 members with varied membership statuses
        for i in range(1, 6):
            # Determine gender and select appropriate name
            gender = random.choice(['male', 'female'])
            if gender == 'male':
                first_name = random.choice(male_names)
            else:
                first_name = random.choice(female_names)

            last_name = random.choice(last_names)
            name = f"{first_name} {last_name}"

            # Generate member ID
            member_id = f"M-{1000 + i}"

            # Assign a random package with weighted distribution (more basic packages)
            package_weights = [0.4, 0.3, 0.2, 0.1]  # Basic, Premium, VIP, Super VIP
            package = random.choices(list(packages), weights=package_weights)[0]

            # Calculate start and end dates with varied scenarios
            if i <= 2:  # Active members
                start_date = timezone.now().date() - timedelta(days=random.randint(0, 30))
                payment_status = 'paid'
            elif i <= 4:  # Some near expiration
                start_date = timezone.now().date() - timedelta(days=random.randint(60, 90))
                payment_status = random.choice(['paid', 'pending'])
            else:  # Some expired or new
                start_date = timezone.now().date() - timedelta(days=random.randint(0, 120))
                payment_status = random.choice(['paid', 'pending', 'overdue'])

            end_date = start_date + timedelta(days=package.duration * 30)

            # Generate realistic discount (20% get discounts)
            discount = random.choice([0, 0, 0, 0, 10000, 20000])

            # Create the member if it doesn't exist
            if not Member.objects.filter(member_id=member_id).exists():
                member = Member.objects.create(
                    member_id=member_id,
                    name=name,
                    gender=gender,
                    dob=datetime(random.randint(1985, 2005), random.randint(1, 12), random.randint(1, 28)).date(),
                    contact=self.generate_phone(),
                    telegram=f"@{first_name.lower()}{random.randint(100, 999)}",
                    address=self.generate_cambodian_address(),
                    package=package,
                    start_date=start_date,
                    end_date=end_date,
                    payment_status=payment_status,
                    discount=discount,
                    due_payment=0 if payment_status == 'paid' else (package.price_khr - discount)
                )

                # Print status for tracking
                status = "Active"
                if member.end_date < timezone.now().date():
                    status = "Expired"
                elif member.is_expiring_soon:
                    status = "Expiring Soon"

                self.stdout.write(f'  - Created member: {name} with {package.name} package ({status})')

        self.stdout.write('✅ Created 5 members')

    def create_products_and_suppliers(self):
        """Create product categories, suppliers, and 10 products"""
        self.stdout.write('\n🛍️  Creating products and suppliers...')

        # Create categories
        categories = [
            {'name': 'Beverages', 'description': 'Drinks and refreshments'},
            {'name': 'Snacks', 'description': 'Healthy snacks and energy bars'},
            {'name': 'Supplements', 'description': 'Protein powders and supplements'},
            {'name': 'Merchandise', 'description': 'Gym branded merchandise'}
        ]

        for category_data in categories:
            Category.objects.get_or_create(
                name=category_data['name'],
                defaults=category_data
            )

        # Get the categories
        beverage_category = Category.objects.get(name='Beverages')
        snack_category = Category.objects.get(name='Snacks')
        supplement_category = Category.objects.get(name='Supplements')
        merchandise_category = Category.objects.get(name='Merchandise')

        # Create suppliers
        suppliers = [
            {
                'name': 'ABC Beverages',
                'phone': self.generate_phone(),
                'telegram': '@abcbeverages',
                'address': '#123, St. 271, Phnom Penh',
                'note': 'Main supplier for all beverages'
            },
            {
                'name': 'HealthySnacks Co.',
                'phone': self.generate_phone(),
                'telegram': '@healthysnacks',
                'address': '#45, St. 310, Phnom Penh',
                'note': 'Supplies protein bars and healthy snacks'
            },
            {
                'name': 'Supplement World',
                'phone': self.generate_phone(),
                'telegram': '@supplementworld',
                'address': '#78, St. 163, Phnom Penh',
                'note': 'Premium supplements and protein powders'
            },
            {
                'name': 'Fitness Gear',
                'phone': self.generate_phone(),
                'telegram': '@fitnessgear',
                'address': '#92, St. 214, Phnom Penh',
                'note': 'Gym merchandise and branded items'
            }
        ]

        for supplier_data in suppliers:
            Supplier.objects.get_or_create(
                name=supplier_data['name'],
                defaults=supplier_data
            )

        # Create 10 products with proper KHR amounts (increments of 100)
        products = [
            {
                'name': 'Mineral Water',
                'sku': 'BEV-001',
                'category': beverage_category,
                'cost_price': 1500,  # 1,500 KHR
                'retail_price': 2000,  # 2,000 KHR
                'quantity': 50,
                'description': '500ml bottled water',
                'box_quantity': 24,
                'box_cost': 36000  # 36,000 KHR
            },
            {
                'name': 'Sports Drink',
                'sku': 'BEV-002',
                'category': beverage_category,
                'cost_price': 4000,  # 4,000 KHR
                'retail_price': 5000,  # 5,000 KHR
                'quantity': 30,
                'description': 'Electrolyte-enhanced sports drink',
                'box_quantity': 12,
                'box_cost': 48000  # 48,000 KHR
            },
            {
                'name': 'Protein Bar',
                'sku': 'SNK-001',
                'category': snack_category,
                'cost_price': 6000,  # 6,000 KHR
                'retail_price': 8000,  # 8,000 KHR
                'quantity': 25,
                'description': '20g protein bar',
                'box_quantity': 12,
                'box_cost': 72000  # 72,000 KHR
            },
            {
                'name': 'Energy Bar',
                'sku': 'SNK-002',
                'category': snack_category,
                'cost_price': 5000,  # 5,000 KHR
                'retail_price': 7000,  # 7,000 KHR
                'quantity': 20,
                'description': 'High-energy snack bar',
                'box_quantity': 24,
                'box_cost': 120000  # 120,000 KHR
            },
            {
                'name': 'Whey Protein',
                'sku': 'SUP-001',
                'category': supplement_category,
                'cost_price': 120000,  # 120,000 KHR
                'retail_price': 150000,  # 150,000 KHR
                'quantity': 10,
                'description': '1kg whey protein powder',
                'box_quantity': 6,
                'box_cost': 720000  # 720,000 KHR
            },
            {
                'name': 'BCAA Supplement',
                'sku': 'SUP-002',
                'category': supplement_category,
                'cost_price': 80000,  # 80,000 KHR
                'retail_price': 100000,  # 100,000 KHR
                'quantity': 8,
                'description': 'Branched-chain amino acids supplement',
                'box_quantity': 12,
                'box_cost': 960000  # 960,000 KHR
            },
            {
                'name': 'Gym T-Shirt',
                'sku': 'MER-001',
                'category': merchandise_category,
                'cost_price': 30000,  # 30,000 KHR
                'retail_price': 40000,  # 40,000 KHR
                'quantity': 15,
                'description': 'Legend Fitness branded t-shirt',
                'box_quantity': 10,
                'box_cost': 300000  # 300,000 KHR
            },
            {
                'name': 'Gym Towel',
                'sku': 'MER-002',
                'category': merchandise_category,
                'cost_price': 18000,  # 18,000 KHR
                'retail_price': 25000,  # 25,000 KHR
                'quantity': 20,
                'description': 'Legend Fitness branded towel',
                'box_quantity': 20,
                'box_cost': 360000  # 360,000 KHR
            },
            {
                'name': 'Creatine Monohydrate',
                'sku': 'SUP-003',
                'category': supplement_category,
                'cost_price': 60000,  # 60,000 KHR
                'retail_price': 80000,  # 80,000 KHR
                'quantity': 12,
                'description': '300g creatine monohydrate powder',
                'box_quantity': 8,
                'box_cost': 480000  # 480,000 KHR
            },
            {
                'name': 'Gym Water Bottle',
                'sku': 'MER-003',
                'category': merchandise_category,
                'cost_price': 25000,  # 25,000 KHR
                'retail_price': 35000,  # 35,000 KHR
                'quantity': 18,
                'description': 'Legend Fitness branded 750ml water bottle',
                'box_quantity': 12,
                'box_cost': 300000  # 300,000 KHR
            }
        ]

        for product_data in products:
            Product.objects.get_or_create(
                sku=product_data['sku'],
                defaults=product_data
            )
            self.stdout.write(f'  - Created product: {product_data["name"]} ({product_data["sku"]})')

        self.stdout.write(f'✅ Created {len(categories)} categories, {len(suppliers)} suppliers, and {len(products)} products')

    def create_purchases_and_sales(self):
        """Create sample purchases and sales"""
        self.stdout.write('\n💰 Creating purchases and sales...')

        # Get required data
        products = Product.objects.all()
        suppliers = Supplier.objects.all()
        cashiers = User.objects.filter(role='cashier')

        if not products or not suppliers or not cashiers:
            self.stdout.write('  - Warning: Missing required data for purchases/sales')
            return

        # Create 2 purchases
        for i in range(2):
            purchase = Purchase.objects.create(
                trxId=self.generate_transaction_id("PUR"),
                supplier=random.choice(suppliers),
                date=timezone.now(),
                total_amount=0,  # Will be calculated
                payment_method=random.choice(['cash', 'bank', 'other']),
                created_by=random.choice(cashiers),
                notes=f"Sample purchase #{i+1}"
            )

            # Add 2-3 items to each purchase
            total_amount = 0
            for j in range(random.randint(2, 3)):
                product = random.choice(products)
                quantity = random.randint(1, 3)

                PurchaseItem.objects.create(
                    purchase=purchase,
                    product=product,
                    supplier=purchase.supplier,
                    quantity=quantity,
                    cost_price=product.cost_price,
                    is_box_purchase=False,
                    box_quantity=product.box_quantity,
                    original_quantity=quantity
                )
                total_amount += product.cost_price * quantity

            # Update purchase total
            purchase.total_amount = total_amount
            purchase.save()

            self.stdout.write(f'  - Created purchase: {purchase.trxId} ({total_amount:,}៛)')

        # Create 3 sales
        for i in range(3):
            sale = Sale.objects.create(
                trxId=self.generate_transaction_id("SALE"),
                total_amount=0,  # Will be calculated
                payment_method=random.choice(['cash', 'bank', 'other']),
                sold_by=random.choice(cashiers),
                notes=f"Sample sale #{i+1}"
            )

            # Add 1-2 items to each sale
            total_amount = 0
            for j in range(random.randint(1, 2)):
                product = random.choice(products)
                quantity = random.randint(1, 2)

                SaleItem.objects.create(
                    sale=sale,
                    product=product,
                    quantity=quantity,
                    price=product.retail_price,
                    is_box_equivalent=False,
                    box_quantity=product.box_quantity
                )
                total_amount += product.retail_price * quantity

            # Update sale total
            sale.total_amount = total_amount
            sale.save()

            self.stdout.write(f'  - Created sale: {sale.trxId} ({total_amount:,}៛)')

        self.stdout.write('✅ Created 2 purchases and 3 sales')

    def create_paypervisit_transactions(self):
        """Create pay-per-visit transactions"""
        self.stdout.write('\n🚶 Creating pay-per-visit transactions...')

        cashiers = User.objects.filter(role='cashier')
        if not cashiers:
            self.stdout.write('  - Warning: No cashiers found')
            return

        # Create 3 pay-per-visit transactions
        for i in range(3):
            num_people = random.randint(1, 5)
            amount = num_people * 4000  # 4,000 KHR per person

            PayPerVisit.objects.create(
                trxId=self.generate_transaction_id("PPV"),
                amount=amount,
                num_people=num_people,
                payment_method=random.choice(['cash', 'bank', 'other']),
                received_by=random.choice(cashiers)
            )

            self.stdout.write(f'  - Created pay-per-visit: {num_people} people ({amount:,}៛)')

        self.stdout.write('✅ Created 3 pay-per-visit transactions')

    def create_member_payments(self):
        """Create member payments"""
        self.stdout.write('\n💳 Creating member payments...')

        members = Member.objects.filter(payment_status='paid')
        cashiers = User.objects.filter(role='cashier')

        if not members or not cashiers:
            self.stdout.write('  - Warning: No paid members or cashiers found')
            return

        # Create payments for paid members
        for member in members:
            if member.package:
                payment = Payment.objects.create(
                    invoice_no=self.generate_transaction_id("INV"),
                    member=member,
                    amount_khr=member.package.price_khr - member.discount,
                    amount_usd=member.package.price_usd,
                    payment_method=random.choice(['cash', 'bank', 'other']),
                    collector=random.choice(cashiers),
                    notes=f"Payment for {member.package.name} package"
                )

                self.stdout.write(f'  - Created payment: {member.name} ({payment.amount_khr:,}៛)')

        self.stdout.write(f'✅ Created {members.count()} member payments')

    def create_salary_payments(self):
        """Create salary payments for employees"""
        self.stdout.write('\n💼 Creating salary payments...')

        employees = User.objects.filter(is_employee=True, salary__isnull=False)
        admin_users = User.objects.filter(role='admin')

        if not employees:
            self.stdout.write('  - Warning: No employees with salary found')
            return

        # Create salary payments for current month
        for employee in employees:
            if employee.salary:
                SalaryPayment.objects.create(
                    payroll_id=self.generate_transaction_id("PAY"),
                    employee=employee,
                    month=timezone.now().date().replace(day=1),
                    base_salary=employee.salary,
                    bonus=0,
                    deduction=0,
                    overtime_hours=0,
                    final_pay=employee.salary,
                    payment_method=random.choice(['cash', 'bank', 'other']),
                    payment_status='paid',
                    employment_type='full_time',
                    notes=f"Salary payment for {timezone.now().strftime('%B %Y')}"
                )

                self.stdout.write(f'  - Created salary payment: {employee.name} ({employee.salary:,}៛)')

        self.stdout.write(f'✅ Created {employees.count()} salary payments')

    def create_finance_transactions(self):
        """Create finance transactions (deposits/withdrawals)"""
        self.stdout.write('\n🏦 Creating finance transactions...')

        admin_users = User.objects.filter(role='admin')
        if not admin_users:
            self.stdout.write('  - Warning: No admin users found')
            return

        # Create 2 deposits and 1 withdrawal
        transactions = [
            {
                'type': 'deposit',
                'amount': 2000000,  # 2,000,000 KHR
                'notes': 'Initial gym fund deposit'
            },
            {
                'type': 'deposit',
                'amount': 1500000,  # 1,500,000 KHR
                'notes': 'Monthly revenue deposit'
            },
            {
                'type': 'withdrawal',
                'amount': 500000,  # 500,000 KHR
                'notes': 'Equipment maintenance expense'
            }
        ]

        for txn_data in transactions:
            transaction = FinanceTransaction.objects.create(
                transaction_type=txn_data['type'],
                amount_khr=txn_data['amount'],
                amount_usd=round(txn_data['amount'] / 4000, 2),
                payment_method=random.choice(['cash', 'bank', 'other']),
                notes=txn_data['notes'],
                staff=admin_users.first()
            )

            self.stdout.write(f'  - Created {txn_data["type"]}: {transaction.transaction_id} ({txn_data["amount"]:,}៛)')

        self.stdout.write('✅ Created 3 finance transactions')

    def create_bills(self):
        """Create bills for gym expenses"""
        self.stdout.write('\n📄 Creating bills...')

        admin_users = User.objects.filter(role='admin')
        if not admin_users:
            self.stdout.write('  - Warning: No admin users found')
            return

        # Create 3 bills for current month
        bills = [
            {
                'category': 'electricity',
                'provider': 'EDC Cambodia',
                'amount_khr': 1200000,  # 1,200,000 KHR
                'description': 'Monthly electricity bill'
            },
            {
                'category': 'water',
                'provider': 'PPWSA',
                'amount_khr': 300000,  # 300,000 KHR
                'description': 'Monthly water bill'
            },
            {
                'category': 'rent',
                'provider': 'Building Owner',
                'amount_khr': 6000000,  # 6,000,000 KHR
                'description': 'Monthly gym space rent'
            }
        ]

        for bill_data in bills:
            bill = Bill.objects.create(
                category=bill_data['category'],
                provider=bill_data['provider'],
                description=bill_data['description'],
                month_year=timezone.now().date().replace(day=1),
                amount_khr=bill_data['amount_khr'],
                amount_usd=round(bill_data['amount_khr'] / 4000, 2),
                payment_method=random.choice(['cash', 'bank', 'other']),
                payment_status='paid',
                paid_by=admin_users.first(),
                notes=f"Monthly {bill_data['category']} expense"
            )

            self.stdout.write(f'  - Created bill: {bill_data["category"]} - {bill_data["provider"]} ({bill_data["amount_khr"]:,}៛)')

        self.stdout.write('✅ Created 3 bills')

    def print_summary(self):
        """Print summary of created demo data"""
        self.stdout.write('\n📊 Demo Data Summary:')
        self.stdout.write('=' * 50)

        # Count all created records
        summary_data = [
            ('Users/Employees', User.objects.count()),
            ('Members', Member.objects.count()),
            ('Packages', Package.objects.count()),
            ('Products', Product.objects.count()),
            ('Suppliers', Supplier.objects.count()),
            ('Categories', Category.objects.count()),
            ('Purchases', Purchase.objects.count()),
            ('Sales', Sale.objects.count()),
            ('Pay-per-visit', PayPerVisit.objects.count()),
            ('Member Payments', Payment.objects.count()),
            ('Salary Payments', SalaryPayment.objects.count()),
            ('Finance Transactions', FinanceTransaction.objects.count()),
            ('Bills', Bill.objects.count()),
        ]

        for item_name, count in summary_data:
            self.stdout.write(f'  {item_name:<20}: {count:>3}')

        # Show financial summary
        metadata = MetaData.objects.first()
        if metadata:
            self.stdout.write(f'\n💰 Current Gym Funds: {metadata.funds:,}៛')

        self.stdout.write('\n🎯 Demo data creation completed successfully!')
        self.stdout.write('   All amounts follow Cambodian currency conventions (KHR in increments of 100)')
        self.stdout.write('   Login credentials for demo users: password123')
