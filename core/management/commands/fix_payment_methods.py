from django.core.management.base import BaseCommand
from django.db import transaction
from payment.models import Payment
from product.models import Sale
from paypervisit.models import PayPerVisit
from finance.models import Transaction as FinanceTransaction


class Command(BaseCommand):
    help = 'Fix legacy payment method values to match current model choices'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making actual changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Payment method mapping from old values to new values
        payment_method_mapping = {
            'aba': 'bank',
            'wing': 'bank', 
            'card': 'bank',
            'cash': 'cash',
            'bank': 'bank',
            'other': 'other'
        }
        
        total_updated = 0
        
        with transaction.atomic():
            # Fix Payment model
            self.stdout.write('Checking Payment model...')
            payment_updates = 0
            for old_method, new_method in payment_method_mapping.items():
                payments = Payment.objects.filter(payment_method=old_method)
                count = payments.count()
                if count > 0:
                    self.stdout.write(f'  Found {count} payments with method "{old_method}" -> "{new_method}"')
                    if not dry_run:
                        payments.update(payment_method=new_method)
                    payment_updates += count
            
            # Fix Sale model
            self.stdout.write('Checking Sale model...')
            sale_updates = 0
            for old_method, new_method in payment_method_mapping.items():
                sales = Sale.objects.filter(payment_method=old_method)
                count = sales.count()
                if count > 0:
                    self.stdout.write(f'  Found {count} sales with method "{old_method}" -> "{new_method}"')
                    if not dry_run:
                        sales.update(payment_method=new_method)
                    sale_updates += count
            
            # Fix PayPerVisit model (should already be correct)
            self.stdout.write('Checking PayPerVisit model...')
            ppv_updates = 0
            for old_method, new_method in payment_method_mapping.items():
                ppv_records = PayPerVisit.objects.filter(payment_method=old_method)
                count = ppv_records.count()
                if count > 0:
                    self.stdout.write(f'  Found {count} pay-per-visit records with method "{old_method}" -> "{new_method}"')
                    if not dry_run:
                        ppv_records.update(payment_method=new_method)
                    ppv_updates += count
            
            # Fix Transaction model (should already be correct)
            self.stdout.write('Checking Transaction model...')
            trans_updates = 0
            for old_method, new_method in payment_method_mapping.items():
                transactions = FinanceTransaction.objects.filter(payment_method=old_method)
                count = transactions.count()
                if count > 0:
                    self.stdout.write(f'  Found {count} transactions with method "{old_method}" -> "{new_method}"')
                    if not dry_run:
                        transactions.update(payment_method=new_method)
                    trans_updates += count
            
            total_updated = payment_updates + sale_updates + ppv_updates + trans_updates
            
            if dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'DRY RUN: Would update {total_updated} records total:\n'
                        f'  - Payments: {payment_updates}\n'
                        f'  - Sales: {sale_updates}\n'
                        f'  - Pay-per-visit: {ppv_updates}\n'
                        f'  - Transactions: {trans_updates}'
                    )
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully updated {total_updated} records:\n'
                        f'  - Payments: {payment_updates}\n'
                        f'  - Sales: {sale_updates}\n'
                        f'  - Pay-per-visit: {ppv_updates}\n'
                        f'  - Transactions: {trans_updates}'
                    )
                )
