from django.core.management.base import BaseCommand
from django.db import connection

class Command(BaseCommand):
    help = 'Fix migration issues related to coach app removal'

    def handle(self, *args, **options):
        with connection.cursor() as cursor:
            # Drop foreign key constraints first
            self.stdout.write(self.style.WARNING('Dropping foreign key constraints...'))
            try:
                cursor.execute("""
                    ALTER TABLE transaction_transaction
                    DROP FOREIGN KEY transaction_transaction_coach_id_6fbb85b6_fk_coaches_coach_id
                """)
                self.stdout.write(self.style.SUCCESS('Successfully dropped foreign key constraint for transaction_transaction'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error dropping foreign key constraint: {str(e)}'))

            try:
                cursor.execute("""
                    ALTER TABLE transaction_payrollrecord
                    DROP FOREIGN KEY transaction_payrollrecord_coach_id_11e36f38_fk_coaches_coach_id
                """)
                self.stdout.write(self.style.SUCCESS('Successfully dropped foreign key constraint for transaction_payrollrecord'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error dropping foreign key constraint: {str(e)}'))

            try:
                cursor.execute("""
                    ALTER TABLE attendance_attendance
                    DROP FOREIGN KEY attendance_attendance_coach_id_7a8909cc_fk_coaches_coach_id
                """)
                self.stdout.write(self.style.SUCCESS('Successfully dropped foreign key constraint for attendance_attendance'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error dropping foreign key constraint: {str(e)}'))

            # Check if coach_id column exists in transaction_transaction table
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name='transaction_transaction' AND column_name='coach_id'
            """)
            if cursor.fetchone()[0] > 0:
                self.stdout.write(self.style.WARNING('Removing coach_id from transaction_transaction table...'))
                cursor.execute("ALTER TABLE transaction_transaction DROP COLUMN coach_id")
                self.stdout.write(self.style.SUCCESS('Successfully removed coach_id from transaction_transaction table'))
            else:
                self.stdout.write(self.style.SUCCESS('coach_id column does not exist in transaction_transaction table'))

            # Check if coach_id column exists in transaction_payrollrecord table
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name='transaction_payrollrecord' AND column_name='coach_id'
            """)
            if cursor.fetchone()[0] > 0:
                self.stdout.write(self.style.WARNING('Removing coach_id from transaction_payrollrecord table...'))
                cursor.execute("ALTER TABLE transaction_payrollrecord DROP COLUMN coach_id")
                self.stdout.write(self.style.SUCCESS('Successfully removed coach_id from transaction_payrollrecord table'))
            else:
                self.stdout.write(self.style.SUCCESS('coach_id column does not exist in transaction_payrollrecord table'))

            # Check if is_coach column exists in transaction_payrollrecord table
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name='transaction_payrollrecord' AND column_name='is_coach'
            """)
            if cursor.fetchone()[0] > 0:
                self.stdout.write(self.style.WARNING('Removing is_coach from transaction_payrollrecord table...'))
                cursor.execute("ALTER TABLE transaction_payrollrecord DROP COLUMN is_coach")
                self.stdout.write(self.style.SUCCESS('Successfully removed is_coach from transaction_payrollrecord table'))
            else:
                self.stdout.write(self.style.SUCCESS('is_coach column does not exist in transaction_payrollrecord table'))

            # Check if coach_id column exists in attendance_attendance table
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name='attendance_attendance' AND column_name='coach_id'
            """)
            if cursor.fetchone()[0] > 0:
                self.stdout.write(self.style.WARNING('Removing coach_id from attendance_attendance table...'))
                cursor.execute("ALTER TABLE attendance_attendance DROP COLUMN coach_id")
                self.stdout.write(self.style.SUCCESS('Successfully removed coach_id from attendance_attendance table'))
            else:
                self.stdout.write(self.style.SUCCESS('coach_id column does not exist in attendance_attendance table'))

            # Check if is_coach column exists in attendance_attendance table
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name='attendance_attendance' AND column_name='is_coach'
            """)
            if cursor.fetchone()[0] > 0:
                self.stdout.write(self.style.WARNING('Removing is_coach from attendance_attendance table...'))
                cursor.execute("ALTER TABLE attendance_attendance DROP COLUMN is_coach")
                self.stdout.write(self.style.SUCCESS('Successfully removed is_coach from attendance_attendance table'))
            else:
                self.stdout.write(self.style.SUCCESS('is_coach column does not exist in attendance_attendance table'))

            # Mark migrations as applied
            self.stdout.write(self.style.WARNING('Marking migrations as applied...'))
            try:
                cursor.execute("""
                    INSERT IGNORE INTO django_migrations (app, name, applied)
                    VALUES
                    ('transaction', '0004_remove_coach_field', NOW()),
                    ('transaction', '0004_remove_coach_fields', NOW()),
                    ('transaction', '0005_merge_20250504_1840', NOW()),
                    ('attendance', '0003_remove_coach_field', NOW()),
                    ('attendance', '0003_remove_coach_fields', NOW()),
                    ('attendance', '0004_merge_20250504_1841', NOW())
                """)
                self.stdout.write(self.style.SUCCESS('Successfully marked migrations as applied'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error marking migrations as applied: {str(e)}'))

        self.stdout.write(self.style.SUCCESS('Migration issues fixed successfully!'))
