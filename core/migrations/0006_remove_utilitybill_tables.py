from django.db import migrations

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0005_remove_utilitybill_tables'),
    ]

    operations = [
        migrations.RunSQL(
            "SET FOREIGN_KEY_CHECKS = 0;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilitypayment;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilitybill;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilityprovider;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "DROP TABLE IF EXISTS utilitybill_utilitycategory;",
            reverse_sql=migrations.RunSQL.noop,
        ),
        migrations.RunSQL(
            "SET FOREIGN_KEY_CHECKS = 1;",
            reverse_sql=migrations.RunSQL.noop,
        ),
    ]
