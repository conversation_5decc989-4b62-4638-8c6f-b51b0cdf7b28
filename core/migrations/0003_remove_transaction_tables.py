# Generated manually

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_remove_equipment_tables'),
    ]

    operations = [
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS transaction_transaction;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS transaction_payrollrecord;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DELETE FROM django_migrations WHERE app = 'transaction';",
            reverse_sql="",  # No reverse operation
        ),
    ]
