# Generated manually

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_remove_notification_table'),
    ]

    operations = [
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS equipment_equipment;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DROP TABLE IF EXISTS equipment_equipmentactivity;",
            reverse_sql="",  # No reverse operation
        ),
        migrations.RunSQL(
            sql="DELETE FROM django_migrations WHERE app = 'equipment';",
            reverse_sql="",  # No reverse operation
        ),
    ]
