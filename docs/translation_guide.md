# Translation Guide for Legend Fitness Club

This guide explains how to work with translations in the Legend Fitness Club project.

## Overview

The Legend Fitness Club project supports multiple languages using Django's internationalization (i18n) framework. Currently, the following languages are supported:

- English (en)
- Khmer (km)

## How to Mark Strings for Translation

### In Templates

1. Load the i18n tag library at the top of your template:
   ```html
   {% load i18n %}
   ```

2. Mark strings for translation using the `trans` tag:
   ```html
   <h1>{% trans "Welcome to Legend Fitness" %}</h1>
   ```

3. For strings with variables, use the `blocktrans` tag:
   ```html
   {% blocktrans with name=user.name %}
   Hello, {{ name }}!
   {% endblocktrans %}
   ```

### In Python Code

1. Import the translation function:
   ```python
   from django.utils.translation import gettext as _
   ```

2. Mark strings for translation:
   ```python
   message = _("Welcome to Legend Fitness")
   ```

3. For model fields, use `gettext_lazy`:
   ```python
   from django.utils.translation import gettext_lazy as _
   
   class MyModel(models.Model):
       name = models.CharField(_("Name"), max_length=100)
   ```

## Working with Translation Files

### Prerequisites

You need to have GNU gettext tools installed:

1. On Windows, run the provided PowerShell script:
   ```
   .\install_gettext.ps1
   ```

2. On Linux/Mac:
   ```
   sudo apt-get install gettext  # Ubuntu/Debian
   brew install gettext          # macOS with Homebrew
   ```

### Creating/Updating Translation Files

1. Run Django's makemessages command:
   ```
   python manage.py makemessages -l km  # For Khmer
   python manage.py makemessages -l en  # For English
   ```

2. Or use our custom script:
   ```
   python make_translations.py --make --locale=km
   ```

### Editing Translation Files

1. Translation files are located in the `locale/<language_code>/LC_MESSAGES/` directory.
2. Edit the `.po` files using a text editor or a specialized PO file editor like Poedit.
3. Each string to translate has this format:
   ```
   msgid "Original string"
   msgstr "Translated string"
   ```

### Compiling Translation Files

After editing the `.po` files, you need to compile them to `.mo` files:

1. Using Django's command:
   ```
   python manage.py compilemessages
   ```

2. Or using our custom script:
   ```
   python compile_mo_direct.py
   ```

## Language Switching

The language switcher is located in the header of the application. Users can click on the language icon to switch between available languages.

## Adding a New Language

1. Add the language to `LANGUAGES` in `settings.py`:
   ```python
   LANGUAGES = [
       ('en', _('English')),
       ('km', _('Khmer')),
       ('fr', _('French')),  # New language
   ]
   ```

2. Create translation files for the new language:
   ```
   python manage.py makemessages -l fr
   ```

3. Edit the generated `.po` file in `locale/fr/LC_MESSAGES/django.po`

4. Compile the translation:
   ```
   python manage.py compilemessages
   ```

5. Update the language switcher in `templates/includes/language_switcher.html` to include the new language.

## Best Practices

1. Use context notes for translators when necessary:
   ```python
   # Translators: This is a greeting message
   message = _("Welcome")
   ```

2. Keep translations up to date by regularly running makemessages.

3. Test your translations by switching languages in the application.

4. Use placeholders consistently:
   ```python
   # Good
   _("Hello, %(name)s") % {'name': user.name}
   
   # Avoid
   _("Hello, ") + user.name
   ```

5. Remember that some languages may require more space than English, so design your UI accordingly.

## Troubleshooting

- If you see untranslated strings, make sure they are properly marked for translation.
- If changes to `.po` files don't appear, make sure you've compiled them to `.mo` files.
- Check that the `LocaleMiddleware` is properly configured in `settings.py`.
- Ensure that `USE_I18N = True` in `settings.py`.

For more information, refer to the [Django Translation documentation](https://docs.djangoproject.com/en/5.1/topics/i18n/translation/).
