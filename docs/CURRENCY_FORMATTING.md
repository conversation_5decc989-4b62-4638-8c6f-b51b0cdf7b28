# Currency Formatting Guide

This document provides guidelines for maintaining consistent currency formatting throughout the Legend Fitness Club project.

## Currency Formatting Standards

All monetary values in the Legend Fitness Club project should follow these formatting standards:

1. **Numbers should include thousand separators using commas**
   - Example: "1,000" instead of "1000"
   - Example: "2,434,556" instead of "2434556"

2. **Currency symbols should be consistently applied**
   - Cambodian Riel (KHR): ៛ (symbol appears after the number)
   - US Dollar (USD): $ (symbol appears before the number)

3. **Decimal places**
   - KHR values should be displayed as whole numbers without decimal places
   - USD values should be displayed with 2 decimal places

## Implementation Details

### Backend (Django Templates)

We've implemented three template filters for formatting currency values:

1. **`format_khr`**: Formats a number as Cambodian Riel with thousand separators
   ```
   {{ value|format_khr }}  <!-- Example: 10000 -> 10,000៛ -->
   ```

2. **`format_usd`**: Formats a number as US Dollar with thousand separators
   ```
   {{ value|format_usd }}  <!-- Example: 10000 -> $10,000.00 -->
   ```

3. **`format_number_with_commas`**: Formats a number with thousand separators without any currency symbol
   ```
   {{ value|format_number_with_commas }}  <!-- Example: 10000 -> 10,000 -->
   ```

### Frontend (JavaScript)

For client-side formatting, we've implemented JavaScript utility functions in `static/js/currency-formatter.js`:

1. **`formatKHR(value)`**: Formats a number as Cambodian Riel with thousand separators
   ```javascript
   formatKHR(10000);  // Returns "10,000៛"
   ```

2. **`formatUSD(value)`**: Formats a number as US Dollar with thousand separators
   ```javascript
   formatUSD(10000);  // Returns "$10,000.00"
   ```

3. **`formatNumber(value, decimals = 0)`**: Formats a number with thousand separators without any currency symbol
   ```javascript
   formatNumber(10000);  // Returns "10,000"
   formatNumber(10000, 2);  // Returns "10,000.00"
   ```

4. **`setupFormattedInput(inputElement, formatType)`**: Sets up an input field to display formatted values
   ```javascript
   // Example usage:
   const amountInput = document.getElementById('amount_khr');
   setupFormattedInput(amountInput, 'khr');
   ```

## How to Use

### In Django Templates

1. Load the currency filters at the top of your template:
   ```html
   {% load currency_filters %}
   ```

2. Apply the appropriate filter to your currency values:
   ```html
   <p>Price: {{ product.price|format_khr }}</p>
   <p>USD Price: {{ product.usd_price|format_usd }}</p>
   ```

### In JavaScript

1. Make sure the currency-formatter.js script is included in your template:
   ```html
   <script src="{% static 'js/currency-formatter.js' %}"></script>
   ```

2. Use the formatting functions in your JavaScript code:
   ```javascript
   const price = 10000;
   const formattedPrice = formatKHR(price);  // "10,000៛"
   ```

3. For input fields that need to display formatted values:
   ```javascript
   document.addEventListener('DOMContentLoaded', function() {
       const amountInput = document.getElementById('amount_khr');
       setupFormattedInput(amountInput, 'khr');
       
       // Ensure the form submits the numeric value
       const form = amountInput.closest('form');
       form.addEventListener('submit', function(e) {
           // Get the current formatted value
           const formattedValue = amountInput.value;
           
           // Convert to numeric value for submission
           amountInput.value = formattedValue.replace(/[^\d]/g, '');
       });
   });
   ```

## Best Practices

1. **Always use the provided filters and functions** for formatting currency values to maintain consistency.

2. **Change input types from "number" to "text"** when using formatted currency values, as HTML number inputs don't support thousand separators.

3. **Remember to convert formatted values back to numeric values** before form submission.

4. **Use the appropriate filter for each currency type**:
   - Use `format_khr` for Cambodian Riel values
   - Use `format_usd` for US Dollar values
   - Use `format_number_with_commas` for generic numeric values

5. **Include both currency filters in templates** that might display either currency:
   ```html
   {% load custom_filters %}
   {% load currency_filters %}
   ```

## Troubleshooting

If you encounter issues with currency formatting:

1. **Check that the correct filter is being applied** to the value.

2. **Verify that the value is a valid number** before formatting.

3. **For input fields, ensure the value is converted back to a numeric value** before form submission.

4. **If a value appears unformatted, check if the template is loading the currency filters**.

## Adding New Currency Formats

If you need to add support for additional currency formats:

1. Add a new filter function to `core/templatetags/currency_formatters.py`
2. Add a corresponding JavaScript function to `static/js/currency-formatter.js`
3. Update this documentation with the new format details
