# Generated by Django 5.1.6 on 2025-04-26 03:49

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('date', models.DateTimeField(verbose_name='Date')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total Amount')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Cost Price')),
                ('expiry_date', models.DateField(blank=True, null=True, verbose_name='Expiry Date')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='Batch Number')),
                ('is_box_purchase', models.BooleanField(default=False, verbose_name='Box Purchase')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of units per box', verbose_name='Box Quantity')),
                ('original_quantity', models.IntegerField(default=0, help_text='Original quantity entered (in boxes if box purchase)', verbose_name='Original Quantity')),
                ('stored_box_cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Stored Box Cost')),
            ],
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='Date')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Total Amount')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('card', 'Card')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
            ],
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Price')),
                ('is_box_equivalent', models.BooleanField(default=False, help_text='Whether this sale represents full box equivalents', verbose_name='Box Equivalent')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of units per box', verbose_name='Box Quantity')),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True, verbose_name='Contact Person')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='Email')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
            ],
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('sku', models.CharField(max_length=20, unique=True, verbose_name='SKU')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='Image')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Cost Price')),
                ('retail_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Retail Price')),
                ('quantity', models.IntegerField(default=0, verbose_name='Quantity')),
                ('low_stock_threshold', models.IntegerField(default=5, verbose_name='Low Stock Threshold')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of items per box', verbose_name='Box Quantity')),
                ('box_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='Box Cost')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('category', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='product.category')),
            ],
        ),
        migrations.CreateModel(
            name='ProductExpiry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='Batch Number')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('expiry_date', models.DateField(verbose_name='Expiry Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expiry_dates', to='product.product')),
            ],
            options={
                'verbose_name_plural': 'Product Expiry Dates',
            },
        ),
    ]
