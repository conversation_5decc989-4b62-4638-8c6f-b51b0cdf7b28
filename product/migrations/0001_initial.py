# Generated by Django 4.2.16 on 2025-05-31 09:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
            ],
            options={
                'verbose_name_plural': 'Categories',
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('sku', models.CharField(max_length=20, unique=True, verbose_name='SKU')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='Image')),
                ('cost_price', models.IntegerField(verbose_name='Cost Price')),
                ('retail_price', models.IntegerField(verbose_name='Retail Price')),
                ('quantity', models.IntegerField(default=0, verbose_name='Quantity')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of items per box', verbose_name='Box Quantity')),
                ('box_cost', models.IntegerField(help_text='Total cost for purchasing one box', verbose_name='Box Cost')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this product is active and available for sale', verbose_name='Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
        ),
        migrations.CreateModel(
            name='Purchase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('date', models.DateTimeField(verbose_name='Date')),
                ('total_amount', models.IntegerField(verbose_name='Total Amount')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
        ),
        migrations.CreateModel(
            name='PurchaseItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('cost_price', models.IntegerField(verbose_name='Cost Price')),
                ('is_box_purchase', models.BooleanField(default=False, verbose_name='Box Purchase')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of units per box', verbose_name='Box Quantity')),
                ('original_quantity', models.IntegerField(default=0, help_text='Original quantity entered (in boxes if box purchase)', verbose_name='Original Quantity')),
                ('stored_box_cost', models.IntegerField(default=0, verbose_name='Stored Box Cost')),
            ],
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trxId', models.CharField(max_length=50, unique=True, verbose_name='Transaction ID')),
                ('date', models.DateTimeField(auto_now_add=True, verbose_name='Date')),
                ('total_amount', models.IntegerField(verbose_name='Total Amount')),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('bank', 'Bank Transfer'), ('other', 'Other')], default='cash', max_length=20, verbose_name='Payment Method')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='Notes')),
            ],
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('telegram', models.CharField(blank=True, max_length=100, null=True, verbose_name='Telegram/Messenger')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('note', models.TextField(blank=True, null=True, verbose_name='Note')),
            ],
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(verbose_name='Quantity')),
                ('price', models.IntegerField(verbose_name='Price')),
                ('is_box_equivalent', models.BooleanField(default=False, help_text='Whether this sale represents full box equivalents', verbose_name='Box Equivalent')),
                ('box_quantity', models.IntegerField(default=1, help_text='Number of units per box', verbose_name='Box Quantity')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='product.product')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='product.sale')),
            ],
        ),
    ]
