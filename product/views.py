from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.db.models import Q, Sum, F, ExpressionWrapper, DecimalField
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.http import HttpResponse, JsonResponse
from django.conf import settings
import csv
import datetime
from datetime import datetime, timedelta
from .models import Category, Supplier, Product, Purchase, PurchaseItem, Sale, SaleItem
from user.models import MetaData
from core.decorators import module_permission_required
from core.logging_utils import log_create_action, log_edit_action, log_delete_action

# Create your views here.
@login_required
@module_permission_required(module='product', required_level='view')
def index(request):
    # Default sorting is by most recently added
    sort_by = request.GET.get('sort', '-created_at')

    # Map frontend sort options to model fields
    sort_options = {
        'name-asc': 'name',
        'name-desc': '-name',
        'price-asc': 'retail_price',
        'price-desc': '-retail_price',
        'newest': '-created_at',
    }

    # Use the mapped sort field or default to '-created_at'
    sort_field = sort_options.get(sort_by, '-created_at')

    # Get all products with sorting
    products_list = Product.objects.all().order_by(sort_field)
    categories = Category.objects.all()

    # Filter by category if provided
    category_id = request.GET.get('category')
    if category_id:
        products_list = products_list.filter(category_id=category_id)

    # Filter by stock status if provided
    stock_status = request.GET.get('stock_status')
    if stock_status:
        if stock_status == 'in-stock':
            products_list = products_list.filter(quantity__gt=0)
        elif stock_status == 'out-of-stock':
            products_list = products_list.filter(quantity=0)

    # Filter by price range if provided
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')
    if min_price:
        try:
            min_price = int(min_price)
            products_list = products_list.filter(retail_price__gte=min_price)
        except ValueError:
            pass

    if max_price:
        try:
            max_price = int(max_price)
            products_list = products_list.filter(retail_price__lte=max_price)
        except ValueError:
            pass

    # Filter by date added if provided
    date_added = request.GET.get('date_added')
    if date_added:
        try:
            if date_added == 'today':
                today = datetime.now().date()
                products_list = products_list.filter(created_at__date=today)
            elif date_added == 'this_week':
                today = datetime.now().date()
                start_of_week = today - timedelta(days=today.weekday())
                products_list = products_list.filter(created_at__date__gte=start_of_week)
            elif date_added == 'this_month':
                today = datetime.now().date()
                start_of_month = today.replace(day=1)
                products_list = products_list.filter(created_at__date__gte=start_of_month)
            elif date_added == 'this_year':
                today = datetime.now().date()
                start_of_year = today.replace(month=1, day=1)
                products_list = products_list.filter(created_at__date__gte=start_of_year)
        except Exception:
            pass

    # Handle search
    search_query = request.GET.get('search')
    if search_query:
        products_list = products_list.filter(
            Q(name__icontains=search_query) |
            Q(sku__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(category__name__icontains=search_query)
        )

    # Handle POST search (for backward compatibility)
    if request.method == "POST" and 'search' in request.POST:
        search = request.POST.get("search")
        products_list = Product.objects.filter(
            Q(name__icontains=search) |
            Q(sku__icontains=search) |
            Q(description__icontains=search)
        )



    # Pagination with configurable items per page
    from django.core.paginator import Paginator

    # Get items per page from request or use default (10)
    items_per_page = request.GET.get('items_per_page', 10)
    try:
        items_per_page = int(items_per_page)
        # Validate items per page (between 5 and 100)
        if items_per_page < 5:
            items_per_page = 5
        elif items_per_page > 100:
            items_per_page = 100
    except (ValueError, TypeError):
        items_per_page = 10

    paginator = Paginator(products_list, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get paginated products
    products = page_obj

    context = {
        'page_obj': page_obj,
        'products': products,  # For template compatibility
        'categories': categories,
        'selected_category': category_id,
        'selected_stock_status': stock_status,
        'selected_sort': sort_by,
        'search_query': search_query,
        'min_price': min_price,
        'max_price': max_price,
        'date_added': date_added,
        'items_per_page': items_per_page,
    }
    return render(request, 'product/index.html', context)

@login_required
@module_permission_required(module='product', required_level='edit')
def create_product(request):
    """
    Create a new product
    """
    categories = Category.objects.all()

    if request.method == "POST":
        try:
            name = request.POST.get("name")
            sku = request.POST.get("sku")
            category_id = request.POST.get("category")
            description = request.POST.get("description")
            retail_price = int(float(request.POST.get("retail_price", 0)))
            quantity = int(request.POST.get("quantity", 0))
            box_quantity = int(request.POST.get("box_quantity", 1))
            box_cost = request.POST.get("box_cost")

            # Validate required fields
            if not name or not name.strip():
                messages.error(request, "Product name is required.")
                return render(request, 'product/product_form.html', {'categories': categories})

            if not sku or not sku.strip():
                messages.error(request, "SKU is required.")
                return render(request, 'product/product_form.html', {'categories': categories})

            # Validate Box Cost is required and greater than 0
            if not box_cost or float(box_cost) <= 0:
                messages.error(request, "Box cost is required and must be greater than 0.")
                return render(request, 'product/product_form.html', {'categories': categories})

            # Validate Box Quantity is greater than 0
            if box_quantity <= 0:
                messages.error(request, "Box quantity must be greater than 0.")
                return render(request, 'product/product_form.html', {'categories': categories})

            # Convert box_cost to integer
            box_cost = int(float(box_cost))

            # Calculate Cost Price automatically from Box Cost ÷ Box Quantity
            cost_price = int(box_cost / box_quantity)

            # Validate Cost Price is greater than 0 (should be automatic if box_cost and box_quantity are valid)
            if cost_price <= 0:
                messages.error(request, "Calculated cost price must be greater than 0. Please check box cost and box quantity.")
                return render(request, 'product/product_form.html', {'categories': categories})

            # Validate Retail Price is greater than or equal to Cost Price
            if retail_price < cost_price:
                messages.error(request, f"Retail price ({retail_price:,}៛) must be greater than or equal to calculated cost price ({cost_price:,}៛).")
                return render(request, 'product/product_form.html', {'categories': categories})

            # Get or create category
            category = None
            if category_id:
                category = Category.objects.get(id=category_id)

            # Get active status
            is_active = request.POST.get("is_active") == 'on'

            # Create product
            product = Product.objects.create(
                name=name,
                sku=sku,
                category=category,
                description=description,
                cost_price=cost_price,
                retail_price=retail_price,
                quantity=quantity,
                box_quantity=box_quantity,
                box_cost=box_cost if box_cost else None,
                is_active=is_active
            )

            # Handle image upload
            if 'image' in request.FILES:
                product.image = request.FILES['image']
                product.save()

            # Log the product creation
            log_create_action(
                request=request,
                module='product',
                target_model='Product',
                target_id=product.id,
                target_description=f'Product {name} (SKU: {sku})',
                additional_data={
                    'sku': sku,
                    'category': category.name if category else None,
                    'cost_price': cost_price,
                    'retail_price': retail_price,
                    'quantity': quantity,
                    'box_quantity': box_quantity,
                    'box_cost': box_cost,
                    'is_active': is_active
                }
            )

            messages.success(request, f"Product {name} created successfully!")
            return redirect('product:index')

        except Exception as e:
            messages.error(request, f"Error creating product: {str(e)}")

    context = {
        'categories': categories
    }
    return render(request, 'product/product_form.html', context)

@login_required
@module_permission_required(module='product', required_level='edit')
def edit_product(request, pk):
    product = get_object_or_404(Product, pk=pk)
    categories = Category.objects.all()

    if request.method == "POST":
        try:
            name = request.POST.get("name")
            sku = request.POST.get("sku")
            category_id = request.POST.get("category")
            description = request.POST.get("description")
            retail_price = int(float(request.POST.get("retail_price", 0)))
            quantity = int(request.POST.get("quantity", 0))
            box_quantity = int(request.POST.get("box_quantity", 1))
            box_cost = request.POST.get("box_cost")

            # Validate required fields
            if not name or not name.strip():
                messages.error(request, "Product name is required.")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            if not sku or not sku.strip():
                messages.error(request, "SKU is required.")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            # Validate Box Cost is required and greater than 0
            if not box_cost or float(box_cost) <= 0:
                messages.error(request, "Box cost is required and must be greater than 0.")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            # Validate Box Quantity is greater than 0
            if box_quantity <= 0:
                messages.error(request, "Box quantity must be greater than 0.")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            # Convert box_cost to integer
            box_cost = int(float(box_cost))

            # Calculate Cost Price automatically from Box Cost ÷ Box Quantity
            cost_price = int(box_cost / box_quantity)

            # Validate Cost Price is greater than 0 (should be automatic if box_cost and box_quantity are valid)
            if cost_price <= 0:
                messages.error(request, "Calculated cost price must be greater than 0. Please check box cost and box quantity.")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            # Validate Retail Price is greater than or equal to Cost Price
            if retail_price < cost_price:
                messages.error(request, f"Retail price ({retail_price:,}៛) must be greater than or equal to calculated cost price ({cost_price:,}៛).")
                return render(request, 'product/product_form.html', {'product': product, 'categories': categories})

            # Update product fields
            product.name = name
            product.sku = sku
            product.description = description
            product.cost_price = cost_price  # This is now calculated automatically
            product.retail_price = retail_price
            product.quantity = quantity
            product.box_quantity = box_quantity
            product.box_cost = box_cost

            if category_id:
                product.category = Category.objects.get(id=category_id)
            else:
                product.category = None

            # Handle active status
            product.is_active = request.POST.get("is_active") == 'on'

            # Handle image upload
            if 'image' in request.FILES:
                product.image = request.FILES['image']

            product.save()

            # Log the product edit
            log_edit_action(
                request=request,
                module='product',
                target_model='Product',
                target_id=product.id,
                target_description=f'Product {product.name} (SKU: {product.sku})',
                additional_data={
                    'sku': product.sku,
                    'category': product.category.name if product.category else None,
                    'cost_price': product.cost_price,
                    'retail_price': product.retail_price,
                    'quantity': product.quantity,
                    'box_quantity': product.box_quantity,
                    'box_cost': product.box_cost,
                    'is_active': product.is_active,
                    'image_updated': 'image' in request.FILES
                }
            )

            messages.success(request, f"Product {product.name} updated successfully!")
            return redirect('product:index')

        except Exception as e:
            messages.error(request, f"Error updating product: {str(e)}")

    context = {
        'product': product,
        'categories': categories
    }
    return render(request, 'product/product_form.html', context)

@login_required
@module_permission_required(module='product', required_level='full')
def delete_product(request, pk):
    product = get_object_or_404(Product, pk=pk)
    name = product.name
    sku = product.sku

    try:
        # Log the product deletion before deleting
        log_delete_action(
            request=request,
            module='product',
            target_model='Product',
            target_id=product.id,
            target_description=f'Product {name} (SKU: {sku})',
            additional_data={
                'sku': sku,
                'category': product.category.name if product.category else None,
                'cost_price': product.cost_price,
                'retail_price': product.retail_price,
                'quantity': product.quantity,
                'box_quantity': product.box_quantity,
                'box_cost': product.box_cost,
                'is_active': getattr(product, 'is_active', True)
            }
        )

        product.delete()
        messages.success(request, f"Product {name} deleted successfully!")
    except Exception as e:
        messages.error(request, f"Error deleting product: {str(e)}")

    return redirect('product:index')

@login_required
@module_permission_required(module='product', required_level='edit')
def bulk_action(request):
    """
    Handle bulk actions on products
    """
    if request.method == "POST":
        action = request.POST.get("action")
        product_ids = request.POST.getlist("product_ids[]")

        if not product_ids:
            messages.error(request, "No products selected.")
            return redirect('product:index')

        if action == "delete":
            # Check if user has full access permission for product module
            from settings.models import RolePermission
            has_delete_permission = RolePermission.has_permission(request.user.role, 'product', 'full')

            if not has_delete_permission and request.user.role != 'admin':
                messages.error(request, "Access denied. You need Full Access permission for the Product Management module to delete products.")
                return redirect('product:index')

            try:
                # Get the products to delete
                products = Product.objects.filter(id__in=product_ids)
                count = products.count()

                # Delete the products
                products.delete()

                messages.success(request, f"{count} products deleted successfully!")
            except Exception as e:
                messages.error(request, f"Error deleting products: {str(e)}")

        elif action in ["active", "inactive"]:
            try:
                is_active = (action == "active")
                # Check if is_active field exists in the database
                from django.db import connection
                cursor = connection.cursor()
                cursor.execute("SHOW COLUMNS FROM product_product LIKE 'is_active'")
                is_active_exists = cursor.fetchone() is not None

                if is_active_exists:
                    # Update the active status of the products
                    Product.objects.filter(id__in=product_ids).update(is_active=is_active)
                    status_text = "active" if is_active else "inactive"
                    messages.success(request, f"Successfully set {len(product_ids)} products to {status_text}.")
                else:
                    # Add is_active column to the database
                    cursor.execute("ALTER TABLE product_product ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT 1")
                    # Update the active status of the products
                    Product.objects.filter(id__in=product_ids).update(is_active=is_active)
                    status_text = "active" if is_active else "inactive"
                    messages.success(request, f"Successfully set {len(product_ids)} products to {status_text}.")
            except Exception as e:
                messages.error(request, f"Error updating product status: {str(e)}")

        elif action == "reactivate_stocked":
            try:
                # Get inactive products with stock
                products = Product.objects.filter(id__in=product_ids, is_active=False, quantity__gt=0)
                count = products.count()

                if count > 0:
                    # Reactivate the products
                    products.update(is_active=True)
                    messages.success(request, f"Successfully reactivated {count} products that have stock.")
                else:
                    messages.info(request, "No inactive products with stock found among the selected products.")
            except Exception as e:
                messages.error(request, f"Error reactivating products: {str(e)}")

        elif action == "export":
            try:
                # Export the selected products to CSV
                return export_products_csv(request, product_ids)
            except Exception as e:
                messages.error(request, f"Error exporting products: {str(e)}")

    return redirect('product:index')

@login_required
@module_permission_required(module='product', required_level='view')
def export_products_csv(request, product_ids=None):
    """
    Export products to CSV
    """
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="products.csv"'

    writer = csv.writer(response)
    writer.writerow(['ID', 'SKU', 'Name', 'Category', 'Description', 'Cost Price', 'Retail Price',
                    'Quantity', 'Box Quantity', 'Box Cost', 'Active', 'Created At'])

    # Get products to export
    if product_ids:
        products = Product.objects.filter(id__in=product_ids)
    else:
        # Apply filters from request if no specific IDs provided
        products = Product.objects.all()

        # Apply the same filters as in the index view
        category_id = request.GET.get('category')
        if category_id:
            products = products.filter(category_id=category_id)

        stock_status = request.GET.get('stock_status')
        if stock_status:
            if stock_status == 'in-stock':
                products = products.filter(quantity__gt=F('low_stock_threshold'))
            elif stock_status == 'low-stock':
                products = products.filter(quantity__gt=0, quantity__lte=F('low_stock_threshold'))
            elif stock_status == 'out-of-stock':
                products = products.filter(quantity=0)

        search_query = request.GET.get('search')
        if search_query:
            products = products.filter(
                Q(name__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(category__name__icontains=search_query)
            )

    # Write product data to CSV
    for product in products:
        writer.writerow([
            product.id,
            product.sku,
            product.name,
            product.category.name if product.category else '',
            product.description,
            product.cost_price,
            product.retail_price,
            product.quantity,
            product.box_quantity,
            product.box_cost if product.box_cost else '',
            'Yes' if getattr(product, 'is_active', True) else 'No',
            product.created_at.strftime('%Y-%m-%d %H:%M:%S')
        ])

    return response



@login_required
@module_permission_required(module='product', required_level='edit')
def import_products(request):
    """
    Import products from CSV
    """
    if request.method == 'POST' and request.FILES.get('csv_file'):
        csv_file = request.FILES['csv_file']

        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'Please upload a CSV file.')
            return redirect('product:index')

        try:
            # Read the CSV file
            decoded_file = csv_file.read().decode('utf-8').splitlines()
            reader = csv.DictReader(decoded_file)

            # Track statistics
            created_count = 0
            updated_count = 0
            error_count = 0

            for row in reader:
                try:
                    # Check if product exists by SKU
                    sku = row.get('SKU', '').strip()
                    if not sku:
                        continue

                    product, created = Product.objects.get_or_create(sku=sku)

                    # Update product fields
                    product.name = row.get('Name', '').strip()

                    # Handle category
                    category_name = row.get('Category', '').strip()
                    if category_name:
                        category, _ = Category.objects.get_or_create(name=category_name)
                        product.category = category

                    # Update other fields
                    product.description = row.get('Description', '').strip()

                    try:
                        product.cost_price = int(float(row.get('Cost Price', 0)))
                        product.retail_price = int(float(row.get('Retail Price', 0)))
                        product.quantity = int(row.get('Quantity', 0))
                        product.box_quantity = int(row.get('Box Quantity', 1))

                        box_cost = row.get('Box Cost', '')
                        if box_cost:
                            product.box_cost = int(float(box_cost))

                        # Handle active status
                        is_active = row.get('Active', '').strip().lower()
                        try:
                            product.is_active = is_active in ['yes', 'true', '1', 'y']
                        except AttributeError:
                            # is_active field doesn't exist yet, will be added when saving
                            pass
                    except (ValueError, TypeError):
                        error_count += 1
                        continue

                    product.save()

                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

                except Exception as e:
                    error_count += 1
                    continue

            messages.success(request, f"Import complete: {created_count} products created, {updated_count} products updated, {error_count} errors.")

        except Exception as e:
            messages.error(request, f"Error importing products: {str(e)}")

    return redirect('product:index')



@login_required
@module_permission_required(module='purchase', required_level='view')
def purchases(request):
    purchases_query = Purchase.objects.all().order_by('-date')

    # Date filtering
    start_date = datetime.now().date()
    end_date = datetime.now().date()
    filter_active = False

    if request.method == "GET" and 'start_date' in request.GET and 'end_date' in request.GET:
        try:
            start_date = datetime.strptime(request.GET.get('start_date'), '%Y-%m-%d').date()
            end_date = datetime.strptime(request.GET.get('end_date'), '%Y-%m-%d').date()
            filter_active = True

            # Add one day to end_date to include the end date in results
            end_date_inclusive = end_date + timedelta(days=1)
            purchases_query = Purchase.objects.filter(
                date__gte=start_date,
                date__lt=end_date_inclusive
            ).order_by('-date')
        except ValueError:
            messages.error(request, "Invalid date format")

    # Get total count before pagination
    total_filtered_purchases = purchases_query.count()

    # Pagination with configurable items per page
    from django.core.paginator import Paginator

    # Get items per page from request or use default (10)
    items_per_page = request.GET.get('items_per_page', 10)
    try:
        items_per_page = int(items_per_page)
        # Validate items per page (between 5 and 100)
        if items_per_page < 5:
            items_per_page = 5
        elif items_per_page > 100:
            items_per_page = 100
    except (ValueError, TypeError):
        items_per_page = 10

    paginator = Paginator(purchases_query, items_per_page)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get paginated purchases
    purchases = page_obj

    context = {
        'page_obj': page_obj,
        'purchases': purchases,  # For template compatibility
        'suppliers': Supplier.objects.all().order_by('name'),
        'start_date': start_date.strftime('%Y-%m-%d'),
        'end_date': end_date.strftime('%Y-%m-%d'),
        'filter_active': filter_active,
        'total_filtered_purchases': total_filtered_purchases,
        'items_per_page': items_per_page,
    }
    return render(request, 'product/purchases.html', context)

@login_required
@module_permission_required(module='purchase', required_level='edit')
def create_purchase(request):
    """
    Create a new purchase
    """
    suppliers = Supplier.objects.all().order_by('name')
    products = Product.objects.all().order_by('name')

    if request.method == "POST" and 'product[]' in request.POST:
        try:
            notes = request.POST.get("notes", "")
            payment_method = request.POST.get("payment_method", "cash")

            # Get product IDs, suppliers, quantities, costs, and box purchase flags from form
            product_ids = request.POST.getlist("product[]")
            supplier_ids = request.POST.getlist("supplier[]")
            quantities = request.POST.getlist("quantity[]")
            costs = request.POST.getlist("cost[]")
            is_box_purchases = request.POST.getlist("is_box_purchase[]")

            # Debug the checkbox values
            print("Box purchase checkboxes:", is_box_purchases)

            # Calculate total amount and prepare data for processing
            total_amount = 0
            processed_items = []
            seen_combinations = set()  # Track product+supplier combinations to prevent duplicates

            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and costs[i]:
                    product = Product.objects.get(id=product_ids[i])
                    quantity = int(quantities[i])
                    cost = float(costs[i])

                    # Get supplier for this item (can be None)
                    supplier = None
                    if i < len(supplier_ids) and supplier_ids[i]:
                        supplier = Supplier.objects.get(id=supplier_ids[i])

                    # Create a unique key for product+supplier combination
                    combination_key = (product.id, supplier.id if supplier else None)

                    # Check for duplicate product+supplier combinations
                    if combination_key in seen_combinations:
                        messages.error(request, f"Duplicate entry detected: {product.name} from {supplier.name if supplier else 'No Supplier'}. Please combine quantities or use different suppliers.")
                        return render(request, 'product/purchase_form.html', {
                            'suppliers': suppliers,
                            'products': products,
                        })

                    seen_combinations.add(combination_key)

                    # Check if this item is purchased as boxes
                    # The value will be '1' if box purchase, '0' if unit purchase
                    is_box_purchase = False
                    if i < len(is_box_purchases) and is_box_purchases[i] == '1':
                        is_box_purchase = True

                    # Force box purchase for products with box quantities > 1
                    if product.box_quantity > 1:
                        is_box_purchase = True

                    print(f"Processing item {i}: Box purchase = {is_box_purchase}, Value = {is_box_purchases[i] if i < len(is_box_purchases) else 'N/A'}, Box Quantity = {product.box_quantity}")

                    # Calculate actual unit quantity and unit cost
                    if is_box_purchase:
                        # If purchasing by box, convert to units
                        actual_quantity = quantity * product.box_quantity
                        box_cost = cost  # This is the cost per box
                        unit_cost = box_cost / product.box_quantity  # Calculate cost per unit
                        display_quantity = f"{quantity} boxes ({actual_quantity} units)"
                    else:
                        # Regular unit purchase (only for products without box quantities)
                        actual_quantity = quantity
                        unit_cost = cost
                        box_cost = unit_cost * product.box_quantity  # Calculate box cost for reference
                        display_quantity = f"{quantity} units"

                    # Add to total amount using the input cost and quantity
                    # (this preserves the total regardless of box/unit mode)
                    total_amount += quantity * cost

                    # Store processed data for later use
                    processed_items.append({
                        'product': product,
                        'supplier': supplier,
                        'display_quantity': display_quantity,
                        'actual_quantity': actual_quantity,
                        'unit_cost': unit_cost,
                        'box_cost': box_cost,  # Store the box cost
                        'total_cost': quantity * cost,
                        'is_box_purchase': is_box_purchase,
                        'original_quantity': quantity  # The original quantity entered (in boxes if box purchase)
                    })

            # Create purchase record
            timestamp = int(datetime.now().timestamp())
            purchase_data = {
                'trxId': f'LFC-PUR-{timestamp}',
                'date': datetime.now(),
                'total_amount': total_amount,
                'payment_method': payment_method,
                'notes': notes,
                'created_by': request.user
            }

            purchase = Purchase.objects.create(**purchase_data)

            # Create purchase items and update inventory using processed data
            for item in processed_items:
                product = item['product']
                supplier = item['supplier']

                # Create purchase item with the actual unit cost and box purchase info
                purchase_item = PurchaseItem.objects.create(
                    purchase=purchase,
                    product=product,
                    supplier=supplier,  # Store the supplier for this item
                    quantity=item['actual_quantity'],  # Store the actual unit quantity
                    cost_price=item['unit_cost'],     # Store the unit cost
                    is_box_purchase=item.get('is_box_purchase', False),  # Store whether this was a box purchase
                    box_quantity=product.box_quantity,  # Store the box quantity for reference
                    original_quantity=item.get('original_quantity', item['actual_quantity']),  # Store original quantity entered
                    stored_box_cost=item.get('box_cost', 0)  # Store the box cost directly
                )



                # Update product quantity with the actual unit quantity
                product.quantity += item['actual_quantity']

                # Check if product was out of stock before adding new inventory
                was_out_of_stock = (product.quantity - item['actual_quantity']) <= 0

                product.save()

                # If product was out of stock and auto-deactivated, but now has stock, show a message
                if was_out_of_stock and product.quantity > 0 and not product.is_active:
                    messages.info(request, f"Product '{product.name}' was automatically deactivated when it went out of stock. You may want to reactivate it now.")

                # Log the purchase for debugging
                print(f"Added {item['display_quantity']} of {product.name} at {item['unit_cost']} per unit")

            # Update funds
            meta = MetaData.objects.last()
            meta.funds -= total_amount
            meta.save()

            messages.success(request, "Purchase recorded successfully!")
            return redirect('product:purchases')

        except Exception as e:
            messages.error(request, f"Error recording purchase: {str(e)}")

    context = {
        'suppliers': suppliers,
        'products': products,
    }
    return render(request, 'product/purchase_form.html', context)

@login_required
@module_permission_required(module='purchase', required_level='full')
def delete_purchase(request, pk):
    """
    Delete a purchase record and reverse its financial impact
    """
    purchase = get_object_or_404(Purchase, pk=pk)

    # Store information for success message
    transaction_id = purchase.trxId
    total_amount = purchase.total_amount
    created_by = purchase.created_by.username if purchase.created_by else "Unknown"

    try:
        # Reverse the financial impact by adding the amount back to funds
        meta = MetaData.objects.last()
        if meta:
            meta.funds += total_amount
            meta.save()

        # Delete the purchase (this will cascade delete all purchase items)
        purchase.delete()

        # Format amount for display
        formatted_amount = f"{total_amount:,}៛"

        messages.success(
            request,
            f"Purchase {transaction_id} deleted successfully! "
            f"Amount {formatted_amount} has been added back to gym funds. "
            f"(Originally created by: {created_by})"
        )

    except Exception as e:
        messages.error(request, f"Error deleting purchase {transaction_id}: {str(e)}")

    return redirect('product:purchases')



@login_required
@module_permission_required(module='pos', required_level='edit')
def pos(request):
    # Only show active products with stock in POS
    products = Product.objects.filter(quantity__gt=0, is_active=True).order_by('name')
    categories = Category.objects.all()

    # Handle sale form submission
    if request.method == "POST":
        try:
            # Debug POST data
            print("POST data:", request.POST)

            product_ids = request.POST.getlist("product_ids[]")
            quantities = request.POST.getlist("quantities[]")
            prices = request.POST.getlist("prices[]")
            payment_method = request.POST.get("payment_method", "cash")
            notes = request.POST.get("notes", "")

            # Debug extracted data in detail
            print("\n==== SALE FORM DATA ====")
            print("Product IDs:", product_ids)
            print("Quantities:", quantities)
            print("Prices:", prices)
            print("Payment Method:", payment_method)
            print("Notes:", notes)

            # Debug each product
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and prices[i]:
                    try:
                        product = Product.objects.get(id=product_ids[i])
                        print(f"Product {i+1}: {product.name} (ID: {product_ids[i]}) - Quantity: {quantities[i]} - Price: {prices[i]}")
                    except Product.DoesNotExist:
                        print(f"Product {i+1}: ID {product_ids[i]} not found")
            print("========================\n")

            # Calculate total amount
            total_amount = 0
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and prices[i]:
                    total_amount += float(quantities[i]) * float(prices[i])

            # Create sale record
            timestamp = int(datetime.now().timestamp())
            sale = Sale.objects.create(
                trxId=f'LFC-SALE-{timestamp}',
                total_amount=total_amount,
                payment_method=payment_method,
                sold_by=request.user,
                notes=notes
            )

            # Create sale items and update inventory
            for i in range(len(product_ids)):
                if product_ids[i] and quantities[i] and prices[i]:
                    try:
                        product = Product.objects.get(id=product_ids[i])
                        quantity = int(quantities[i])
                        price = float(prices[i])

                        # For products with box quantities, check if the quantity is a multiple of box_quantity
                        is_box_equivalent = False

                        # If the product has a box quantity > 1 and the quantity is a multiple, mark it as a box equivalent
                        if product.box_quantity > 1 and quantity % product.box_quantity == 0:
                            is_box_equivalent = True

                        # Create sale item
                        SaleItem.objects.create(
                            sale=sale,
                            product=product,
                            quantity=quantity,
                            price=price,
                            is_box_equivalent=is_box_equivalent,
                            box_quantity=product.box_quantity
                        )

                        # Update product quantity
                        print(f"BEFORE UPDATE: Product {product.name} - Current quantity: {product.quantity}")

                        # Use F() expression to avoid race conditions
                        from django.db.models import F
                        Product.objects.filter(id=product.id).update(quantity=F('quantity') - quantity)

                        # Refresh from database to get updated value
                        product.refresh_from_db()

                        print(f"AFTER UPDATE: Product {product.name} - New quantity: {product.quantity} (Deducted: {quantity})")
                    except Exception as item_error:
                        print(f"Error processing item {i}: {str(item_error)}")
                        messages.error(request, f"Error processing item: {str(item_error)}")

            # Update funds
            meta = MetaData.objects.last()
            meta.funds += total_amount
            meta.save()

            messages.success(request, "Sale completed successfully!")
            return redirect('product:pos')

        except Exception as e:
            messages.error(request, f"Error processing sale: {str(e)}")

    context = {
        'products': products,
        'categories': categories
    }
    return render(request, 'product/pos.html', context)



@login_required
@module_permission_required(module='product', required_level='view')
def categories(request):
    categories = Category.objects.all().order_by('name')

    # Handle new category form submission
    if request.method == "POST" and 'name' in request.POST:
        try:
            name = request.POST.get("name")
            description = request.POST.get("description", "")

            Category.objects.create(
                name=name,
                description=description
            )

            messages.success(request, f"Category {name} added successfully!")
            return redirect('product:categories')

        except Exception as e:
            messages.error(request, f"Error adding category: {str(e)}")

    context = {
        'categories': categories
    }
    return render(request, 'product/categories.html', context)

@login_required
@module_permission_required(module='product', required_level='edit')
def edit_category(request, pk):
    category = get_object_or_404(Category, pk=pk)

    if request.method == "POST":
        try:
            category.name = request.POST.get("name")
            category.description = request.POST.get("description", "")
            category.save()

            messages.success(request, f"Category {category.name} updated successfully!")
            return redirect('product:categories')

        except Exception as e:
            messages.error(request, f"Error updating category: {str(e)}")

    context = {
        'category': category
    }
    return render(request, 'product/edit_category.html', context)

@login_required
@module_permission_required(module='product', required_level='full')
def delete_category(request, pk):
    category = get_object_or_404(Category, pk=pk)
    name = category.name

    try:
        category.delete()
        messages.success(request, f"Category {name} deleted successfully!")
    except Exception as e:
        messages.error(request, f"Error deleting category: {str(e)}")

    return redirect('product:categories')

@login_required
@module_permission_required(module='product', required_level='view')
def suppliers(request):
    suppliers = Supplier.objects.all().order_by('name')

    context = {
        'suppliers': suppliers
    }
    return render(request, 'product/suppliers.html', context)

@login_required
@module_permission_required(module='product', required_level='edit')
def create_supplier(request):
    """
    Create a new supplier
    """
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            phone = request.POST.get("phone", "")
            telegram = request.POST.get("telegram", "")
            address = request.POST.get("address", "")
            note = request.POST.get("note", "")

            Supplier.objects.create(
                name=name,
                phone=phone,
                telegram=telegram,
                address=address,
                note=note
            )

            messages.success(request, f"Supplier {name} added successfully!")
            return redirect('product:suppliers')

        except Exception as e:
            messages.error(request, f"Error adding supplier: {str(e)}")

    return render(request, 'product/supplier_form.html')

@login_required
@module_permission_required(module='product', required_level='edit')
def edit_supplier(request, pk):
    supplier = get_object_or_404(Supplier, pk=pk)

    if request.method == "POST":
        try:
            supplier.name = request.POST.get("name")
            supplier.phone = request.POST.get("phone", "")
            supplier.telegram = request.POST.get("telegram", "")
            supplier.address = request.POST.get("address", "")
            supplier.note = request.POST.get("note", "")
            supplier.save()

            messages.success(request, f"Supplier {supplier.name} updated successfully!")
            return redirect('product:suppliers')

        except Exception as e:
            messages.error(request, f"Error updating supplier: {str(e)}")

    context = {
        'supplier': supplier
    }
    return render(request, 'product/supplier_form.html', context)

@login_required
@module_permission_required(module='product', required_level='full')
def delete_supplier(request, pk):
    supplier = get_object_or_404(Supplier, pk=pk)
    name = supplier.name

    try:
        supplier.delete()
        messages.success(request, f"Supplier {name} deleted successfully!")
    except Exception as e:
        messages.error(request, f"Error deleting supplier: {str(e)}")

    return redirect('product:suppliers')

@login_required
@module_permission_required(module='product', required_level='edit')
def create_category_ajax(request):
    """
    Create a new category via AJAX request
    """
    if request.method == "POST":
        try:
            name = request.POST.get("name")
            description = request.POST.get("description", "")

            if not name:
                return JsonResponse({
                    'success': False,
                    'message': 'Category name is required'
                }, status=400)

            # Create the category
            category = Category.objects.create(
                name=name,
                description=description
            )

            # Return success response with category data
            return JsonResponse({
                'success': True,
                'message': f"Category {name} added successfully!",
                'category': {
                    'id': category.id,
                    'name': category.name
                }
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f"Error adding category: {str(e)}"
            }, status=500)

    return JsonResponse({
        'success': False,
        'message': 'Invalid request method'
    }, status=405)