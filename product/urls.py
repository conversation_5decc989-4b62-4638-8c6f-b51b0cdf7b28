from django.urls import path
from . import views
from . import views_pos

app_name = "product"

urlpatterns = [
    path('', views.index, name='index'),
    path('create/', views.create_product, name='create_product'),
    path('edit/<int:pk>/', views.edit_product, name='edit_product'),
    path('delete/<int:pk>/', views.delete_product, name='delete_product'),
    path('bulk-action/', views.bulk_action, name='bulk_action'),
    path('export-csv/', views.export_products_csv, name='export_csv'),
    path('import-csv/', views.import_products, name='import_csv'),

    path('purchases/', views.purchases, name='purchases'),
    path('purchases/create/', views.create_purchase, name='create_purchase'),
    path('purchases/delete/<int:pk>/', views.delete_purchase, name='delete_purchase'),

    # POS System
    path('pos/', views_pos.pos, name='pos'),
    path('pos/get_products/', views_pos.get_products, name='get_products'),
    path('pos/print_receipt/<int:pk>/', views_pos.print_receipt, name='print_receipt'),
    path('pos/history/', views_pos.history, name='pos_history'),
    path('pos/history/delete/<int:pk>/', views_pos.delete_sale, name='delete_sale'),

    path('categories/', views.categories, name='categories'),
    path('categories/edit/<int:pk>/', views.edit_category, name='edit_category'),
    path('categories/delete/<int:pk>/', views.delete_category, name='delete_category'),
    path('categories/create-ajax/', views.create_category_ajax, name='create_category_ajax'),

    path('suppliers/', views.suppliers, name='suppliers'),
    path('suppliers/create/', views.create_supplier, name='create_supplier'),
    path('suppliers/edit/<int:pk>/', views.edit_supplier, name='edit_supplier'),
    path('suppliers/delete/<int:pk>/', views.delete_supplier, name='delete_supplier'),
]
