from django import template
from core.templatetags.currency_formatters import format_khr as format_khr_with_separators
from core.templatetags.currency_formatters import format_usd as format_usd_with_separators
from core.templatetags.currency_formatters import format_number

register = template.Library()

@register.filter
def format_khr(value):
    """
    Format a number as Cambodian Riel (KHR) currency with thousand separators
    Example: 10000 -> 10,000៛
    """
    return format_khr_with_separators(value)

@register.filter
def format_usd(value):
    """
    Format a number as USD currency with thousand separators
    Example: 10000 -> $10,000.00
    """
    return format_usd_with_separators(value)

@register.filter
def format_number_with_commas(value):
    """
    Format a number with thousand separators without any currency symbol
    Example: 10000 -> 10,000
    """
    return format_number(value)

@register.filter
def abs(value):
    """
    Return the absolute value of a number
    """
    if value is None:
        return 0

    return abs(value)
