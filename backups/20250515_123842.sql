-- MySQL dump 10.13  Distrib 9.2.0, for Win64 (x86_64)
--
-- Host: localhost    Database: legend_fitness
-- ------------------------------------------------------
-- Server version	9.2.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `attendance_attendance`
--

DROP TABLE IF EXISTS `attendance_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `attendance_attendance` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `date` datetime(6) DEFAULT NULL,
  `is_employee` tinyint(1) NOT NULL,
  `attendance` tinyint(1) NOT NULL,
  `employee_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `attendance_attendance_employee_id_63b4db5a_fk_user_user_id` (`employee_id`),
  CONSTRAINT `attendance_attendance_employee_id_63b4db5a_fk_user_user_id` FOREIGN KEY (`employee_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `attendance_attendance`
--

LOCK TABLES `attendance_attendance` WRITE;
/*!40000 ALTER TABLE `attendance_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `attendance_attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_group`
--

DROP TABLE IF EXISTS `auth_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(150) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group`
--

LOCK TABLES `auth_group` WRITE;
/*!40000 ALTER TABLE `auth_group` DISABLE KEYS */;
INSERT INTO `auth_group` VALUES (2,'admin'),(3,'cashier'),(1,'front_desk');
/*!40000 ALTER TABLE `auth_group` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_group_permissions`
--

DROP TABLE IF EXISTS `auth_group_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_group_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `group_id` int NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_group_permissions_group_id_permission_id_0cd325b0_uniq` (`group_id`,`permission_id`),
  KEY `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` (`permission_id`),
  CONSTRAINT `auth_group_permissio_permission_id_84c5c92e_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `auth_group_permissions_group_id_b120cbf9_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_group_permissions`
--

LOCK TABLES `auth_group_permissions` WRITE;
/*!40000 ALTER TABLE `auth_group_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `auth_group_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `auth_permission`
--

DROP TABLE IF EXISTS `auth_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `auth_permission` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `content_type_id` int NOT NULL,
  `codename` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `auth_permission_content_type_id_codename_01ab375a_uniq` (`content_type_id`,`codename`),
  CONSTRAINT `auth_permission_content_type_id_2f476e4b_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=221 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `auth_permission`
--

LOCK TABLES `auth_permission` WRITE;
/*!40000 ALTER TABLE `auth_permission` DISABLE KEYS */;
INSERT INTO `auth_permission` VALUES (1,'Can add log entry',1,'add_logentry'),(2,'Can change log entry',1,'change_logentry'),(3,'Can delete log entry',1,'delete_logentry'),(4,'Can view log entry',1,'view_logentry'),(5,'Can add permission',2,'add_permission'),(6,'Can change permission',2,'change_permission'),(7,'Can delete permission',2,'delete_permission'),(8,'Can view permission',2,'view_permission'),(9,'Can add group',3,'add_group'),(10,'Can change group',3,'change_group'),(11,'Can delete group',3,'delete_group'),(12,'Can view group',3,'view_group'),(13,'Can add content type',4,'add_contenttype'),(14,'Can change content type',4,'change_contenttype'),(15,'Can delete content type',4,'delete_contenttype'),(16,'Can view content type',4,'view_contenttype'),(17,'Can add session',5,'add_session'),(18,'Can change session',5,'change_session'),(19,'Can delete session',5,'delete_session'),(20,'Can view session',5,'view_session'),(21,'Can add package',6,'add_package'),(22,'Can change package',6,'change_package'),(23,'Can delete package',6,'delete_package'),(24,'Can view package',6,'view_package'),(25,'Can add member',7,'add_member'),(26,'Can change member',7,'change_member'),(27,'Can delete member',7,'delete_member'),(28,'Can view member',7,'view_member'),(29,'Can add meta data',8,'add_metadata'),(30,'Can change meta data',8,'change_metadata'),(31,'Can delete meta data',8,'delete_metadata'),(32,'Can view meta data',8,'view_metadata'),(33,'Can add user',9,'add_user'),(34,'Can change user',9,'change_user'),(35,'Can delete user',9,'delete_user'),(36,'Can view user',9,'view_user'),(37,'Can add coach',10,'add_coach'),(38,'Can change coach',10,'change_coach'),(39,'Can delete coach',10,'delete_coach'),(40,'Can view coach',10,'view_coach'),(41,'Can add coach session',11,'add_coachsession'),(42,'Can change coach session',11,'change_coachsession'),(43,'Can delete coach session',11,'delete_coachsession'),(44,'Can view coach session',11,'view_coachsession'),(45,'Can add equipment',12,'add_equipment'),(46,'Can change equipment',12,'change_equipment'),(47,'Can delete equipment',12,'delete_equipment'),(48,'Can view equipment',12,'view_equipment'),(49,'Can add equipment activity track',13,'add_equipmentactivitytrack'),(50,'Can change equipment activity track',13,'change_equipmentactivitytrack'),(51,'Can delete equipment activity track',13,'delete_equipmentactivitytrack'),(52,'Can view equipment activity track',13,'view_equipmentactivitytrack'),(53,'Can add equipment type',14,'add_equipmenttype'),(54,'Can change equipment type',14,'change_equipmenttype'),(55,'Can delete equipment type',14,'delete_equipmenttype'),(56,'Can view equipment type',14,'view_equipmenttype'),(57,'Can add debit',15,'add_debit'),(58,'Can change debit',15,'change_debit'),(59,'Can delete debit',15,'delete_debit'),(60,'Can view debit',15,'view_debit'),(61,'Can add bill',16,'add_bill'),(62,'Can change bill',16,'change_bill'),(63,'Can delete bill',16,'delete_bill'),(64,'Can view bill',16,'view_bill'),(65,'Can add credit',17,'add_credit'),(66,'Can change credit',17,'change_credit'),(67,'Can delete credit',17,'delete_credit'),(68,'Can view credit',17,'view_credit'),(69,'Can add payment',18,'add_payment'),(70,'Can change payment',18,'change_payment'),(71,'Can delete payment',18,'delete_payment'),(72,'Can view payment',18,'view_payment'),(73,'Can add schedule',19,'add_schedule'),(74,'Can change schedule',19,'change_schedule'),(75,'Can delete schedule',19,'delete_schedule'),(76,'Can view schedule',19,'view_schedule'),(77,'Can add attendance',20,'add_attendance'),(78,'Can change attendance',20,'change_attendance'),(79,'Can delete attendance',20,'delete_attendance'),(80,'Can view attendance',20,'view_attendance'),(81,'Can add category',21,'add_category'),(82,'Can change category',21,'change_category'),(83,'Can delete category',21,'delete_category'),(84,'Can view category',21,'view_category'),(85,'Can add purchase',22,'add_purchase'),(86,'Can change purchase',22,'change_purchase'),(87,'Can delete purchase',22,'delete_purchase'),(88,'Can view purchase',22,'view_purchase'),(89,'Can add purchase item',23,'add_purchaseitem'),(90,'Can change purchase item',23,'change_purchaseitem'),(91,'Can delete purchase item',23,'delete_purchaseitem'),(92,'Can view purchase item',23,'view_purchaseitem'),(93,'Can add sale',24,'add_sale'),(94,'Can change sale',24,'change_sale'),(95,'Can delete sale',24,'delete_sale'),(96,'Can view sale',24,'view_sale'),(97,'Can add sale item',25,'add_saleitem'),(98,'Can change sale item',25,'change_saleitem'),(99,'Can delete sale item',25,'delete_saleitem'),(100,'Can view sale item',25,'view_saleitem'),(101,'Can add supplier',26,'add_supplier'),(102,'Can change supplier',26,'change_supplier'),(103,'Can delete supplier',26,'delete_supplier'),(104,'Can view supplier',26,'view_supplier'),(105,'Can add product',27,'add_product'),(106,'Can change product',27,'change_product'),(107,'Can delete product',27,'delete_product'),(108,'Can view product',27,'view_product'),(109,'Can add product expiry',28,'add_productexpiry'),(110,'Can change product expiry',28,'change_productexpiry'),(111,'Can delete product expiry',28,'delete_productexpiry'),(112,'Can view product expiry',28,'view_productexpiry'),(113,'Can add pay per visit',29,'add_paypervisit'),(114,'Can change pay per visit',29,'change_paypervisit'),(115,'Can delete pay per visit',29,'delete_paypervisit'),(116,'Can view pay per visit',29,'view_paypervisit'),(117,'Can add Payroll Record',30,'add_payrollrecord'),(118,'Can change Payroll Record',30,'change_payrollrecord'),(119,'Can delete Payroll Record',30,'delete_payrollrecord'),(120,'Can view Payroll Record',30,'view_payrollrecord'),(121,'Can add Transaction',31,'add_transaction'),(122,'Can change Transaction',31,'change_transaction'),(123,'Can delete Transaction',31,'delete_transaction'),(124,'Can view Transaction',31,'view_transaction'),(125,'Can add coach certification',32,'add_coachcertification'),(126,'Can change coach certification',32,'change_coachcertification'),(127,'Can delete coach certification',32,'delete_coachcertification'),(128,'Can view coach certification',32,'view_coachcertification'),(129,'Can add coach class',33,'add_coachclass'),(130,'Can change coach class',33,'change_coachclass'),(131,'Can delete coach class',33,'delete_coachclass'),(132,'Can view coach class',33,'view_coachclass'),(133,'Can add Employee',34,'add_employee'),(134,'Can change Employee',34,'change_employee'),(135,'Can delete Employee',34,'delete_employee'),(136,'Can view Employee',34,'view_employee'),(137,'Can add pay per visit settings',35,'add_paypervisitsettings'),(138,'Can change pay per visit settings',35,'change_paypervisitsettings'),(139,'Can delete pay per visit settings',35,'delete_paypervisitsettings'),(140,'Can view pay per visit settings',35,'view_paypervisitsettings'),(141,'Can add Notification',36,'add_notification'),(142,'Can change Notification',36,'change_notification'),(143,'Can delete Notification',36,'delete_notification'),(144,'Can view Notification',36,'view_notification'),(145,'Can add salary payment',37,'add_salarypayment'),(146,'Can change salary payment',37,'change_salarypayment'),(147,'Can delete salary payment',37,'delete_salarypayment'),(148,'Can view salary payment',37,'view_salarypayment'),(149,'Can add salary payment',38,'add_salarypayment'),(150,'Can change salary payment',38,'change_salarypayment'),(151,'Can delete salary payment',38,'delete_salarypayment'),(152,'Can view salary payment',38,'view_salarypayment'),(153,'Can add slip template',39,'add_sliptemplate'),(154,'Can change slip template',39,'change_sliptemplate'),(155,'Can delete slip template',39,'delete_sliptemplate'),(156,'Can view slip template',39,'view_sliptemplate'),(157,'Can add payment template',40,'add_paymenttemplate'),(158,'Can change payment template',40,'change_paymenttemplate'),(159,'Can delete payment template',40,'delete_paymenttemplate'),(160,'Can view payment template',40,'view_paymenttemplate'),(161,'Can add Utility Provider',41,'add_utilityprovider'),(162,'Can change Utility Provider',41,'change_utilityprovider'),(163,'Can delete Utility Provider',41,'delete_utilityprovider'),(164,'Can view Utility Provider',41,'view_utilityprovider'),(165,'Can add Utility Bill',42,'add_utilitybill'),(166,'Can change Utility Bill',42,'change_utilitybill'),(167,'Can delete Utility Bill',42,'delete_utilitybill'),(168,'Can view Utility Bill',42,'view_utilitybill'),(169,'Can add Utility Bill Category',43,'add_utilitybillcategory'),(170,'Can change Utility Bill Category',43,'change_utilitybillcategory'),(171,'Can delete Utility Bill Category',43,'delete_utilitybillcategory'),(172,'Can view Utility Bill Category',43,'view_utilitybillcategory'),(173,'Can add Utility Payment',44,'add_utilitypayment'),(174,'Can change Utility Payment',44,'change_utilitypayment'),(175,'Can delete Utility Payment',44,'delete_utilitypayment'),(176,'Can view Utility Payment',44,'view_utilitypayment'),(177,'Can add Utility Category',45,'add_utilitycategory'),(178,'Can change Utility Category',45,'change_utilitycategory'),(179,'Can delete Utility Category',45,'delete_utilitycategory'),(180,'Can view Utility Category',45,'view_utilitycategory'),(181,'Can add Bill Receipt Template',46,'add_billreceipttemplate'),(182,'Can change Bill Receipt Template',46,'change_billreceipttemplate'),(183,'Can delete Bill Receipt Template',46,'delete_billreceipttemplate'),(184,'Can view Bill Receipt Template',46,'view_billreceipttemplate'),(185,'Can add Bill',47,'add_bill'),(186,'Can change Bill',47,'change_bill'),(187,'Can delete Bill',47,'delete_bill'),(188,'Can view Bill',47,'view_bill'),(189,'Can add Report Template',48,'add_reporttemplate'),(190,'Can change Report Template',48,'change_reporttemplate'),(191,'Can delete Report Template',48,'delete_reporttemplate'),(192,'Can view Report Template',48,'view_reporttemplate'),(193,'Can add Financial Metric',49,'add_financialmetric'),(194,'Can change Financial Metric',49,'change_financialmetric'),(195,'Can delete Financial Metric',49,'delete_financialmetric'),(196,'Can view Financial Metric',49,'view_financialmetric'),(197,'Can add Budget',50,'add_budget'),(198,'Can change Budget',50,'change_budget'),(199,'Can delete Budget',50,'delete_budget'),(200,'Can view Budget',50,'view_budget'),(201,'Can add Budget Item',51,'add_budgetitem'),(202,'Can change Budget Item',51,'change_budgetitem'),(203,'Can delete Budget Item',51,'delete_budgetitem'),(204,'Can view Budget Item',51,'view_budgetitem'),(205,'Can add Metric Value',52,'add_metricvalue'),(206,'Can change Metric Value',52,'change_metricvalue'),(207,'Can delete Metric Value',52,'delete_metricvalue'),(208,'Can view Metric Value',52,'view_metricvalue'),(209,'Can add discount',53,'add_discount'),(210,'Can change discount',53,'change_discount'),(211,'Can delete discount',53,'delete_discount'),(212,'Can view discount',53,'view_discount'),(213,'Can add promotion',54,'add_promotion'),(214,'Can change promotion',54,'change_promotion'),(215,'Can delete promotion',54,'delete_promotion'),(216,'Can view promotion',54,'view_promotion'),(217,'Can add Settings',55,'add_settings'),(218,'Can change Settings',55,'change_settings'),(219,'Can delete Settings',55,'delete_settings'),(220,'Can view Settings',55,'view_settings');
/*!40000 ALTER TABLE `auth_permission` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `billmanagement_bill`
--

DROP TABLE IF EXISTS `billmanagement_bill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `billmanagement_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `bill_id` varchar(30) NOT NULL,
  `category` varchar(20) NOT NULL,
  `provider` varchar(100) NOT NULL,
  `description` longtext,
  `month_year` date NOT NULL,
  `payment_period` int NOT NULL,
  `amount_khr` int NOT NULL,
  `amount_usd` decimal(10,2) DEFAULT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `payment_date` datetime(6) DEFAULT NULL,
  `notes` longtext,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `paid_by_id` bigint DEFAULT NULL,
  `is_recurring` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bill_id` (`bill_id`),
  KEY `billmanagement_bill_paid_by_id_941087e9_fk_user_user_id` (`paid_by_id`),
  CONSTRAINT `billmanagement_bill_paid_by_id_941087e9_fk_user_user_id` FOREIGN KEY (`paid_by_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `billmanagement_bill`
--

LOCK TABLES `billmanagement_bill` WRITE;
/*!40000 ALTER TABLE `billmanagement_bill` DISABLE KEYS */;
/*!40000 ALTER TABLE `billmanagement_bill` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `billmanagement_billreceipttemplate`
--

DROP TABLE IF EXISTS `billmanagement_billreceipttemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `billmanagement_billreceipttemplate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_default` tinyint(1) NOT NULL,
  `language` varchar(10) NOT NULL,
  `header_text` varchar(200) NOT NULL,
  `subheader_text` varchar(200) NOT NULL,
  `footer_text` varchar(200) NOT NULL,
  `company_logo` varchar(100) DEFAULT NULL,
  `background_color` varchar(20) NOT NULL,
  `text_color` varchar(20) NOT NULL,
  `accent_color` varchar(20) NOT NULL,
  `show_company_info` tinyint(1) NOT NULL,
  `show_signatures` tinyint(1) NOT NULL,
  `custom_css` longtext,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `billmanagement_billreceipttemplate`
--

LOCK TABLES `billmanagement_billreceipttemplate` WRITE;
/*!40000 ALTER TABLE `billmanagement_billreceipttemplate` DISABLE KEYS */;
INSERT INTO `billmanagement_billreceipttemplate` VALUES (1,'Default Template',1,'en','LEGEND FITNESS','Bill Payment Receipt','Thank you for your business!','','#ffffff','#000000','#0066cc',1,1,NULL,'2025-05-09 23:05:14.768604','2025-05-09 23:05:14.768604');
/*!40000 ALTER TABLE `billmanagement_billreceipttemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `coaches_coach`
--

DROP TABLE IF EXISTS `coaches_coach`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coaches_coach` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `coach_id` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `specialization` varchar(50) NOT NULL,
  `employment_type` varchar(20) NOT NULL,
  `salary` decimal(10,2) NOT NULL,
  `contact` varchar(20) NOT NULL,
  `assigned_classes` longtext,
  `gender` varchar(10) NOT NULL,
  `join_date` date NOT NULL,
  `status` tinyint(1) NOT NULL,
  `due` decimal(10,2) NOT NULL,
  `schedule` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `coach_id` (`coach_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coaches_coach`
--

LOCK TABLES `coaches_coach` WRITE;
/*!40000 ALTER TABLE `coaches_coach` DISABLE KEYS */;
INSERT INTO `coaches_coach` VALUES (1,'C-101','Vannak Sok','weight_training','full_time',800000.00,'011 223 344','Morning HIIT, Evening Strength','male','2025-04-26',1,81600000.00,NULL),(2,'C-102','Srey Oun','yoga','part_time',400000.00,'012 334 455','Evening Yoga, Weekend Meditation','female','2025-04-26',1,40800000.00,NULL),(3,'C-103','Rithy Chhay','boxing','full_time',850000.00,'010 445 566','Boxing Basics, Advanced Boxing','male','2025-04-26',1,86700000.00,NULL),(4,'C-104','Kunthea Mao','cardio','part_time',450000.00,'099 556 677','Morning Cardio, Weekend HIIT','female','2025-04-26',1,45900000.00,NULL),(6,'C-105','Huy','general','full_time',400000.00,'09786756543','','male','2025-05-01',1,28800000.00,NULL);
/*!40000 ALTER TABLE `coaches_coach` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `coaches_coachsession`
--

DROP TABLE IF EXISTS `coaches_coachsession`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `coaches_coachsession` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `start_time` datetime(6) NOT NULL,
  `end_time` datetime(6) NOT NULL,
  `notes` longtext,
  `fee` decimal(10,2) NOT NULL,
  `coach_id` bigint NOT NULL,
  `member_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `coaches_coachsession_coach_id_73ac1eff_fk_coaches_coach_id` (`coach_id`),
  KEY `coaches_coachsession_member_id_b3cb6c14_fk_members_member_id` (`member_id`),
  CONSTRAINT `coaches_coachsession_coach_id_73ac1eff_fk_coaches_coach_id` FOREIGN KEY (`coach_id`) REFERENCES `coaches_coach` (`id`),
  CONSTRAINT `coaches_coachsession_member_id_b3cb6c14_fk_members_member_id` FOREIGN KEY (`member_id`) REFERENCES `members_member` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `coaches_coachsession`
--

LOCK TABLES `coaches_coachsession` WRITE;
/*!40000 ALTER TABLE `coaches_coachsession` DISABLE KEYS */;
/*!40000 ALTER TABLE `coaches_coachsession` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_admin_log`
--

DROP TABLE IF EXISTS `django_admin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_admin_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `action_time` datetime(6) NOT NULL,
  `object_id` longtext,
  `object_repr` varchar(200) NOT NULL,
  `action_flag` smallint unsigned NOT NULL,
  `change_message` longtext NOT NULL,
  `content_type_id` int DEFAULT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `django_admin_log_content_type_id_c4bce8eb_fk_django_co` (`content_type_id`),
  KEY `django_admin_log_user_id_c564eba6_fk_user_user_id` (`user_id`),
  CONSTRAINT `django_admin_log_content_type_id_c4bce8eb_fk_django_co` FOREIGN KEY (`content_type_id`) REFERENCES `django_content_type` (`id`),
  CONSTRAINT `django_admin_log_user_id_c564eba6_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`),
  CONSTRAINT `django_admin_log_chk_1` CHECK ((`action_flag` >= 0))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_admin_log`
--

LOCK TABLES `django_admin_log` WRITE;
/*!40000 ALTER TABLE `django_admin_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `django_admin_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_content_type`
--

DROP TABLE IF EXISTS `django_content_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_content_type` (
  `id` int NOT NULL AUTO_INCREMENT,
  `app_label` varchar(100) NOT NULL,
  `model` varchar(100) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `django_content_type_app_label_model_76bd3d3b_uniq` (`app_label`,`model`)
) ENGINE=InnoDB AUTO_INCREMENT=56 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_content_type`
--

LOCK TABLES `django_content_type` WRITE;
/*!40000 ALTER TABLE `django_content_type` DISABLE KEYS */;
INSERT INTO `django_content_type` VALUES (1,'admin','logentry'),(20,'attendance','attendance'),(3,'auth','group'),(2,'auth','permission'),(47,'billmanagement','bill'),(46,'billmanagement','billreceipttemplate'),(10,'coaches','coach'),(32,'coaches','coachcertification'),(33,'coaches','coachclass'),(11,'coaches','coachsession'),(4,'contenttypes','contenttype'),(34,'employee','employee'),(12,'equipment','equipment'),(13,'equipment','equipmentactivitytrack'),(14,'equipment','equipmenttype'),(50,'financialreport','budget'),(51,'financialreport','budgetitem'),(49,'financialreport','financialmetric'),(52,'financialreport','metricvalue'),(48,'financialreport','reporttemplate'),(7,'members','member'),(6,'members','package'),(36,'notification','notification'),(18,'payment','payment'),(40,'payment','paymenttemplate'),(29,'paypervisit','paypervisit'),(35,'paypervisit','paypervisitsettings'),(38,'payroll','salarypayment'),(39,'payroll','sliptemplate'),(21,'product','category'),(53,'product','discount'),(27,'product','product'),(28,'product','productexpiry'),(54,'product','promotion'),(22,'product','purchase'),(23,'product','purchaseitem'),(24,'product','sale'),(25,'product','saleitem'),(26,'product','supplier'),(19,'schedule','schedule'),(5,'sessions','session'),(55,'settings','settings'),(16,'transaction','bill'),(17,'transaction','credit'),(15,'transaction','debit'),(30,'transaction','payrollrecord'),(31,'transaction','transaction'),(8,'user','metadata'),(37,'user','salarypayment'),(9,'user','user'),(42,'utilitybill','utilitybill'),(43,'utilitybill','utilitybillcategory'),(45,'utilitybill','utilitycategory'),(44,'utilitybill','utilitypayment'),(41,'utilitybill','utilityprovider');
/*!40000 ALTER TABLE `django_content_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_migrations`
--

DROP TABLE IF EXISTS `django_migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_migrations` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `app` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `applied` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_migrations`
--

LOCK TABLES `django_migrations` WRITE;
/*!40000 ALTER TABLE `django_migrations` DISABLE KEYS */;
INSERT INTO `django_migrations` VALUES (1,'schedule','0001_initial','2025-04-26 03:54:24.128451'),(2,'contenttypes','0001_initial','2025-04-26 03:54:24.157693'),(3,'contenttypes','0002_remove_content_type_name','2025-04-26 03:54:24.211990'),(4,'auth','0001_initial','2025-04-26 03:54:24.387093'),(5,'auth','0002_alter_permission_name_max_length','2025-04-26 03:54:24.424451'),(6,'auth','0003_alter_user_email_max_length','2025-04-26 03:54:24.428012'),(7,'auth','0004_alter_user_username_opts','2025-04-26 03:54:24.433597'),(8,'auth','0005_alter_user_last_login_null','2025-04-26 03:54:24.438342'),(9,'auth','0006_require_contenttypes_0002','2025-04-26 03:54:24.438652'),(10,'auth','0007_alter_validators_add_error_messages','2025-04-26 03:54:24.443954'),(11,'auth','0008_alter_user_username_max_length','2025-04-26 03:54:24.447847'),(12,'auth','0009_alter_user_last_name_max_length','2025-04-26 03:54:24.452973'),(13,'auth','0010_alter_group_name_max_length','2025-04-26 03:54:24.461734'),(14,'auth','0011_update_proxy_permissions','2025-04-26 03:54:24.468611'),(15,'auth','0012_alter_user_first_name_max_length','2025-04-26 03:54:24.472471'),(16,'user','0001_initial','2025-04-26 03:54:24.722564'),(17,'admin','0001_initial','2025-04-26 03:54:24.801542'),(18,'admin','0002_logentry_remove_auto_add','2025-04-26 03:54:24.804669'),(19,'admin','0003_logentry_add_action_flag_choices','2025-04-26 03:54:24.814163'),(20,'members','0001_initial','2025-04-26 03:54:24.893855'),(21,'coaches','0001_initial','2025-04-26 03:54:24.986023'),(22,'attendance','0001_initial','2025-04-26 03:54:24.989725'),(23,'attendance','0002_initial','2025-04-26 03:54:25.030846'),(24,'attendance','0003_initial','2025-04-26 03:54:25.070175'),(27,'payment','0001_initial','2025-04-26 03:54:25.370519'),(28,'payment','0002_initial','2025-04-26 03:54:25.468696'),(29,'paypervisit','0001_initial','2025-04-26 03:54:25.489131'),(30,'paypervisit','0002_initial','2025-04-26 03:54:25.535325'),(31,'product','0001_initial','2025-04-26 03:54:25.695199'),(32,'product','0002_initial','2025-04-26 03:54:26.007222'),(33,'sessions','0001_initial','2025-04-26 03:54:26.030286'),(34,'transaction','0001_initial','2025-04-26 03:54:26.145803'),(35,'transaction','0002_initial','2025-04-26 03:54:26.349889'),(36,'coaches','0002_coach_due','2025-04-26 04:03:03.877533'),(37,'members','0002_member_status','2025-04-26 04:03:03.940302'),(38,'product','0003_alter_product_box_cost_alter_product_cost_price_and_more','2025-04-26 05:27:20.567716'),(39,'transaction','0003_remove_credit_coach_remove_credit_employee_and_more','2025-04-26 22:10:31.217059'),(40,'payment','0003_alter_payment_invoice_no','2025-04-30 21:54:12.408812'),(41,'coaches','0003_remove_coach_schedule_coach_address_and_more','2025-05-01 20:42:15.107615'),(42,'members','0003_alter_member_payment_status','2025-05-01 20:42:15.108337'),(43,'user','0002_alter_metadata_funds_alter_user_due_and_more','2025-05-03 10:16:04.300641'),(44,'coaches','0004_add_schedule_field_back','2025-05-04 11:59:14.455845'),(45,'user','0003_user_role','2025-05-04 13:16:56.951534'),(46,'employee','0001_initial','2025-05-04 13:20:16.712654'),(47,'employee','0002_migrate_data_from_user','2025-05-04 13:48:48.737792'),(48,'employee','0003_alter_employee_options_and_more','2025-05-04 14:06:06.546162'),(55,'transaction','0004_cleanup','2025-05-04 21:41:32.000000'),(56,'paypervisit','0003_paypervisitsettings','2025-05-06 23:14:59.617151'),(57,'product','0002_product_is_active','2025-05-06 23:15:06.587971'),(58,'product','0004_merge_20250506_2255','2025-05-06 23:15:06.590149'),(59,'product','0005_product_is_active','2025-05-06 23:17:05.887767'),(60,'transaction','0005_fix_coach_field','2025-05-06 23:17:23.783052'),(61,'user','0003_metadata_auto_deactivate_out_of_stock_metadata_default_items_per_page','2025-05-06 23:32:26.865110'),(62,'user','0004_alter_user_managers_and_more','2025-05-06 23:48:51.555228'),(63,'notification','0001_initial','2025-05-07 00:00:12.215832'),(64,'product','0006_product_barcode_image_product_qrcode_image','2025-05-07 00:17:26.198655'),(65,'product','0007_remove_barcode_fields','2025-05-08 21:16:28.053431'),(66,'core','0001_remove_notification_table','2025-05-08 21:26:29.294817'),(67,'core','0002_remove_equipment_tables','2025-05-08 21:38:07.000000'),(68,'transaction','0006_remove_equipment_category','2025-05-08 21:38:07.000000'),(69,'transaction','0007_fix_equipment_reference','2025-05-08 21:41:04.000000'),(70,'core','0003_remove_transaction_tables','2025-05-08 21:52:46.000000'),(71,'core','0004_remove_report_tables','2025-05-08 22:00:12.000000'),(72,'core','0005_remove_schedule_tables','2025-05-08 22:10:05.000000'),(73,'user','0002_remove_user_schedules','2025-05-08 22:10:05.000000'),(74,'payroll','0001_initial','2025-05-08 23:48:44.697231'),(75,'user','0005_merge_20250508_2334','2025-05-08 23:48:44.698746'),(76,'user','0006_salarypayment','2025-05-08 23:48:44.864952'),(77,'user','0007_fix_migration_issues','2025-05-08 23:48:44.872150'),(78,'payroll','0002_sliptemplate','2025-05-09 00:22:23.984245'),(79,'payment','0004_paymenttemplate','2025-05-09 01:02:15.556318'),(80,'paypervisit','0004_paypervisitsettings_price_for_10_and_more','2025-05-09 02:39:03.907248'),(81,'paypervisit','0005_paypervisitsettings_custom_price_1_and_more','2025-05-09 02:47:35.382324'),(82,'user','0008_remove_metadata_notification_email_and_more','2025-05-09 16:57:48.205611'),(85,'core','0005_remove_utilitybill_tables','2025-05-09 17:29:14.897890'),(86,'core','0006_merge_20250509_1728','2025-05-09 17:29:14.898953'),(88,'utilitybill','0001_initial','2025-05-09 21:00:12.077027'),(89,'core','0006_remove_utilitybill_tables','2025-05-09 22:16:10.866450'),(90,'core','0007_merge_20250509_2215','2025-05-09 22:16:10.866450'),(91,'billmanagement','0001_initial','2025-05-09 22:54:55.673899'),(92,'billmanagement','0002_bill_is_recurring','2025-05-09 23:16:59.506191'),(93,'financialreport','0001_initial','2025-05-10 15:00:33.947252'),(95,'product','0008_product_discount_type_product_discount_value_and_more','2025-05-13 00:50:59.963592'),(96,'product','0009_remove_promotion_categories_and_more','2025-05-13 01:14:34.176957'),(97,'members','0004_remove_package_freeze_policy','2025-05-13 20:39:41.007646'),(98,'product','0010_remove_supplier_contact_person_remove_supplier_email_and_more','2025-05-13 20:50:47.418591'),(99,'members','0005_member_address_member_discount_member_dob_and_more','2025-05-14 21:31:30.501821'),(100,'settings','0001_initial','2025-05-14 22:20:48.620578'),(101,'settings','0002_migrate_existing_settings','2025-05-14 22:21:15.954827');
/*!40000 ALTER TABLE `django_migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `django_session`
--

DROP TABLE IF EXISTS `django_session`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `django_session` (
  `session_key` varchar(40) NOT NULL,
  `session_data` longtext NOT NULL,
  `expire_date` datetime(6) NOT NULL,
  PRIMARY KEY (`session_key`),
  KEY `django_session_expire_date_a5c62663` (`expire_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `django_session`
--

LOCK TABLES `django_session` WRITE;
/*!40000 ALTER TABLE `django_session` DISABLE KEYS */;
INSERT INTO `django_session` VALUES ('jdll2jq1hylrdmxl9wayj7wdcrkhxn4o','.eJxVjEEOwiAQRe_C2hCKbRlcuu8ZyMDMSNXQpLQr492VpAvd_vfef6mA-5bDXnkNM6mL6tTpd4uYHlwaoDuW26LTUrZ1jrop-qBVTwvx83q4fwcZa261twxCBkB6SJ1nY80QBd0wGvDWnj36MXEiQi9fKAQikMgZ7h33Rr0_7xQ4gQ:1uDeyt:0HmMR4JLFCFI8l9jV-IodGbNoGFM9-upH7asMoh2BQQ','2025-05-24 14:50:47.243132'),('nb4ap6fznibr095v53hngiln0491jmgm','.eJxVjrsOwjAMRf8lM4rS0IfDyN5viNzYbktLI_UxIf6dRHSAxcM99x75pTwe--CPjVc_krqpQl1-sw7DxEsG9MCljzrEZV_HTueKPumm20g838_un2DAbchrZxmEDICUEArHxpqqE2yq2oCz9urQ1YEDETpJUAhEIFBjuGy4NEn6fcDP6R7Yc5JOT_X-AGBcQF0:1uFGOR:dU_Qs5a_u1kqTsxKNSGZD_7RHvMc1uTLkhA2WtU5te8','2025-05-29 00:59:47.384168'),('p7q33xbf5mhlsy0161fsoirgj902d5e8','.eJxVjEEOwiAQRe_C2hCKbRlcuu8ZyMDMSNXQpLQr492VpAvd_vfef6mA-5bDXnkNM6mL6tTpd4uYHlwaoDuW26LTUrZ1jrop-qBVTwvx83q4fwcZa261twxCBkB6SJ1nY80QBd0wGvDWnj36MXEiQi9fKAQikMgZ7h33Rr0_7xQ4gQ:1uFF9s:vmOf9G2LWuBGmRwvwWESXf-qWF5LkGQ5uMupNRtG22c','2025-05-28 23:40:40.900776'),('pj9m2nx4snhq41bpar3b5swkyrkcicpi','.eJxVjMsOwiAQRf-FtSHADEhduu83kBlgpGrapI-V8d-1SRe6veec-1KJtrWlbalzGoq6KKdOvxtTftRxB-VO423SeRrXeWC9K_qgi-6nUp_Xw_07aLS0b41gEHyJUqwHqBFIGMRKphAzRnZoBfEc2VRkYqEYgKFzocudJ8Pq_QHgnTf-:1uB8CC:3i1nMBFwJnSg7O4ZG6GNNqpyzbRVCUsq-Cy2eH_-Nio','2025-05-17 15:26:04.558758'),('tk0bmrrpgdihchgo3vatpmdhivtzqdrb','.eJxVjMsOwiAQRf-FtSHADEhduu83kBlgpGrapI-V8d-1SRe6veec-1KJtrWlbalzGoq6KKdOvxtTftRxB-VO423SeRrXeWC9K_qgi-6nUp_Xw_07aLS0b41gEHyJUqwHqBFIGMRKphAzRnZoBfEc2VRkYqEYgKFzocudJ8Pq_QHgnTf-:1uB7tM:zeg0CczUeAX0kSgkmG6A5yy89iH-ZHO4Vrlpb8e79NQ','2025-05-17 15:06:36.075068'),('wnowufm16edcqpion7q8vn2rpqvxnidi','.eJxVjDEOgzAQBP_iOrKMA_icMj1vsA7fHZAgIwGuovw9WKGh2WJndj-KXpiGJcxHZhxYPRQndVMB8z6GvPEaJjrK6tr1GN-cCvjvdVzSvk69Loo-6aa7hXh-nu7lYMRtLGtvGYQMgNQQK8_GmqYXdE1rwFt79-jbyJEIvRxQCEQgkjNcO66N-v4Ak-FAWA:1uFGGn:SDVfUXEZyPIVX2xAXRw4q2vuKfeBNjbROvq3RUbZh38','2025-05-29 00:51:53.375029');
/*!40000 ALTER TABLE `django_session` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employee_employee`
--

DROP TABLE IF EXISTS `employee_employee`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employee_employee` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `emp_id` varchar(20) NOT NULL,
  `name` varchar(100) NOT NULL,
  `gender` varchar(10) NOT NULL,
  `dob` date DEFAULT NULL,
  `phone` varchar(20) NOT NULL,
  `role` varchar(20) NOT NULL,
  `salary` int NOT NULL,
  `join_date` date NOT NULL,
  `address` longtext,
  `nid` varchar(20) DEFAULT NULL,
  `due` int NOT NULL,
  `status` tinyint(1) NOT NULL,
  `user_id` bigint DEFAULT NULL,
  `work_schedule` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `emp_id` (`emp_id`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `user_id` (`user_id`),
  CONSTRAINT `employee_employee_user_id_2dd26fdc_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_employee`
--

LOCK TABLES `employee_employee` WRITE;
/*!40000 ALTER TABLE `employee_employee` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_employee` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `employee_employee_schedules`
--

DROP TABLE IF EXISTS `employee_employee_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `employee_employee_schedules` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `employee_id` bigint NOT NULL,
  `schedule_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `employee_employee_schedu_employee_id_schedule_id_bb7c75fc_uniq` (`employee_id`,`schedule_id`),
  KEY `employee_employee_sc_schedule_id_0762f909_fk_schedule_` (`schedule_id`),
  CONSTRAINT `employee_employee_sc_employee_id_c3f89dfe_fk_employee_` FOREIGN KEY (`employee_id`) REFERENCES `employee_employee` (`id`),
  CONSTRAINT `employee_employee_sc_schedule_id_0762f909_fk_schedule_` FOREIGN KEY (`schedule_id`) REFERENCES `schedule_schedule` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `employee_employee_schedules`
--

LOCK TABLES `employee_employee_schedules` WRITE;
/*!40000 ALTER TABLE `employee_employee_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `employee_employee_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `financialreport_reporttemplate`
--

DROP TABLE IF EXISTS `financialreport_reporttemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `financialreport_reporttemplate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `template_type` varchar(20) NOT NULL,
  `is_default` tinyint(1) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `header_color` varchar(20) NOT NULL,
  `text_color` varchar(20) NOT NULL,
  `accent_color` varchar(20) NOT NULL,
  `background_color` varchar(20) NOT NULL,
  `table_header_color` varchar(20) NOT NULL,
  `show_logo` tinyint(1) NOT NULL,
  `show_footer` tinyint(1) NOT NULL,
  `footer_text` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `financialreport_reportte_is_default_template_type_32423152_uniq` (`is_default`,`template_type`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `financialreport_reporttemplate`
--

LOCK TABLES `financialreport_reporttemplate` WRITE;
/*!40000 ALTER TABLE `financialreport_reporttemplate` DISABLE KEYS */;
INSERT INTO `financialreport_reporttemplate` VALUES (2,'Default Balance Template','balance',1,'2025-05-10 15:36:36.673495','2025-05-10 15:36:36.673495','#2563eb','#333333','#2563eb','#ffffff','#f8f9fa',1,1,'Legend Fitness Club'),(3,'Test','income',1,'2025-05-10 15:37:16.817024','2025-05-10 15:37:16.817024','#2563eb','#333333','#2563eb','#ffffff','#f8f9fa',1,1,'Legend Fitness Club'),(4,'test','balance',0,'2025-05-10 16:04:52.821375','2025-05-10 16:04:52.821375','#16a34a','#000000','#16a34a','#ffffff','#007dfa',1,1,'Legend Fitness Club'),(5,'x','expense',1,'2025-05-10 16:05:25.605308','2025-05-10 16:05:25.605308','#e11d48','#333333','#e11d48','#ffffff','#f8f9fa',1,1,'Legend Fitness Club');
/*!40000 ALTER TABLE `financialreport_reporttemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_member`
--

DROP TABLE IF EXISTS `members_member`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_member` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `member_id` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `photo` varchar(100) DEFAULT NULL,
  `contact` varchar(20) NOT NULL,
  `telegram` varchar(100) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `health_notes` longtext,
  `emergency_contact` varchar(20) DEFAULT NULL,
  `blood_type` varchar(3) DEFAULT NULL,
  `gender` varchar(10) NOT NULL,
  `due_payment` int NOT NULL,
  `package_id` bigint DEFAULT NULL,
  `status` tinyint(1) NOT NULL,
  `address` longtext,
  `discount` int NOT NULL,
  `dob` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_id` (`member_id`),
  KEY `members_member_package_id_edaa1b6d_fk_members_package_id` (`package_id`),
  CONSTRAINT `members_member_package_id_edaa1b6d_fk_members_package_id` FOREIGN KEY (`package_id`) REFERENCES `members_package` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_member`
--

LOCK TABLES `members_member` WRITE;
/*!40000 ALTER TABLE `members_member` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_member` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members_package`
--

DROP TABLE IF EXISTS `members_package`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members_package` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `package_id` varchar(10) NOT NULL,
  `name` varchar(100) NOT NULL,
  `duration` int NOT NULL,
  `price_khr` int NOT NULL,
  `price_usd` decimal(10,2) DEFAULT NULL,
  `access_type` varchar(50) NOT NULL,
  `description` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `package_id` (`package_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members_package`
--

LOCK TABLES `members_package` WRITE;
/*!40000 ALTER TABLE `members_package` DISABLE KEYS */;
/*!40000 ALTER TABLE `members_package` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_payment`
--

DROP TABLE IF EXISTS `payment_payment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_payment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `invoice_no` varchar(30) NOT NULL,
  `amount_khr` int NOT NULL,
  `amount_usd` decimal(10,2) DEFAULT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_date` datetime(6) NOT NULL,
  `notes` longtext,
  `collector_id` bigint DEFAULT NULL,
  `member_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `invoice_no` (`invoice_no`),
  KEY `payment_payment_collector_id_427386f4_fk_user_user_id` (`collector_id`),
  KEY `payment_payment_member_id_ce5c4d60_fk_members_member_id` (`member_id`),
  CONSTRAINT `payment_payment_collector_id_427386f4_fk_user_user_id` FOREIGN KEY (`collector_id`) REFERENCES `user_user` (`id`),
  CONSTRAINT `payment_payment_member_id_ce5c4d60_fk_members_member_id` FOREIGN KEY (`member_id`) REFERENCES `members_member` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_payment`
--

LOCK TABLES `payment_payment` WRITE;
/*!40000 ALTER TABLE `payment_payment` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_payment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payment_paymenttemplate`
--

DROP TABLE IF EXISTS `payment_paymenttemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payment_paymenttemplate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_default` tinyint(1) NOT NULL,
  `language` varchar(10) NOT NULL,
  `header_text` varchar(200) NOT NULL,
  `subheader_text` varchar(200) NOT NULL,
  `footer_text` varchar(200) NOT NULL,
  `company_logo` varchar(100) DEFAULT NULL,
  `background_color` varchar(20) NOT NULL,
  `text_color` varchar(20) NOT NULL,
  `accent_color` varchar(20) NOT NULL,
  `show_company_info` tinyint(1) NOT NULL,
  `show_signatures` tinyint(1) NOT NULL,
  `custom_css` longtext,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payment_paymenttemplate`
--

LOCK TABLES `payment_paymenttemplate` WRITE;
/*!40000 ALTER TABLE `payment_paymenttemplate` DISABLE KEYS */;
/*!40000 ALTER TABLE `payment_paymenttemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `paypervisit_paypervisit`
--

DROP TABLE IF EXISTS `paypervisit_paypervisit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `paypervisit_paypervisit` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trxId` varchar(50) NOT NULL,
  `amount` int NOT NULL,
  `num_people` int NOT NULL,
  `date` datetime(6) NOT NULL,
  `received_by_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trxId` (`trxId`),
  KEY `paypervisit_paypervisit_received_by_id_a6de007e_fk_user_user_id` (`received_by_id`),
  CONSTRAINT `paypervisit_paypervisit_received_by_id_a6de007e_fk_user_user_id` FOREIGN KEY (`received_by_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `paypervisit_paypervisit`
--

LOCK TABLES `paypervisit_paypervisit` WRITE;
/*!40000 ALTER TABLE `paypervisit_paypervisit` DISABLE KEYS */;
INSERT INTO `paypervisit_paypervisit` VALUES (36,'LFC-PPV-20250515-faf93c05',20000,5,'2025-05-15 00:18:32.911750',1),(37,'LFC-PPV-20250515-f98a6c0b',4000,1,'2025-05-15 00:19:21.097301',1);
/*!40000 ALTER TABLE `paypervisit_paypervisit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `paypervisit_paypervisitsettings`
--

DROP TABLE IF EXISTS `paypervisit_paypervisitsettings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `paypervisit_paypervisitsettings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `price_per_person` int NOT NULL DEFAULT '4000',
  `last_updated` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `price_for_10` int NOT NULL,
  `price_for_2` int NOT NULL,
  `price_for_5` int NOT NULL,
  `custom_price_1` int NOT NULL,
  `custom_price_2` int NOT NULL,
  `custom_price_3` int NOT NULL,
  `quick_select_1` int NOT NULL,
  `quick_select_2` int NOT NULL,
  `quick_select_3` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `paypervisit_paypervisitsettings`
--

LOCK TABLES `paypervisit_paypervisitsettings` WRITE;
/*!40000 ALTER TABLE `paypervisit_paypervisitsettings` DISABLE KEYS */;
INSERT INTO `paypervisit_paypervisitsettings` VALUES (1,4000,'2025-05-09 02:49:15',40000,8000,20000,8000,20000,40000,2,5,10);
/*!40000 ALTER TABLE `paypervisit_paypervisitsettings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payroll_salarypayment`
--

DROP TABLE IF EXISTS `payroll_salarypayment`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_salarypayment` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payroll_id` varchar(30) NOT NULL,
  `month` date NOT NULL,
  `base_salary` int NOT NULL,
  `bonus` int NOT NULL,
  `deduction` int NOT NULL,
  `overtime_hours` int NOT NULL,
  `overtime_rate` int NOT NULL,
  `final_pay` int NOT NULL,
  `payment_date` datetime(6) DEFAULT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `employment_type` varchar(20) NOT NULL,
  `notes` longtext,
  `employee_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payroll_id` (`payroll_id`),
  KEY `payroll_salarypayment_employee_id_d146dcf6_fk_user_user_id` (`employee_id`),
  CONSTRAINT `payroll_salarypayment_employee_id_d146dcf6_fk_user_user_id` FOREIGN KEY (`employee_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payroll_salarypayment`
--

LOCK TABLES `payroll_salarypayment` WRITE;
/*!40000 ALTER TABLE `payroll_salarypayment` DISABLE KEYS */;
/*!40000 ALTER TABLE `payroll_salarypayment` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `payroll_sliptemplate`
--

DROP TABLE IF EXISTS `payroll_sliptemplate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `payroll_sliptemplate` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `is_default` tinyint(1) NOT NULL,
  `language` varchar(10) NOT NULL,
  `header_text` varchar(200) NOT NULL,
  `subheader_text` varchar(200) NOT NULL,
  `footer_text` varchar(200) NOT NULL,
  `company_logo` varchar(100) DEFAULT NULL,
  `background_color` varchar(20) NOT NULL,
  `text_color` varchar(20) NOT NULL,
  `accent_color` varchar(20) NOT NULL,
  `show_company_info` tinyint(1) NOT NULL,
  `show_signatures` tinyint(1) NOT NULL,
  `custom_css` longtext,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `payroll_sliptemplate`
--

LOCK TABLES `payroll_sliptemplate` WRITE;
/*!40000 ALTER TABLE `payroll_sliptemplate` DISABLE KEYS */;
INSERT INTO `payroll_sliptemplate` VALUES (1,'test',1,'km','LEGEND FITNESS','Salary Slip','Thank you for your service!','slip_templates/Cambodia_Water.jpg','#ffffff','#000000','#0c4a6e',1,1,'','2025-05-09 00:25:20.971594','2025-05-09 00:25:20.976402');
/*!40000 ALTER TABLE `payroll_sliptemplate` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_category`
--

DROP TABLE IF EXISTS `product_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_category` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` longtext,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_category`
--

LOCK TABLES `product_category` WRITE;
/*!40000 ALTER TABLE `product_category` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_category` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_product`
--

DROP TABLE IF EXISTS `product_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_product` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `sku` varchar(20) NOT NULL,
  `description` longtext,
  `image` varchar(100) DEFAULT NULL,
  `cost_price` int NOT NULL,
  `retail_price` int NOT NULL,
  `quantity` int NOT NULL,
  `low_stock_threshold` int NOT NULL,
  `box_quantity` int NOT NULL,
  `box_cost` int DEFAULT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `category_id` bigint DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`),
  KEY `product_product_category_id_0c725779_fk_product_category_id` (`category_id`),
  CONSTRAINT `product_product_category_id_0c725779_fk_product_category_id` FOREIGN KEY (`category_id`) REFERENCES `product_category` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_product`
--

LOCK TABLES `product_product` WRITE;
/*!40000 ALTER TABLE `product_product` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_product` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_productexpiry`
--

DROP TABLE IF EXISTS `product_productexpiry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_productexpiry` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `batch_number` varchar(50) DEFAULT NULL,
  `quantity` int NOT NULL,
  `expiry_date` date NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `product_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_productexpiry_product_id_711b4c3e_fk_product_product_id` (`product_id`),
  CONSTRAINT `product_productexpiry_product_id_711b4c3e_fk_product_product_id` FOREIGN KEY (`product_id`) REFERENCES `product_product` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_productexpiry`
--

LOCK TABLES `product_productexpiry` WRITE;
/*!40000 ALTER TABLE `product_productexpiry` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_productexpiry` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_purchase`
--

DROP TABLE IF EXISTS `product_purchase`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_purchase` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trxId` varchar(50) NOT NULL,
  `date` datetime(6) NOT NULL,
  `total_amount` int NOT NULL,
  `notes` longtext,
  `created_at` datetime(6) NOT NULL,
  `created_by_id` bigint DEFAULT NULL,
  `supplier_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trxId` (`trxId`),
  KEY `product_purchase_created_by_id_8c70d51d_fk_user_user_id` (`created_by_id`),
  KEY `product_purchase_supplier_id_72c71fe7_fk_product_supplier_id` (`supplier_id`),
  CONSTRAINT `product_purchase_created_by_id_8c70d51d_fk_user_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_user` (`id`),
  CONSTRAINT `product_purchase_supplier_id_72c71fe7_fk_product_supplier_id` FOREIGN KEY (`supplier_id`) REFERENCES `product_supplier` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_purchase`
--

LOCK TABLES `product_purchase` WRITE;
/*!40000 ALTER TABLE `product_purchase` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_purchase` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_purchaseitem`
--

DROP TABLE IF EXISTS `product_purchaseitem`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_purchaseitem` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `quantity` int NOT NULL,
  `cost_price` int NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `batch_number` varchar(50) DEFAULT NULL,
  `is_box_purchase` tinyint(1) NOT NULL,
  `box_quantity` int NOT NULL,
  `original_quantity` int NOT NULL,
  `stored_box_cost` int NOT NULL,
  `product_id` bigint NOT NULL,
  `purchase_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_purchaseitem_product_id_4cbda6d1_fk_product_product_id` (`product_id`),
  KEY `product_purchaseitem_purchase_id_7d344dda_fk_product_purchase_id` (`purchase_id`),
  CONSTRAINT `product_purchaseitem_product_id_4cbda6d1_fk_product_product_id` FOREIGN KEY (`product_id`) REFERENCES `product_product` (`id`),
  CONSTRAINT `product_purchaseitem_purchase_id_7d344dda_fk_product_purchase_id` FOREIGN KEY (`purchase_id`) REFERENCES `product_purchase` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_purchaseitem`
--

LOCK TABLES `product_purchaseitem` WRITE;
/*!40000 ALTER TABLE `product_purchaseitem` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_purchaseitem` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_sale`
--

DROP TABLE IF EXISTS `product_sale`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_sale` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `trxId` varchar(50) NOT NULL,
  `date` datetime(6) NOT NULL,
  `total_amount` int NOT NULL,
  `payment_method` varchar(20) NOT NULL,
  `notes` longtext,
  `sold_by_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `trxId` (`trxId`),
  KEY `product_sale_sold_by_id_80a57d1b_fk_user_user_id` (`sold_by_id`),
  CONSTRAINT `product_sale_sold_by_id_80a57d1b_fk_user_user_id` FOREIGN KEY (`sold_by_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_sale`
--

LOCK TABLES `product_sale` WRITE;
/*!40000 ALTER TABLE `product_sale` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_sale` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_saleitem`
--

DROP TABLE IF EXISTS `product_saleitem`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_saleitem` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `quantity` int NOT NULL,
  `price` int NOT NULL,
  `is_box_equivalent` tinyint(1) NOT NULL,
  `box_quantity` int NOT NULL,
  `product_id` bigint NOT NULL,
  `sale_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  KEY `product_saleitem_product_id_5cfb6595_fk_product_product_id` (`product_id`),
  KEY `product_saleitem_sale_id_faa579e5_fk_product_sale_id` (`sale_id`),
  CONSTRAINT `product_saleitem_product_id_5cfb6595_fk_product_product_id` FOREIGN KEY (`product_id`) REFERENCES `product_product` (`id`),
  CONSTRAINT `product_saleitem_sale_id_faa579e5_fk_product_sale_id` FOREIGN KEY (`sale_id`) REFERENCES `product_sale` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_saleitem`
--

LOCK TABLES `product_saleitem` WRITE;
/*!40000 ALTER TABLE `product_saleitem` DISABLE KEYS */;
/*!40000 ALTER TABLE `product_saleitem` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_supplier`
--

DROP TABLE IF EXISTS `product_supplier`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `product_supplier` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `address` longtext,
  `note` longtext,
  `telegram` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_supplier`
--

LOCK TABLES `product_supplier` WRITE;
/*!40000 ALTER TABLE `product_supplier` DISABLE KEYS */;
INSERT INTO `product_supplier` VALUES (4,'Pisey','087644','bt','bb','87653');
/*!40000 ALTER TABLE `product_supplier` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `schedule_schedule`
--

DROP TABLE IF EXISTS `schedule_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `schedule_schedule` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL,
  `day` varchar(10) DEFAULT NULL,
  `start_time` time(6) DEFAULT NULL,
  `end_time` time(6) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `schedule_schedule`
--

LOCK TABLES `schedule_schedule` WRITE;
/*!40000 ALTER TABLE `schedule_schedule` DISABLE KEYS */;
INSERT INTO `schedule_schedule` VALUES (1,'stuff','Monday','07:00:00.000000','13:00:00.000000'),(2,'stuff','Tuesday','07:00:00.000000','13:00:00.000000');
/*!40000 ALTER TABLE `schedule_schedule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings_settings`
--

DROP TABLE IF EXISTS `settings_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings_settings` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `gym_name` varchar(100) NOT NULL,
  `contact_email` varchar(254) DEFAULT NULL,
  `contact_phone` varchar(20) DEFAULT NULL,
  `address` longtext,
  `auto_deactivate_out_of_stock` tinyint(1) NOT NULL,
  `auto_reactivate_in_stock` tinyint(1) NOT NULL,
  `default_items_per_page` int NOT NULL,
  `paypervisit_price_per_person` int NOT NULL,
  `paypervisit_quick_select_1` int NOT NULL,
  `paypervisit_quick_select_2` int NOT NULL,
  `paypervisit_quick_select_3` int NOT NULL,
  `paypervisit_custom_price_1` int NOT NULL,
  `paypervisit_custom_price_2` int NOT NULL,
  `paypervisit_custom_price_3` int NOT NULL,
  `notification_success_color` varchar(20) NOT NULL,
  `notification_error_color` varchar(20) NOT NULL,
  `notification_warning_color` varchar(20) NOT NULL,
  `notification_info_color` varchar(20) NOT NULL,
  `notification_text_color` varchar(20) NOT NULL,
  `default_currency` varchar(10) NOT NULL,
  `last_backup_date` datetime(6) DEFAULT NULL,
  `last_data_cleanup` datetime(6) DEFAULT NULL,
  `funds` int NOT NULL,
  `last_checked` date DEFAULT NULL,
  `last_updated` datetime(6) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings_settings`
--

LOCK TABLES `settings_settings` WRITE;
/*!40000 ALTER TABLE `settings_settings` DISABLE KEYS */;
INSERT INTO `settings_settings` VALUES (1,'Legend Fitness',NULL,NULL,NULL,1,1,10,4000,2,5,10,8000,20000,40000,'#058509','#F44336','#FF9800','#2196F3','#FFFFFF','KHR','2025-05-14 23:23:46.306770','2025-05-14 23:59:05.897029',251262003,'2025-05-03','2025-05-15 12:37:38.030481','2025-05-14 22:21:15.950924');
/*!40000 ALTER TABLE `settings_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_payrollrecord`
--

DROP TABLE IF EXISTS `transaction_payrollrecord`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_payrollrecord` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `payroll_id` varchar(50) NOT NULL,
  `amount` int NOT NULL,
  `payment_date` datetime(6) NOT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `period_start` date DEFAULT NULL,
  `period_end` date DEFAULT NULL,
  `notes` longtext,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint DEFAULT NULL,
  `staff_id` bigint DEFAULT NULL,
  `transaction_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `payroll_id` (`payroll_id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `transaction_payrollrecord_created_by_id_b2db313f_fk_user_user_id` (`created_by_id`),
  KEY `transaction_payrollrecord_staff_id_beb2aaf2_fk_user_user_id` (`staff_id`),
  CONSTRAINT `transaction_payrollr_transaction_id_afa3956a_fk_transacti` FOREIGN KEY (`transaction_id`) REFERENCES `transaction_transaction` (`id`),
  CONSTRAINT `transaction_payrollrecord_created_by_id_b2db313f_fk_user_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_user` (`id`),
  CONSTRAINT `transaction_payrollrecord_staff_id_beb2aaf2_fk_user_user_id` FOREIGN KEY (`staff_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_payrollrecord`
--

LOCK TABLES `transaction_payrollrecord` WRITE;
/*!40000 ALTER TABLE `transaction_payrollrecord` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_payrollrecord` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transaction_transaction`
--

DROP TABLE IF EXISTS `transaction_transaction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `transaction_transaction` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `transaction_id` varchar(50) NOT NULL,
  `type` varchar(20) NOT NULL,
  `category` varchar(50) NOT NULL,
  `amount` int NOT NULL,
  `description` longtext,
  `date` datetime(6) NOT NULL,
  `payment_method` varchar(20) NOT NULL,
  `payment_status` varchar(20) NOT NULL,
  `created_at` datetime(6) NOT NULL,
  `updated_at` datetime(6) NOT NULL,
  `created_by_id` bigint DEFAULT NULL,
  `member_id` bigint DEFAULT NULL,
  `product_id` bigint DEFAULT NULL,
  `staff_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `transaction_transaction_created_by_id_d77c6f05_fk_user_user_id` (`created_by_id`),
  KEY `transaction_transaction_member_id_88714b0b_fk_members_member_id` (`member_id`),
  KEY `transaction_transact_product_id_b6aba92a_fk_product_p` (`product_id`),
  KEY `transaction_transaction_staff_id_ddf51918_fk_user_user_id` (`staff_id`),
  CONSTRAINT `transaction_transact_product_id_b6aba92a_fk_product_p` FOREIGN KEY (`product_id`) REFERENCES `product_product` (`id`),
  CONSTRAINT `transaction_transaction_created_by_id_d77c6f05_fk_user_user_id` FOREIGN KEY (`created_by_id`) REFERENCES `user_user` (`id`),
  CONSTRAINT `transaction_transaction_member_id_88714b0b_fk_members_member_id` FOREIGN KEY (`member_id`) REFERENCES `members_member` (`id`),
  CONSTRAINT `transaction_transaction_staff_id_ddf51918_fk_user_user_id` FOREIGN KEY (`staff_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=218 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transaction_transaction`
--

LOCK TABLES `transaction_transaction` WRITE;
/*!40000 ALTER TABLE `transaction_transaction` DISABLE KEYS */;
/*!40000 ALTER TABLE `transaction_transaction` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_metadata`
--

DROP TABLE IF EXISTS `user_metadata`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_metadata` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `lastChecked` date DEFAULT NULL,
  `funds` int DEFAULT NULL,
  `auto_deactivate_out_of_stock` tinyint(1) NOT NULL,
  `default_items_per_page` int NOT NULL,
  `auto_reactivate_in_stock` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_metadata`
--

LOCK TABLES `user_metadata` WRITE;
/*!40000 ALTER TABLE `user_metadata` DISABLE KEYS */;
INSERT INTO `user_metadata` VALUES (1,'2025-05-03',24000,1,10,1);
/*!40000 ALTER TABLE `user_metadata` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_user`
--

DROP TABLE IF EXISTS `user_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_user` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `password` varchar(128) NOT NULL,
  `last_login` datetime(6) DEFAULT NULL,
  `is_superuser` tinyint(1) NOT NULL,
  `username` varchar(150) NOT NULL,
  `first_name` varchar(150) NOT NULL,
  `last_name` varchar(150) NOT NULL,
  `email` varchar(254) NOT NULL,
  `is_staff` tinyint(1) NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `date_joined` datetime(6) NOT NULL,
  `phone` varchar(14) DEFAULT NULL,
  `name` varchar(50) DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `emp_id` varchar(20) DEFAULT NULL,
  `nid` varchar(20) DEFAULT NULL,
  `is_manager` tinyint(1) NOT NULL,
  `is_employee` tinyint(1) NOT NULL,
  `address` longtext,
  `salary` int DEFAULT NULL,
  `gender` varchar(50) DEFAULT NULL,
  `due` int NOT NULL,
  `join_date` date DEFAULT NULL,
  `status` tinyint(1) NOT NULL,
  `role` varchar(20) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `phone` (`phone`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_user`
--

LOCK TABLES `user_user` WRITE;
/*!40000 ALTER TABLE `user_user` DISABLE KEYS */;
INSERT INTO `user_user` VALUES (1,'pbkdf2_sha256$720000$MPElidf0UTxogQqBftLogx$ZIbAPo9TYL8LBkt8qa0/pmKqWFcqBSU9ban7C/BR7+g=','2025-05-15 00:59:34.360358',1,'admin','','','<EMAIL>',1,1,'2025-04-26 03:56:08.757521',NULL,'Administrator',NULL,NULL,NULL,1,1,NULL,NULL,NULL,0,'2025-05-05',1,'admin',NULL),(12,'pbkdf2_sha256$720000$xVgUH3qGbo5A1RXuPTCCjU$RMRJ0Kkr/09T/rsIKpBxab1volo+7seuK/Objt/p1Fg=','2025-05-05 21:53:14.986082',1,'Admin1','','','<EMAIL>',1,1,'2025-05-05 21:45:32.982379',NULL,'Administrator',NULL,NULL,NULL,1,1,NULL,NULL,NULL,0,'2025-05-05',1,'admin',NULL);
/*!40000 ALTER TABLE `user_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_user_groups`
--

DROP TABLE IF EXISTS `user_user_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_user_groups` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `group_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_user_groups_user_id_group_id_bb60391f_uniq` (`user_id`,`group_id`),
  KEY `user_user_groups_group_id_c57f13c0_fk_auth_group_id` (`group_id`),
  CONSTRAINT `user_user_groups_group_id_c57f13c0_fk_auth_group_id` FOREIGN KEY (`group_id`) REFERENCES `auth_group` (`id`),
  CONSTRAINT `user_user_groups_user_id_13f9a20d_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_user_groups`
--

LOCK TABLES `user_user_groups` WRITE;
/*!40000 ALTER TABLE `user_user_groups` DISABLE KEYS */;
INSERT INTO `user_user_groups` VALUES (4,1,2);
/*!40000 ALTER TABLE `user_user_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_user_schedules`
--

DROP TABLE IF EXISTS `user_user_schedules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_user_schedules` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `schedule_id` bigint NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_user_schedules_user_id_schedule_id_692af209_uniq` (`user_id`,`schedule_id`),
  KEY `user_user_schedules_schedule_id_04b48987_fk_schedule_schedule_id` (`schedule_id`),
  CONSTRAINT `user_user_schedules_schedule_id_04b48987_fk_schedule_schedule_id` FOREIGN KEY (`schedule_id`) REFERENCES `schedule_schedule` (`id`),
  CONSTRAINT `user_user_schedules_user_id_63641094_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_user_schedules`
--

LOCK TABLES `user_user_schedules` WRITE;
/*!40000 ALTER TABLE `user_user_schedules` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_user_schedules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_user_user_permissions`
--

DROP TABLE IF EXISTS `user_user_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_user_user_permissions` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL,
  `permission_id` int NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_user_user_permissions_user_id_permission_id_64f4d5b8_uniq` (`user_id`,`permission_id`),
  KEY `user_user_user_permi_permission_id_ce49d4de_fk_auth_perm` (`permission_id`),
  CONSTRAINT `user_user_user_permi_permission_id_ce49d4de_fk_auth_perm` FOREIGN KEY (`permission_id`) REFERENCES `auth_permission` (`id`),
  CONSTRAINT `user_user_user_permissions_user_id_31782f58_fk_user_user_id` FOREIGN KEY (`user_id`) REFERENCES `user_user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_user_user_permissions`
--

LOCK TABLES `user_user_user_permissions` WRITE;
/*!40000 ALTER TABLE `user_user_user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_user_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-15 12:38:42
